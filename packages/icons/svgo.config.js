// @ts-check

module.exports = {
  plugins: [
    'convertStyleToAttrs',
    {
      name: 'preset-default',
      params: {
        overrides: {
          convertColors: {
            // preserve subway or firework colors
            currentColor:
              /^(?!(?:none|white|#(?:00A751|F33B41|FC8427|FFD407|FFD759|275DC5|E8104D))).+$/i,
          },
        },
      },
    },
    'prefixIds',
    {
      name: 'removeAttrs',
      params: {
        attrs: ['data-.+'],
      },
    },
  ],
};
