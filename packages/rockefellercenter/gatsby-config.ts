import dotenv from 'dotenv';
import { z } from 'zod';

import type { GatsbyConfig } from 'gatsby';

dotenv.config({
  path: `.env.${process.env.NODE_ENV ?? 'development'}`,
});

const netlifyEnv = z
  .union([
    z.object({
      CONTEXT: z.string(),
      DEPLOY_PRIME_URL: z.string(),
      NETLIFY: z.string(),
      URL: z.string(),
    }),
    z.object({
      NETLIFY: z.undefined(),
    }),
  ])
  .parse(process.env);

const env = z
  .object({
    GTM_ID: z.string().optional(),
    GTM_AUTH: z.string().optional(),
    GTM_PREVIEW: z.string().optional(),
    GTM_ENV: z.enum(['development', 'production']),
    NODE_ENV: z.enum(['development', 'production']),
    SANITY_DATASET: z.string(),
    SANITY_PROJECT_ID: z.string(),
    SANITY_TOKEN: z.string(),
    SENTRY_DSN: z.string().optional(),
    SITE_URL: z
      .string()
      .default(
        netlifyEnv.NETLIFY
          ? netlifyEnv.CONTEXT === 'production'
            ? netlifyEnv.URL
            : netlifyEnv.DEPLOY_PRIME_URL
          : 'http://localhost:8000/',
      ),
    ROBOTS_POLICY: z.enum(['production', 'development']).optional(),
  })
  .parse(process.env);

const config: GatsbyConfig = {
  siteMetadata: {
    title: 'Rockefeller Center',
    description: 'Rockefeller Center',
    author: 'tishman',
    siteUrl: env.SITE_URL,
  },
  // More easily incorporate content into your pages through automatic TypeScript type generation and better GraphQL IntelliSense.
  // If you use VSCode you can also use the GraphQL plugin
  // Learn more at: https://gatsby.dev/graphql-typegen
  graphqlTypegen: {
    generateOnBuild: true,
    typesOutputPath: './src/__generated__/gatsby-types.d.ts',
  },
  plugins: [
    {
      resolve: 'gatsby-plugin-env-variables',
      options: {
        // expose these envs to use with ./src/environment.ts
        allowList: [
          'CURATOR_API_KEY',
          'FILM_PHOTO_AUDIENCE_ID',
          'GOOGLE_API_KEY',
          'GOOGLE_OPTIMIZE_RAINBOW_ROOM_EXPERIMENT_ID',
          'GOOGLE_OPTIMIZE_VIP_EXPERIMENT_ID',
          'GOOGLE_SEARCH_KEY',
          'MAILCHIMP_API_KEY',
          'MAILCHIMP_AUDIENCE_ID',
          'MAILCHIMP_URL',
          'SANITY_DATASET',
          'SANITY_PROJECT_ID',
          'SANITY_TOKEN',
          'SENDGRID_API_KEY',
          'SENDGRID_DEV_FROM',
          'SENDGRID_DEV_TO',
          'SENTRY_DSN',
          'TRIPLESEAT_PUBLIC_KEY',
          'SHIFT4_TOR_ACCESS_TOKEN',
          'SHIFT4_RINK_ACCESS_TOKEN',
          'SHIFT4_I4GO_URL',
          'CF_TURNSTILE_SITEKEY',
          'CF_TURNSTILE_SECRET',
          'MONGODB_CONNECTION',
          'MONGODB_DB',
          'MONGODB_COLLECTION',
          'KOUNT_MID',
          'KOUNT_KHASH_KEY',
          'KOUNT_ENV',
          'KOUNT_API_URL',
          'KOUNT_CLIENT_ID',
          'KOUNT_API_KEY',
          'KOUNT_AUTH_URL',
          'VIVA_CLIENT_BASE_URL',
          'DISABLE_MONGODB_CACHING',
          'GTM_ENV',
          'MAILCHIMP_SKYLIFT_AUDIENCE_ID',
          'ENABLE_NEW_RELIC_LOGGING',
          'NEW_RELIC_LICENSE_KEY',
          'FIREBASE_API_KEY',
          'FIREBASE_AUTH_DOMAIN',
          'FIREBASE_PROJECT_ID',
          'FIREBASE_STORAGE_BUCKET',
          'FIREBASE_MESSAGING_SENDER_ID',
          'FIREBASE_APP_ID',
          'FIREBASE_MEASUREMENT_ID',
          'ENV_ALL_IN_PASS_ADULT_PEAK_AK',
          'ENV_ALL_IN_PASS_CHILD_PEAK_AK',
          'ENV_ALL_IN_PASS_SENIOR_PEAK_AK',
          'ENV_BUNNY_PHOTO_PRODUCT_AK',
          'ENV_FIVE_ACRES_PRODUCT_AK',
          'ENV_ON_THE_ROCKS_EVENT_AK',
        ],
      },
    },
    {
      resolve: 'gatsby-plugin-advanced-sitemap-v5',
      options: {
        query: `
        {
          allSitePage {
            edges {
              node {
                id
                path
              }
            }
          }
          businesses:allSanityBusiness {
            edges {
              node {
                archive
                category {
                  category
                }
                id
                titleAndSlug {
                  slug {
                    current
                  }
                }
              }
            }
          },
          events:allSanityEvent {
            edges {
              node {
                archive
                id
                titleAndSlug {
                  slug {
                    current
                  }
                }
              }
            }
          }
        }`,
        mapping: {
          allSitePage: {
            sitemap: 'pages',
            serializer: (edges: Queries.SitePageEdge[]) => {
              return edges
                .filter(({ node }: { node: Queries.SitePage }) => {
                  // Exclude paths that match /shops/*, /dine/*, /events/* or /amenities/* but not /shop, /dine, /events, or /amenities
                  return !/^\/(shops|dine|events|amenities)\/.+/.test(
                    node.path,
                  );
                })
                .map(({ node }: { node: Queries.SitePage }) => {
                  return {
                    node: {
                      id: node.id,
                      slug: node.path,
                      url: node.path,
                    },
                  };
                });
            },
          },
          businesses: {
            sitemap: 'businesses',
            serializer: (edges: Queries.SanityBusinessEdge[]) => {
              return edges
                .filter(
                  ({ node }: { node: Queries.SanityBusiness }) => !node.archive,
                )
                .map(({ node }: { node: Queries.SanityBusiness }) => {
                  const slug = `/${
                    node.category?.category === 'shop'
                      ? node.category?.category + 's'
                      : node.category?.category === 'amenity'
                      ? 'amenities'
                      : node.category?.category
                  }/${node.titleAndSlug?.slug?.current}`;
                  return {
                    node: {
                      id: node.id,
                      slug,
                      url: slug,
                    },
                  };
                });
            },
          },
          events: {
            sitemap: 'events',
            serializer: (edges: Queries.SanityEventEdge[]) => {
              return edges
                .filter(
                  ({ node }: { node: Queries.SanityEvent }) => !node.archive,
                )
                .map(({ node }: { node: Queries.SanityEvent }) => {
                  const slug = `/events/${node.titleAndSlug?.slug?.current}`;
                  return {
                    node: {
                      id: node.id,
                      slug,
                      url: slug,
                    },
                  };
                });
            },
          },
        },
        exclude: [
          '/404',
          '/404.html',
          '/dine/hombre-taco/',
          '/events/the-flag-project-2020/',
          '/events/flag-project-2022/',
          '/events/masa-at-rockefeller-center/',
          '/events/jeppe-heins-changing-spaces-at-rockefeller-center/',
          '/events/dine-at-radio-park/',
          '/events/indieplaza-at-rockefeller-center/',
          '/events/mexico-week-dia-de-muertos-at-rockefeller-center/',
          '/buy-tickets/confirmation/',
          '/de-de/tickets-kaufen/bestätigung/',
          '/es-us/comprar-boletos/confirmación/',
          '/pt-br/comprar-ingressos/confirmação/',
          '/fr-fr/acheter-des-billets/confirmation/',
          '/ja-jp/チケットを買う/確認/',
          '/zh-cn/买票/确认/',
          '/zh-tw/購票/確認/',
          '/it-it/comprare-biglietti/conferma/',
          '/buy-tickets/refunds/',
          '/de-de/tickets-kaufen/erstattungen/',
          '/es-us/comprar-boletos/reembolsos/',
          '/pt-br/comprar-ingressos/reembolsos/',
          '/fr-fr/acheter-des-billets/remboursements/',
          '/ko-kr/티켓-구매하기/확인',
          '/ko-kr/티켓-구매하기/환불/',
          '/ja-jp/チケットを買う/返金/',
          '/zh-cn/买票/refunds/',
          '/zh-tw/買票/退款/',
          '/it-it/comprare-biglietti/rimborsi/',
          '/maps/north',
          '/maps/south',
          '/maps/west-north',
          '/maps/west-south',
        ],
      },
    },
    {
      resolve: 'gatsby-plugin-google-tagmanager',
      options: {
        id: env.GTM_ID,
        defaultDataLayer: () => ({
          originalLocation:
            document.location.protocol +
            '//' +
            document.location.hostname +
            document.location.pathname +
            document.location.search,
          platform: 'gatsby',
          project: 'tishman',
        }),
        includeInDevelopment: false,
        ...(env.GTM_AUTH && env.GTM_PREVIEW && env.GTM_ENV === 'production'
          ? {
              gtmAuth: env.GTM_AUTH,
              gtmPreview: env.GTM_PREVIEW,
            }
          : {}),
      },
    },
    {
      resolve: 'gatsby-source-sanity',
      options: {
        dataset: env.SANITY_DATASET,
        overlayDrafts: false,
        projectId: env.SANITY_PROJECT_ID,
        token: env.SANITY_TOKEN,
        watchMode: env.NODE_ENV !== 'production',
      },
    },
    'gatsby-plugin-image',
    'gatsby-plugin-sharp',
    'gatsby-transformer-sharp',
    'gatsby-transformer-json',
    'gatsby-plugin-theme-ui',
    'gatsby-plugin-sitemap',
    {
      resolve: 'gatsby-plugin-manifest',
      options: {
        icon: 'src/images/favicon.svg',
      },
    },
    {
      resolve: 'gatsby-plugin-netlify',
      options: {
        mergeCachingHeaders: false,
      },
    },
    'gatsby-plugin-react-helmet',
    {
      resolve: 'gatsby-plugin-robots-txt',
      options: {
        host: env.SITE_URL,
        sitemap: `${env.SITE_URL}/sitemap.xml`,
        policy: [{ userAgent: '*', allow: '/' }],
        env: {
          development: {
            policy:
              'CONTEXT' in netlifyEnv &&
              (netlifyEnv.CONTEXT === 'release' ||
                env.ROBOTS_POLICY === 'production')
                ? [{ userAgent: '*', allow: '/' }]
                : [{ userAgent: '*', disallow: ['/'] }],
          },
          production: {
            policy: [{ userAgent: '*', allow: '/' }],
          },
        },
        resolveEnv: () =>
          env.SITE_URL.includes('netlify.app') &&
          !('CONTEXT' in netlifyEnv && netlifyEnv.CONTEXT === 'release')
            ? 'development'
            : 'production',
      },
    },
    {
      resolve: '@sentry/gatsby',
    },
    {
      resolve: 'gatsby-source-filesystem',
      options: {
        name: 'images',
        path: './src/images/',
      },
      __key: 'images',
    },
    {
      resolve: 'gatsby-source-filesystem',
      options: {
        name: 'pages',
        path: './src/pages/',
      },
      __key: 'pages',
    },
    {
      resolve: 'gatsby-source-filesystem',
      options: {
        name: 'data',
        path: `./src/data/`,
        ignore: ['**/*.graphql'],
      },
    },
  ],
};

export default config;
