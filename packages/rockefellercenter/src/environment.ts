export const FILM_PHOTO_AUDIENCE_ID = process.env.FILM_PHOTO_AUDIENCE_ID!;
export const BOS_API_URL = process.env.BOS_API_URL!;
export const BOS_TOR_SESSION_ID = process.env.BOS_TOR_SESSION_ID!;
export const BOS_TOR_WORKSTATION_AK = process.env.BOS_TOR_WORKSTATION_AK!;
export const BOS_TOR_USERNAME = process.env.BOS_TOR_USERNAME!;
export const BOS_TOR_PASSWORD = process.env.BOS_TOR_PASSWORD!;
export const BOS_RINK_USERNAME = process.env.BOS_RINK_USERNAME!;
export const BOS_RINK_PASSWORD = process.env.BOS_RINK_PASSWORD!;
export const BOS_RINK_SESSION_ID = process.env.BOS_RINK_SESSION_ID!;
export const BOS_RINK_WORKSTATION_AK = process.env.BOS_RINK_WORKSTATION_AK!;
export const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY!;
export const GOOGLE_SEARCH_KEY = process.env.GOOGLE_SEARCH_KEY!;
export const MAILCHIMP_API_KEY = process.env.MAILCHIMP_API_KEY!;
export const MAILCHIMP_AUDIENCE_ID = process.env.MAILCHIMP_AUDIENCE_ID!;
export const MAILCHIMP_URL = process.env.MAILCHIMP_URL!;
export const SANITY_DATASET = process.env.SANITY_DATASET!;
export const SANITY_PROJECT_ID = process.env.SANITY_PROJECT_ID!;
export const SANITY_TOKEN = process.env.SANITY_TOKEN!;
export const SENDGRID_API_KEY = process.env.SENDGRID_API_KEY!;
export const SENDGRID_DEV_FROM = process.env.SENDGRID_DEV_FROM;
export const SENDGRID_DEV_TO = process.env.SENDGRID_DEV_TO;
export const SENTRY_DSN = process.env.SENTRY_DSN!;
export const TRIPLESEAT_PUBLIC_KEY = process.env.TRIPLESEAT_PUBLIC_KEY!;
export const LANDBOT_ID = process.env.LANDBOT_ID;
export const CURATOR_API_KEY = process.env.CURATOR_API_KEY;
export const SHIFT4_CLIENT_GUID = process.env.SHIFT4_CLIENT_GUID;
export const SHIFT4_AUTH_TOKEN = process.env.SHIFT4_AUTH_TOKEN;
export const SHIFT4_TOR_ACCESS_TOKEN = process.env.SHIFT4_TOR_ACCESS_TOKEN;
export const SHIFT4_RINK_ACCESS_TOKEN = process.env.SHIFT4_RINK_ACCESS_TOKEN;
export const SHIFT4_API_URL = process.env.SHIFT4_API_URL;
export const SHIFT4_I4GO_URL = process.env.SHIFT4_I4GO_URL;
export const MAILCHIMP_ECOMM_AUDIENCE_ID =
  process.env.MAILCHIMP_ECOMM_AUDIENCE_ID!;
export const MAILCHIMP_ECOMM_API_KEY = process.env.MAILCHIMP_ECOMM_API_KEY!;
export const MONGODB_CONNECTION = process.env.MONGODB_CONNECTION!;
export const MONGODB_DB = process.env.MONGODB_DB!;
export const MONGODB_COLLECTION = process.env.MONGODB_COLLECTION!;

// Legacy Kount Command variables
export const KOUNT_MID = process.env.KOUNT_MID!;
export const KOUNT_KHASH_KEY = process.env.KOUNT_KHASH_KEY!;
export const KOUNT_ENV = process.env.KOUNT_ENV || 'sandbox';
export const KOUNT_API_URL =
  process.env.KOUNT_API_URL ||
  (KOUNT_ENV === 'TEST'
    ? 'https://api-sandbox.kount.com'
    : 'https://api.kount.com');

// Kount 360 Payments Fraud v2.0 variables
export const KOUNT_CLIENT_ID = process.env.KOUNT_CLIENT_ID || KOUNT_MID;
export const KOUNT_API_KEY = process.env.KOUNT_API_KEY!;
export const KOUNT_AUTH_URL =
  process.env.KOUNT_AUTH_URL ||
  (KOUNT_ENV === 'TEST'
    ? 'https://login.kount.com/oauth2/ausdppkujzCPQuIrY357/v1/token'
    : 'https://login.kount.com/oauth2/ausdppksgrbyM0abp357/v1/token');

export const GTM_ENV = process.env.GTM_ENV!;
export const MAILCHIMP_SKYLIFT_AUDIENCE_ID =
  process.env.MAILCHIMP_SKYLIFT_AUDIENCE_ID!;
export const ENABLE_NEW_RELIC_LOGGING = process.env.ENABLE_NEW_RELIC_LOGGING!;
export const NEW_RELIC_LICENSE_KEY = process.env.NEW_RELIC_LICENSE_KEY!;
export const VIVA_CLIENT_BASE_URL = process.env.VIVA_CLIENT_BASE_URL;
export const DISABLE_MONGODB_CACHING =
  process.env.DISABLE_MONGODB_CACHING === 'true';
export const FIREBASE_API_KEY = process.env.FIREBASE_API_KEY!;
export const FIREBASE_AUTH_DOMAIN = process.env.FIREBASE_AUTH_DOMAIN!;
export const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID!;
export const FIREBASE_STORAGE_BUCKET = process.env.FIREBASE_STORAGE_BUCKET!;
export const FIREBASE_MESSAGING_SENDER_ID =
  process.env.FIREBASE_MESSAGING_SENDER_ID!;
export const FIREBASE_APP_ID = process.env.FIREBASE_APP_ID!;
export const FIREBASE_MEASUREMENT_ID = process.env.FIREBASE_MEASUREMENT_ID!;
export const ENV_ALL_IN_PASS_ADULT_PEAK_AK =
  process.env.ENV_ALL_IN_PASS_ADULT_PEAK_AK!;
export const ENV_ALL_IN_PASS_CHILD_PEAK_AK =
  process.env.ENV_ALL_IN_PASS_CHILD_PEAK_AK!;
export const ENV_ALL_IN_PASS_SENIOR_PEAK_AK =
  process.env.ENV_ALL_IN_PASS_SENIOR_PEAK_AK!;
export const ENV_BUNNY_PHOTO_PRODUCT_AK =
  process.env.ENV_BUNNY_PHOTO_PRODUCT_AK!;
export const ENV_FIVE_ACRES_PRODUCT_AK = process.env.ENV_FIVE_ACRES_PRODUCT_AK!;
export const ENV_ON_THE_ROCKS_EVENT_AK = process.env.ENV_ON_THE_ROCKS_EVENT_AK!;
