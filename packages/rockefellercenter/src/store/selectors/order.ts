import { createSelector } from 'reselect';

import { COMBO_FLOWS, StatGroup } from '../../services/viva/constants';

import type { CheckBasketItem } from '../../services/viva/types';
import type { RootState } from '../.';

export const selectOrder = (state: RootState) => state.order;

export const selectEvent = createSelector(selectOrder, (order) => order.event);

// export const selectAttraction = createSelector(
//   selectOrder,
//   (order) => order.attraction,
// );

export const selectAttributes = createSelector(
  selectOrder,
  (order) => order.attributes,
);

export const selectAttribute = createSelector(
  [selectAttributes, (_, attribute: string) => attribute],
  (attributes, attribute) => attributes?.[attribute],
);

export const selectShopCart = createSelector(selectOrder, (order) => {
  return { flow: order.flow, ...order.shopCart };
});

export const selectFlow = createSelector(selectOrder, (order) => order.flow);

export const selectFlowBaseProducts = createSelector(
  selectOrder,
  (order) => order.flowBaseProducts,
);

export const selectShopCartDateTime = createSelector(
  selectShopCart,
  (shopCart) => shopCart.dateTime,
);

export const selectShopCartDateTimeObject = createSelector(
  selectShopCart,
  (shopCart) => {
    const dt = new Date(shopCart.dateTime);
    return {
      day: dt.getDate(),
      month: dt.getMonth(),
      year: dt.getFullYear(),
      time: shopCart.dateTime,
    };
  },
);

export const selectShopCartDate = createSelector(
  selectShopCart,
  (shopCart) => shopCart.date,
);

export const selectShopCartTimeAsDate = createSelector(
  selectShopCartDateTime,
  (dateTime) => new Date(dateTime),
);

export const selectPerformances = createSelector(
  selectShopCart,
  (shopCart) => shopCart.performances,
);

export const selectPerformancesForFlow = createSelector(
  selectShopCart,
  selectFlow,
  (shopcart, flow) => shopcart.performances[flow],
);

export const selectPerformancesFlat = createSelector(
  selectPerformances,
  (performances) => Object.values(performances),
);

export const selectHasPerformances = createSelector(
  selectPerformancesFlat,
  (performances) => performances.flat().length > 0,
);

// export const selectSkipToBilling = createSelector(
//   selectOrder,
//   (order) => order.skipToBilling,
// );

export const selectShopCartSelections = createSelector(
  selectShopCart,
  (shopCart) => shopCart.selections[shopCart.flow] || [],
);

export const selectShopCartAddOnSelections = createSelector(
  selectShopCart,
  (shopCart) =>
    shopCart.addOnSelections.filter((sel) => (sel.quantity ?? 0) > 0),
);

export const selectTotalQuantity = createSelector(selectOrder, (order) => {
  const initialTotal = 0;
  const filteredSelections =
    order.shopCart.selections[order.flow]?.filter(
      (selection) => !selection.statGroup.includes(StatGroup.RINK_CHAPERONE),
    ) ?? [];
  return filteredSelections.reduce(
    (prev, curr) => prev + curr.count,
    initialTotal,
  );
});

export const selectTotalPrice = createSelector(selectOrder, (order) => {
  const initialTotal = 0;
  return (order.shopCart.selections[order.flow] ?? []).reduce(
    (prev, curr) => prev + curr.price * curr.count,
    initialTotal,
  );
});

export const selectUpsellCount = createSelector(
  selectOrder,
  (order) => order.shopCart.upsellCount,
);

export const selectHasAdult = createSelector(
  selectShopCartSelections,
  (selections) =>
    selections.reduce((acc, selection) => {
      if (
        selection.statGroup.includes(StatGroup.RINK_ADULT) ||
        selection.statGroup.includes(StatGroup.GA_SENIOR) ||
        selection.statGroup.includes(StatGroup.TOR_VIP) ||
        selection.statGroup.includes(StatGroup.GA_ADULT)
      ) {
        return acc + selection.count;
      }

      return acc;
    }, 0) > 0,
);

export const selectHasChild = createSelector(
  selectShopCartSelections,
  (selections) =>
    selections.reduce((acc, selection) => {
      if (
        selection.statGroup.includes(StatGroup.RINK_CHILD) ||
        selection.statGroup.includes(StatGroup.GA_CHILD)
      ) {
        return acc + selection.count;
      }

      return acc;
    }, 0) > 0,
);

export const selectHasToddler = createSelector(
  selectShopCartSelections,
  (selections) =>
    selections.reduce((acc, selection) => {
      if (selection.statGroup.includes(StatGroup.RINK_TODDLER)) {
        return acc + selection.count;
      }

      return acc;
    }, 0) > 0,
);

export const selectNumberOfTicketsWithoutChildrenAndToddlers = createSelector(
  selectShopCartSelections,
  (selections) => {
    const filteredSelections = selections.filter(
      (selection) =>
        !selection.statGroup.includes(StatGroup.RINK_CHILD) &&
        !selection.statGroup.includes(StatGroup.RINK_TODDLER) &&
        !selection.statGroup.includes(StatGroup.GA_CHILD) &&
        !selection.statGroup.includes(StatGroup.GA_TODDLER),
    );
    return filteredSelections.reduce((prev, curr) => prev + curr.count, 0);
  },
);

export const selectHasChaperone = createSelector(
  selectShopCartSelections,
  (selections) =>
    selections.some(
      (selection) =>
        selection.statGroup.includes(StatGroup.RINK_CHAPERONE) &&
        selection.count > 0,
    ),
);

export const selectHasMembership = createSelector(
  selectShopCartSelections,
  (selections) =>
    selections.some(
      (selection) =>
        selection.statGroup.includes(StatGroup.RINK_MEMBERSHIP) &&
        selection.count > 0,
    ),
);

export const selectCityPassBooklet = createSelector(
  selectOrder,
  (order) => order.cityPassBooklet,
);

export const selectIsCombo = createSelector(
  selectOrder,
  (order) => order.isCombo,
);

export const selectCombo = createSelector(selectOrder, (order) => order.combo);

export const selectComboPerformances = createSelector(
  selectCombo,
  (combo) => combo.performances,
);

export const selectComboIndex = createSelector(
  selectOrder,
  (order) => order.combo.comboIndex,
);

// export const selectShopCartFlat = createSelector(selectShopCart, (shopCart) => {
//   return Object.keys(shopCart.selections)
//     .map((flow) => shopCart.selections[flow])
//     .flat();
// });

// export const selectShopCartTotals = createSelector(selectShopCart, (shopCart) =>
//   [
//     ...Object.keys(shopCart.selections)
//       .map((flow) => shopCart.selections[flow])
//       .flat(),
//     ...shopCart.addOnSelections,
//   ].reduce((acc, sel) => acc + (sel?.price ?? 0), 0),
// );

export const selectCitypassItems = createSelector(
  selectShopCart,
  selectShopCartSelections,
  selectFlow,
  (shopcart, selections, flow) => {
    return selections.map<CheckBasketItem>((sel) => ({
      AK: sel.productAK,
      QTY: sel.count,
      performances: shopcart.performances[flow],
    }));
  },
);

export const selectFirstTimeSlot = createSelector(
  selectFlow,
  selectCombo,
  selectShopCart,
  (flow, combo, shopCart) => {
    const index = COMBO_FLOWS.includes(flow) ? combo.comboIndex : 0;

    return shopCart.timeSlot?.[index];
  },
);

export const selectShopCartDateAsValid = createSelector(
  selectShopCart,
  (shopCart) => new Date(shopCart.dateTime).getHours() === 0,
);

export const selectIsFetchingPerformancesForAddOns = (state: RootState) =>
  state.order.isFetchingPerformancesForAddOns;
