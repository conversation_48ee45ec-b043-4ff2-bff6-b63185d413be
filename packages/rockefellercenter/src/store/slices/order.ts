import { createSlice } from '@reduxjs/toolkit';
import { tzDate } from '@formkit/tempo';

import {
  Flow,
  StatGroup,
  Attraction,
  BosEvent,
  COMBO_FLOWS,
  DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK,
} from '../../services/viva/constants';

import { rinkMonth } from './wizard';

import type { TicketType } from '../../services/viva/types';
import type { ProductTicketType } from '../../services/viva/types/response';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { AddOnItem } from '../../components/BuyTickets';
import type { Availability } from '../../components/BuyTickets/SegmentedTimePicker/Availabilities';
import type {
  CityPassVoucher,
  Combo,
  ComboPerformance,
  FlowKeys,
  OrderState,
  ShopCart,
  ShopCartDate,
  ShopCartSelection,
  ShopCartSelectionFlow,
} from '../types/order';
import type { TPerformance } from '../../utils/use-capacity-addon';

export const EMPTY_VOUCHER = {
  voucherCode: '',
  productCode: null,
};

const initialSelections = {};
const selectionFlowKeys = Object.keys(Flow);
for (let i = 0; i < selectionFlowKeys.length; i++) {
  Object.assign(initialSelections, { [selectionFlowKeys[i] as FlowKeys]: [] });
}

const today = tzDate(new Date(), 'America/New_York');

export const EMPTY_PERFORMANCES: { [key in Flow]: string[] } =
  Object.fromEntries(
    Object.values(Flow).map((flow) => [flow, []]),
  ) as unknown as { [key in Flow]: string[] };

export const EMPTY_SHOP_CART: ShopCart = {
  addOnSelections: [],
  date: {
    // Use getUTCDate(), getUTCMonth(), and getUTCFullYear() to get the date components
    day: today.getUTCDate(),
    // Adjust month + 1 since getUTCMonth() returns 0-11 for Jan-Dec
    month: today.getUTCMonth(),
    year: today.getUTCFullYear(),
    time: 0,
  },
  // To set dateTime, use the Date constructor directly. Adjust month - 1 since months are 0-indexed.
  dateTime: new Date(
    today.getUTCFullYear(),
    today.getUTCMonth(),
    today.getUTCDate(),
    0,
  ).getTime(),
  performances: EMPTY_PERFORMANCES,
  selections: initialSelections as ShopCartSelectionFlow,
  upsellCount: 0,
};

const initialState: OrderState = {
  account: {
    accountAKs: [],
    address: {
      city: '',
      country: '',
      street: '',
      state: '',
      zip: '',
    },
    email: '',
    firstName: '',
    lastName: '',
    mobileNumber: null,
    name: null,
    optInMail: false,
    optInSMS: false,
    tos: false,
    nonrefundable: false,
  },
  attraction: null,
  attributes: {},
  cityPassBooklet: [],
  shopCart: EMPTY_SHOP_CART,
  event: null,
  flow: Flow.NONE,
  flowBaseProducts: [],
  skipToBilling: false,
  isCombo: false,
  combo: {
    flow: Flow.RC_ROCK_PASS,
    comboIndex: 0,
    performances: [],
  },
  isFetchingPerformancesForAddOns: false,
};

type InitialState = OrderState;

export const orderSlice = createSlice({
  initialState,
  name: 'order',
  reducers: {
    initialize: (
      state,
      action: PayloadAction<
        Pick<InitialState, 'attraction' | 'flow' | 'event' | 'isCombo'>
      >,
    ) => {
      state.attraction = action.payload.attraction;
      state.event = action.payload.event;
      state.flow = action.payload.flow;
      state.isCombo = action.payload.isCombo;

      if (
        [Flow.RINK, Flow.CHALETS, Flow.RINK_VIP].includes(action.payload.flow)
      ) {
        // MOMENT
        // const todayInNYC = moment().tz('America/New_York').set({
        //   hour: 0,
        //   minute: 0,
        //   second: 0,
        //   millisecond: 0,
        // });
        const todayInNYC = new Date(today.setHours(0, 0, 0, 0));
        const _rinkMonth = rinkMonth();
        // const month = todayInNYC.month();
        const month = todayInNYC.getMonth();
        // const day = month === _rinkMonth ? todayInNYC.day() : 1;
        const day = month === _rinkMonth ? todayInNYC.getDate() : 1;

        state.shopCart.date.month = _rinkMonth;
        state.shopCart.date.day = day;

        // MOMENT
        // const dateTime = moment().tz('America/New_York').set({
        //   year: today.year(),
        //   month: _rinkMonth,
        //   day: day,
        //   hours: 0,
        // });

        // Create a Date object with the specified year, month, and day
        const specificDate = new Date(today.getFullYear(), _rinkMonth, day);

        // Set hours to 0 (midnight)
        specificDate.setHours(0, 0, 0, 0);

        // Adjust the date to the 'America/New_York' timezone
        const dateTime = tzDate(specificDate, 'America/New_York');

        state.shopCart.dateTime = dateTime.getTime();
      }
    },
    reset: (state) => {
      return {
        ...initialState,
        account: {
          ...initialState.account,
        },
        attraction: state.attraction,
        event: state.event,
        flow: state.flow,
        isCombo: state.isCombo,
      };
    },
    setAccountAK: (state, action: PayloadAction<string>) => {
      state.account.accountAKs = [action.payload];
    },
    setPerformances: (
      state,
      action: PayloadAction<{ data: { [key in Flow]?: string[] }; flow: Flow }>,
    ) => {
      const { data, flow } = action.payload;

      if (flow === Flow.RC_ROCK_PASS) {
        const newData = [];

        if (state.shopCart.performances[flow]?.[0]) {
          newData.push(state.shopCart.performances[flow][0]);
        }

        state.shopCart.performances = {
          ...state.shopCart.performances,
          [flow]: [...newData, ...(data[flow] ?? [])],
        };
      } else {
        state.shopCart.performances = {
          ...state.shopCart.performances,
          ...data,
        };
      }
    },
    setAttraction: (state, action: PayloadAction<Attraction>) => {
      state.attraction = action.payload;
    },
    setAttribute: (state, action: PayloadAction<[string, unknown]>) => {
      const [key, value] = action.payload;

      const payload = {
        ...state.attributes,
        [key]: value,
      };

      state.attributes = payload;
    },
    unsetAttribute: (state, action: PayloadAction<string>) => {
      delete state.attributes?.[action.payload];
    },
    setFlow: (state, action: PayloadAction<Flow>) => {
      state.flow = action.payload;
    },
    setFlowBaseProducts: (
      state,
      action: PayloadAction<ProductTicketType[]>,
    ) => {
      state.flowBaseProducts = action.payload;
    },
    setEvent: (state, action: PayloadAction<BosEvent>) => {
      state.event = action.payload;
    },
    resetShopCart: (state) => {
      state.shopCart = EMPTY_SHOP_CART;
    },
    setShopCartAddOnSelections: (state, action: PayloadAction<AddOnItem[]>) => {
      if (state.shopCart.addOnSelections.length === 0) {
        state.shopCart.addOnSelections = action.payload;
      } else {
        if (action.payload.length === 0) {
          return state;
        }

        // Create a map of existing add-ons by ak for quick lookup
        const existingAddOnsMap = new Map(
          state.shopCart.addOnSelections.map((addOn) => [addOn.ak, addOn]),
        );

        // Process each add-on in the payload
        action.payload.forEach((newAddOn) => {
          const existingAddOn = existingAddOnsMap.get(newAddOn.ak);

          if (existingAddOn) {
            // If it exists, update it while preserving any existing properties
            existingAddOnsMap.set(newAddOn.ak, {
              ...existingAddOn,
              ...newAddOn,
              // Preserve the higher quantity if both have quantities
              quantity: newAddOn.quantity ?? 0,
              // Preserve existing performances if new one doesn't have any
              performances: newAddOn.performances?.length
                ? newAddOn.performances
                : existingAddOn.performances,
            });
          } else {
            // If it doesn't exist, add it
            existingAddOnsMap.set(newAddOn.ak, newAddOn);
          }
        });

        // Convert map back to array
        state.shopCart.addOnSelections = Array.from(existingAddOnsMap.values());
      }
      return state;
    },
    setShopCartDate: (state, action: PayloadAction<ShopCartDate>) => {
      state.shopCart.date = action.payload;
    },
    setShopCartDateTime: (state, action: PayloadAction<number>) => {
      state.shopCart.dateTime = action.payload;
    },
    resetShopCartSelectionsTimeSlots: (state) => {
      state.shopCart.timeSlot = [];
    },
    setShopCartSelectionsTimeSlots: (
      state,
      action: PayloadAction<Availability[]>,
    ) => {
      const selections = [...(state.shopCart.selections[state.flow] ?? [])].map(
        (sel) => ({
          ...sel,
          timeSlot: action.payload,
        }),
      );

      state.shopCart.selections[state.flow] = selections;

      return state;
    },
    setShopCartTimeSlot: (state, action: PayloadAction<Availability>) => {
      state.shopCart.timeSlot = [action.payload];
    },
    updateShopCartTimeSlots: (state, action: PayloadAction<Availability>) => {
      const index = COMBO_FLOWS.includes(state.flow)
        ? state.combo.comboIndex
        : 0;

      if (!state.shopCart.timeSlot) {
        state.shopCart.timeSlot = [];
      }

      state.shopCart.timeSlot[index] = action.payload;
    },
    setSelectionProductAK: (
      state,
      action: PayloadAction<{ statGroup: StatGroup; productAK: string }>,
    ) => {
      const selections = state.shopCart.selections[state.flow];
      if (!selections) return state;

      const selectionIndex = selections.findIndex(
        (selection: ShopCartSelection) =>
          selection.statGroup.indexOf(action.payload.statGroup) !== -1,
      );

      if (selectionIndex !== -1) {
        selections[selectionIndex].productAK = action.payload.productAK;
      }

      return state;
    },
    updateShopCartSelections: (state, action: PayloadAction<TicketType[]>) => {
      const flowSelections = state.shopCart.selections[state.flow];
      if (!flowSelections) return state;

      action.payload
        .filter((baseProd) => baseProd.pricetableAK === state.priceTableAK)
        .forEach((selectionPayload) => {
          const index = flowSelections.findIndex(
            (selection) => selection.type === selectionPayload.type,
          );

          if (index !== -1) {
            const existing = flowSelections[index];
            if (existing) {
              flowSelections[index] = {
                ...existing,
                ...selectionPayload,
                count: existing.count,
                timeSlot: existing.timeSlot,
                capacityGuid: existing.capacityGuid,
                dateTime: existing.dateTime,
              };
            } else {
              flowSelections.push({ ...selectionPayload, count: 1 });
            }
          }
        });

      return state;
    },
    updateShopCartSelectionsBySwap: (
      state,
      action: PayloadAction<{
        newTicket: TicketType;
        ticketToSwapIndex: number;
      }>,
    ) => {
      const { newTicket, ticketToSwapIndex } = action.payload;
      const flowSelections = state.shopCart.selections[state.flow];

      if (flowSelections && flowSelections[ticketToSwapIndex]) {
        flowSelections[ticketToSwapIndex] = {
          ...newTicket,
          count: 1,
        };
      }
    },
    updateShopCartSelectionsPrice: (
      state,
      action: PayloadAction<{
        productAK: string;
        price: number;
        tax: number;
        discounted?: boolean;
      }>,
    ) => {
      const { productAK, price, tax, discounted = false } = action.payload;
      const flowSelections = state.shopCart.selections[state.flow];

      if (!flowSelections) return state;

      let updatedSelections;

      if (discounted) {
        const hasDiscountedProducts = (
          flow: Flow,
        ): flow is keyof typeof DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK => {
          return flow in DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK;
        };

        updatedSelections = flowSelections.map((selection) => {
          const matchingAK =
            discounted && hasDiscountedProducts(state.flow)
              ? state.flow &&
                DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK?.[state.flow]?.[
                  productAK as keyof typeof DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK[typeof state.flow]
                ]
              : productAK;

          return selection.productAK === matchingAK
            ? { ...selection, productAK, price, tax }
            : selection;
        });
      } else {
        updatedSelections = flowSelections.map((selection) =>
          selection.productAK === productAK
            ? { ...selection, price, tax }
            : selection,
        );
      }
      state.shopCart.selections[state.flow] = updatedSelections;

      return state;
    },
    setShopCartSelections: (
      state,
      action: PayloadAction<ShopCartSelection>,
    ) => {
      const flowSelections = state.shopCart.selections[state.flow];
      if (!flowSelections) return state;

      const selectionIndex = flowSelections.findIndex(
        (selection) => selection.type === action.payload.type,
      );

      if (selectionIndex !== -1) {
        // Selection exists in cart
        if (state.flow === Flow.RINK_MEMBERSHIP && action.payload.count === 0) {
          // Remove the selection for Rink Membership if count is 0
          flowSelections.splice(selectionIndex, 1);
        } else {
          // Update the count for existing selection
          flowSelections[selectionIndex].count = action.payload.count;
        }
      } else {
        // Add new selection to cart
        flowSelections.push(action.payload);
      }

      return state;
    },
    setShopCartSelectionsByTicketTypeByPush: (
      state,
      action: PayloadAction<TicketType>,
    ) => {
      const flowSelections = state.shopCart.selections[state.flow];
      if (!flowSelections) return state;

      flowSelections.push({
        ...action.payload,
        count: 1,
      });

      return state;
    },
    setShopCartSelectionsBySplice: (
      state,
      action: PayloadAction<number | undefined>,
    ) => {
      const flowSelections = state.shopCart.selections[state.flow];
      if (!flowSelections) return state;

      if (action.payload !== undefined) {
        flowSelections.splice(action.payload, 1);
      } else {
        flowSelections.splice(-1);
      }
      return state;
    },
    // Blindly pushes (or initializes) a selection to the shop cart
    setShopCartSelectionsByVoucherCode: (
      state,
      action: PayloadAction<ShopCartSelection | ShopCartSelection[]>,
    ) => {
      const flowSelections = state.shopCart.selections[state.flow];
      if (!flowSelections) return state;

      const selections = Array.isArray(action.payload)
        ? action.payload
        : [action.payload];

      selections.forEach((selection) => {
        if (
          !flowSelections.some((i) => i.voucherCode === selection.voucherCode)
        ) {
          // Only push if not already in the cart
          flowSelections.push(selection);
        }
      });

      return state;
    },
    setUpsellCount: (state, action: PayloadAction<number>) => {
      state.shopCart.upsellCount = action.payload;
    },
    incrementUpsellCount: (state, action: PayloadAction<number>) => {
      state.shopCart.upsellCount = state.shopCart.upsellCount + action.payload;
    },
    decrementUpsellCount: (state, action: PayloadAction<number>) => {
      state.shopCart.upsellCount = state.shopCart.upsellCount - action.payload;
    },
    setSkipToBilling: (state, action: PayloadAction<boolean>) => {
      state.skipToBilling = action.payload;
    },
    setCityPassBooklet: (
      state,
      action: PayloadAction<{ voucher: CityPassVoucher; index: number }>,
    ) => {
      if (!state.cityPassBooklet) return;

      state.cityPassBooklet[action.payload.index] = action.payload.voucher;
    },
    setCityPassBooklets: (state, action: PayloadAction<CityPassVoucher[]>) => {
      state.cityPassBooklet = [...new Set(action.payload)];
    },
    removeCityPassVoucher: (state, action: PayloadAction<number>) => {
      // Booklet
      if (state.cityPassBooklet) {
        if (state.cityPassBooklet.length === 1) {
          state.cityPassBooklet = [];
        } else {
          state.cityPassBooklet.splice(action.payload, 1);
        }
      }

      // Selections
      const flowSelections = state.shopCart.selections[state.flow];
      if (flowSelections) {
        if (flowSelections.length === 1) {
          state.shopCart.selections[state.flow] = [];
        } else {
          flowSelections.splice(action.payload, 1);
        }
      }
    },
    setIsCombo: (state, action: PayloadAction<boolean>) => {
      state.isCombo = action.payload;
    },
    setComboIndex: (state, action: PayloadAction<number>) => {
      state.combo.comboIndex = action.payload;
    },
    setComboPerformances: (state, action: PayloadAction<ComboPerformance>) => {
      state.combo.performances[state.combo.comboIndex] = action.payload;
    },
    setCombo: (state, action: PayloadAction<Combo>) => {
      state.combo = { ...state.combo, ...action.payload };
    },
    setShopCartFlowForEditing: (
      state,
      action: PayloadAction<{
        flow: Flow;
        data: ShopCartSelection[];
        addOns: AddOnItem[];
      }>,
    ) => {
      state.shopCart.selections[action.payload.flow] = action.payload.data;
      state.shopCart.addOnSelections = action.payload.addOns;
    },
    setPriceTableAK: (state, action: PayloadAction<string>) => {
      state.priceTableAK = action.payload;
    },
    setCityPassCapGUID: (state, action: PayloadAction<string>) => {
      state.cityPassCapGUID = action.payload;
    },
    setAddOnsPerformancesHash: (
      state,
      action: PayloadAction<Record<string, TPerformance>>,
    ) => {
      state.shopCart.addOnsPerformancesHash = action.payload;
    },
    setIsFetchingPerformancesForAddOns: (
      state,
      action: PayloadAction<boolean>,
    ) => {
      state.isFetchingPerformancesForAddOns = action.payload;
    },
  },
});
