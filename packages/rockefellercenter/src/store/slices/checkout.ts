import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import {
  CART_LOCAL_STORAGE_KEY,
  Flow,
  StatGroup,
} from '../../services/viva/constants';
import {
  getObjectForLocalStorageKey,
  removeObjectForLocalStorageKey,
} from '../utils';
import {
  removeByTypeAndDateTimeDTO,
  clearDateFromCheckoutDTO,
  setFlowSelectionDTO,
  removeAddOnDTO,
  setAttributesDTO,
} from '../utils/checkout';

import { EMPTY_SHOP_CART } from './order';

import type { AddOnItem } from '../../components/BuyTickets';
import type {
  CheckoutAddonSelections,
  CheckoutCart,
  CheckoutOrderAccountFields,
  CheckoutOrderPayment,
  CheckoutPerformances,
  CheckoutType,
  CheckoutCartSelectionCapacityGuidPayload,
} from '../types/checkout';
import type { Refund, ShopCart, ShopCartSelection } from '../types/order';
import type { Shift4SaleResponse } from '../../services/viva/types/response';

const now = new Date();
const lsCart = getObjectForLocalStorageKey<CheckoutType>(
  CART_LOCAL_STORAGE_KEY,
);

export const initialCheckoutState: CheckoutType = {
  cart: {
    ...EMPTY_SHOP_CART,
    performances: Object.fromEntries(
      Object.keys(Flow).map((f) => [f, {}]),
    ) as unknown as CheckoutPerformances,
    addOnSelections: Object.fromEntries(
      Object.keys(Flow).map((f) => [f, {}]),
    ) as unknown as CheckoutAddonSelections,
    total: '0',
    totalTax: '0',
    totalWithTax: '0',
  },
  payment: {
    accountAKs: [] as string[],
  } as CheckoutOrderPayment,
  refund: {} as Refund,
  account: {
    accountAKs: [],
    address: {
      city: '',
      country: '',
      street: '',
      state: '',
      zip: '',
    },
    email: '',
    firstName: '',
    lastName: '',
    name: '',
    mobileNumber: '',
    optInMail: false,
    optInSMS: false,
    tos: false,
    nonrefundable: false,
    birthMonth: '',
    birthDate: '',
    birthYear: '',
  },
  isRefund: false,
  flow: Flow.NONE,
  attributes: {},
};

export const checkoutSlice = createSlice({
  initialState:
    (lsCart?.counterEndTime ?? 0) < now.getTime()
      ? initialCheckoutState
      : lsCart ?? initialCheckoutState,
  name: 'checkout',
  reducers: {
    initialize: () => {
      // if cart is expired, reset
      const lsCart = getObjectForLocalStorageKey<CheckoutType>(
        CART_LOCAL_STORAGE_KEY,
      );
      const now = new Date();

      if (
        lsCart &&
        lsCart.counterEndTime &&
        lsCart.counterEndTime < now.getTime()
      ) {
        removeObjectForLocalStorageKey(CART_LOCAL_STORAGE_KEY);
      }
    },
    reset: (state) => {
      return initialCheckoutState;
    },
    setFlowSelection: (
      state,
      action: PayloadAction<{
        flow: Flow;
        data: CheckoutType['cart']['selections'][0];
        performances: ShopCart['performances'];
        dateTime: number;
      }>,
    ) => {
      // The heavy lifting happens in AddToCartButton.tsx
      const newData = setFlowSelectionDTO({
        ...action.payload,
        state,
      });

      if (newData) {
        state.cart = newData.cart;
      }

      return state;
    },
    setAddOns: (
      state,
      action: PayloadAction<{
        addOns: AddOnItem[];
        dateTime: number;
        originalDateTime?: number;
        flow: Flow;
      }>,
    ) => {
      const validAddOns = action.payload.addOns.filter(
        (addOn) => addOn?.quantity && addOn.quantity > 0,
      );
      state.cart.addOnSelections[action.payload.flow] = {
        [action.payload.dateTime]: validAddOns,
      };

      return state;
    },
    removeFlowSelection: (state, action: PayloadAction<Flow>) => {
      state.cart.selections[action.payload] = [];

      return state;
    },
    removeItemFromCheckoutCartByTypeAndDateTime: (
      state,
      action: PayloadAction<{
        type: string;
        dateTime: number;
        flow: Flow;
      }>,
    ) => {
      const { flow, dateTime } = action.payload;

      removeByTypeAndDateTimeDTO({
        ...action.payload,
        state,
      });

      // Check if there are any selections left for the current flow
      const hasRemainingSelections =
        state.cart.selections[flow]?.length ?? 0 > 0;

      // Remove all add-ons for the current flow and datetime
      if (
        !hasRemainingSelections &&
        state.cart.addOnSelections[flow] &&
        state.cart.addOnSelections[flow][dateTime]
      ) {
        delete state.cart.addOnSelections[flow][dateTime];
      }

      return state;
    },
    removeAddOnItemFromCheckoutByFlowAndDateTime: (
      state,
      action: PayloadAction<{
        flow: Flow;
        statGroup: StatGroup;
        dateTime: number;
      }>,
    ) => {
      removeAddOnDTO({ ...action.payload, state });

      return state;
    },
    clearDateFromCheckout: (
      state,
      action: PayloadAction<{ flow: Flow; dateTime: number }>,
    ) => {
      clearDateFromCheckoutDTO({ ...action.payload, state });

      return state;
    },
    setCheckoutTotals: (
      state,
      action: PayloadAction<
        Pick<
          CheckoutCart,
          | 'total'
          | 'totalTax'
          | 'totalWithTax'
          | 'totalDiscount'
          | 'fee'
          | 'couponCode'
          | 'subtotal'
          | 'inclusiveTax'
        >
      >,
    ) => {
      state.cart = {
        ...state.cart,
        ...action.payload,
      };

      return state;
    },
    setSaleAK: (state, action: PayloadAction<string>) => {
      if (!state.payment) return;

      state.payment.saleAK = action.payload;
    },
    setSaleId: (state, action: PayloadAction<string>) => {
      state.payment.saleId = action.payload;
    },
    setSaleAndReservation: (
      state,
      action: PayloadAction<{ saleAK: string; reservationCode: string }>,
    ) => {
      if (!state.payment) return;

      state.payment.saleAK = action.payload.saleAK;
      state.payment.reservationCode = action.payload.reservationCode;
    },
    setReservationCode: (state, action: PayloadAction<string>) => {
      if (!state.payment) return;

      state.payment.reservationCode = action.payload;
    },
    setTransactionAK: (state, action: PayloadAction<string>) => {
      if (!state.payment) return;

      state.payment.transactionAK = action.payload;
    },
    setPayment: (
      state,
      action: PayloadAction<{
        payment?: Shift4SaleResponse;
        invoice?: number | string;
        reservationCode?: string | null | undefined;
        saleAK: string;
      }>,
    ) => {
      const orderPayment = action.payload.payment?.result[0];
      const { card, transaction } = orderPayment ?? {};

      const paymentPayload: CheckoutOrderPayment = {
        ...(card
          ? {
              creditCard: {
                suffix: card.number,
                type: card.type,
              },
            }
          : {}),
        saleId: transaction?.invoice ?? '',
        status: 1,
        total: parseFloat(state.cart.total ?? '0'),
        totalTax: parseFloat(state.cart.totalTax ?? '0'),
        totalWithTax: parseFloat(state.cart.totalWithTax ?? '0'),
        saleAK: action.payload.saleAK,
        accountAKs: state.payment?.accountAKs ?? [],
        address: state.account?.address ?? {
          city: '',
          state: '',
          country: '',
          zip: '',
          street: '',
        },
        email: state.account?.email ?? '',
        firstName: state.account?.firstName ?? '',
        lastName: state.account?.lastName ?? '',
        name: state.account?.name ?? '',
        invoice: action.payload.invoice ?? '',
        reservationCode: action.payload.reservationCode,
      };

      state.payment = paymentPayload;
      return state;
    },
    setAccountFields: (
      state,
      action: PayloadAction<CheckoutOrderAccountFields>,
    ) => {
      state.account = action.payload;
    },
    setAccountZipCode: (state, action: PayloadAction<string>) => {
      if (state.account) {
        state.account.address.zip = action.payload;
      }
    },
    setAttributes: (state, action: PayloadAction<Record<string, unknown>>) => {
      const newData = setAttributesDTO({
        attributes: action.payload,
        state,
      });

      state = newData;

      return state;
    },
    setRefund: (state, action: PayloadAction<Refund>) => {
      state.refund = action.payload;
    },
    setShopCartSelectionsCapacityGuid: (
      state,
      action: PayloadAction<CheckoutCartSelectionCapacityGuidPayload[]>,
    ) => {
      const flowSelections = state.cart.selections[state.flow];
      if (!flowSelections) return state;

      action.payload.forEach((selectionPayload) => {
        const selection = flowSelections.find(
          (selection) => selection.productAK === selectionPayload.productAK,
        );

        if (selection) {
          selection.capacityGuid = selectionPayload.capacityGuid;
        }
      });

      return state;
    },
    setCheckoutFlow: (state, action: PayloadAction<Flow>) => {
      state.flow = action.payload;
      return state;
    },
    setAccountAKs: (state, action: PayloadAction<string[]>) => {
      if (!state.payment.accountAKs) state.payment.accountAKs = [];

      state.payment.accountAKs = [
        ...state.payment.accountAKs,
        ...action.payload,
      ];
    },
    updateCheckoutCartSelections: (
      state,
      action: PayloadAction<{
        flow: Flow;
        dateTime: number;
        selections: Partial<Record<Flow, ShopCartSelection[]>>;
      }>,
    ) => {
      const { flow, selections } = action.payload;
      if (selections[flow]) {
        state.cart.selections[flow] = selections[flow]!;
      }
    },
    updateSkateRentalPerformances: (
      state,
      action: PayloadAction<{
        performanceAK: string;
        flow: Flow;
      }>,
    ) => {
      const { performanceAK, flow } = action.payload;
      const addOnSelections = state.cart.addOnSelections[flow];

      if (addOnSelections) {
        Object.keys(addOnSelections).forEach((dateTime) => {
          Object.keys(addOnSelections[dateTime]).forEach((addonIndex) => {
            if (addonIndex !== undefined && addonIndex !== null) {
              const index = Number(addonIndex);
              if (
                addOnSelections[dateTime][index].statGroup ===
                StatGroup.SKATE_RENTAL
              ) {
                addOnSelections[dateTime][index] = {
                  ...addOnSelections[dateTime][index],
                  performances: [performanceAK],
                };
              }
            }
          });
        });
      }

      return state;
    },
    updateCartItemDiscount: (
      state,
      action: PayloadAction<{
        productAK: string;
        discount: string;
      }>,
    ) => {
      const { productAK, discount } = action.payload;
      // Remove negative sign from discount value
      const positiveDiscount = discount.startsWith('-')
        ? discount.slice(1)
        : discount;

      // Update discount in cart selections
      Object.keys(state.cart.selections).forEach((flow) => {
        const selections = state.cart.selections[flow as Flow];
        if (selections) {
          selections.forEach((selection) => {
            if (selection.productAK === productAK) {
              selection.discount = positiveDiscount;
            }
          });
        }
      });

      // Update discount in add-on selections
      Object.keys(state.cart.addOnSelections).forEach((flow) => {
        const addOnSelections = state.cart.addOnSelections[flow as Flow];
        if (addOnSelections) {
          Object.keys(addOnSelections).forEach((dateTime) => {
            Object.keys(addOnSelections[dateTime]).forEach((addonIndex) => {
              if (addonIndex !== undefined && addonIndex !== null) {
                const index = Number(addonIndex);
                const addOn = addOnSelections[dateTime][index];
                if (addOn.productAK === productAK) {
                  addOn.discount = positiveDiscount;
                }
              }
            });
          });
        }
      });

      return state;
    },
  },
});

export default checkoutSlice.reducer;
