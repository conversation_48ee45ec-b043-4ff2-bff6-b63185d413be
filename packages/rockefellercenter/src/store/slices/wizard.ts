import { createSlice } from '@reduxjs/toolkit';
import { getDate, getMonth, getYear } from 'date-fns';
import { tzDate, format } from '@formkit/tempo';
// import moment from 'moment-timezone';

import { getObjectForLocalStorageKey } from '../utils';
import { CART_LOCAL_STORAGE_KEY, Flow } from '../../services/viva/constants';
import { DateTimeResponse, formatShortDate } from '../../api/date/ny';

import type { PayloadAction } from '@reduxjs/toolkit';
import type { ShopCartDate, CheckoutType } from '../types';

export interface DatePicker {
  day: number;
  month: number;
  time: number;
  year: number;
}

interface TabState {
  timePeriod: number;
}

interface TabManager {
  selected: TabState;
  active: TabState;
}

export interface WizardState {
  currentStep: number;
  skipToBilling: boolean;
  counterEndTime?: number | null;
  cancelRunningQueries: boolean;
  dateInNYCToday: DateTimeResponse;
  datePicker: DatePicker;
  datePickerTabMenu: TabManager;
  isAdvancingMonths: boolean;
}

const lsCart = getObjectForLocalStorageKey<CheckoutType>(
  CART_LOCAL_STORAGE_KEY,
);

export const rinkMonth = () => {
  // MOMENT
  // const todayInNYC = moment().tz('America/New_York').set({
  //   hour: 0,
  //   minute: 0,
  //   second: 0,
  //   millisecond: 0,
  // });

  // Create a Date object for "now", then adjust it to 'America/New_York' timezone
  const nowInNYC = tzDate(new Date(), 'America/New_York');

  // Reset hours, minutes, seconds, and milliseconds to 0
  // Note: There's no direct manipulation for these in @formkit/tempo, so we use standard Date methods
  const todayInNYC = new Date(nowInNYC.setHours(0, 0, 0, 0));
  // MOMENT
  // const currentMonth = todayInNYC.month();
  const currentMonth = todayInNYC.getMonth();
  // Jan through march are not mathematically greater than novermber through december
  // but need to represented as such
  const skateSeasonMonths: Record<number, number> = {
    10: 10,
    11: 11,
    12: 12,
    0: 13,
    1: 14,
    2: 15,
  };

  return Math.max(skateSeasonMonths[currentMonth] ?? 0, 9);
};

const month = new Date().getMonth();
const year = new Date().getFullYear();

const initialState: WizardState = {
  currentStep: 1,
  skipToBilling: false,
  counterEndTime: lsCart?.counterEndTime,
  cancelRunningQueries: false,
  dateInNYCToday: {
    date: '',
    day: new Date().getDate(),
    month,
    year,
    hours: new Date().getHours(),
    minutes: new Date().getMinutes(),
    firstOfMonth: formatShortDate(new Date(year, month - 1, 1)),
    lastOfMonth: formatShortDate(new Date(year, month, 0)),
    totalDays: new Date(year, month, 0).getDate(),
    future: {},
  },
  datePicker: {
    day: 1,
    month: new Date().getMonth(),
    time: 0,
    year: new Date().getFullYear(),
  },
  datePickerTabMenu: {
    selected: { timePeriod: -1 },
    active: { timePeriod: -1 },
  },
  isAdvancingMonths: true,
};

export const wizardSlice = createSlice({
  initialState,
  name: 'wizard',
  reducers: {
    initialize: (state, action: PayloadAction<Flow>) => {
      const lsCart = getObjectForLocalStorageKey<CheckoutType>(
        CART_LOCAL_STORAGE_KEY,
      );

      state.currentStep = 1;
      state.skipToBilling = false;
      state.counterEndTime = lsCart?.counterEndTime;
      state.datePickerTabMenu = {
        selected: { timePeriod: -1 },
        active: { timePeriod: -1 },
      };
      state.dateInNYCToday = initialState.dateInNYCToday;
      state.isAdvancingMonths = true;
    },
    reset: (state) => ({
      ...initialState,
      counterEndTime: state.counterEndTime ?? -1,
      datePicker: {
        day: getDate(new Date()),
        month: getMonth(new Date()),
        time: 0,
        year: getYear(new Date()),
      },
    }),
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
    setSelectedTimePeriod: (state, action: PayloadAction<number>) => {
      state.datePickerTabMenu.selected.timePeriod = action.payload;
    },
    setActiveTimePeriod: (state, action: PayloadAction<number>) => {
      state.datePickerTabMenu.active.timePeriod = action.payload;
    },
    setSelectedDay: (state, action: PayloadAction<number>) => {
      state.datePicker.day = action.payload;
    },
    setSelectedMonth: (state, action: PayloadAction<number>) => {
      state.datePicker.month = action.payload;
    },
    setSelectedYear: (state, action: PayloadAction<number>) => {
      state.datePicker.year = action.payload;
    },
    setSelectedDatePicker: (state, action: PayloadAction<ShopCartDate>) => {
      state.datePicker = action.payload;
    },
    setDateInNYCToday: (state, action: PayloadAction<DateTimeResponse>) => {
      state.dateInNYCToday = action.payload;
    },
    setSkipToBilling: (state, action: PayloadAction<boolean>) => {
      state.skipToBilling = action.payload;
    },
    setCounterEndTime: (state, action: PayloadAction<number | null>) => {
      state.counterEndTime = action.payload;
    },
    setIsAdvancingMonths: (state, action: PayloadAction<boolean>) => {
      state.isAdvancingMonths = action.payload;
    },
    setNextStep: (state) => {
      state.currentStep = state.currentStep + 1;
    },
    setPreviousStep: (state) => {
      state.currentStep =
        state.currentStep === 1 ? state.currentStep : state.currentStep - 1;
    },
    clearCounter: (state) => {
      state.counterEndTime = null;
    },
    setCancelRunningQueries: (state, action: PayloadAction<boolean>) => {
      state.cancelRunningQueries = action.payload;
    },
  },
});
