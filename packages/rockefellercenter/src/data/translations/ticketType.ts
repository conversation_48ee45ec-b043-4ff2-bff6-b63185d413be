import { Locale } from '../../utils';
import { Flow, StatGroup } from '../../services/viva/constants';

export type ModalCard = {
  title: string;
  subTitle: string;
  description: string;
  cta: {
    label: string;
    url: string;
  };
  link: {
    label: string;
    url: string;
  };
  image: {
    src: string;
    alt: string;
  };
};

export type TicketTypesForFlow = {
  sunset: string;
  continue: string;
  reserving: string;
  forFlow: {
    [key in Flow]: {
      copy: string;
      heroTitle?: string;
      description?: string;
      continueText?: string;
      stepTitle?: string;
      stepTitle2?: string;
      ticketStep?: {
        title: string;
        description?: string;
        ticketTypes?: [];
      };
      dateStep?: string;
      timeStep?: string;
      addOns?: string;
      modalCard?: ModalCard;
      modalCard2?: ModalCard;
      modalCard3?: ModalCard;
      link?: {
        label?: string;
        url?: string;
      };
      comboStep?: {
        tour?: {
          title?: string;
          description?: string;
        };
        deck?: {
          title?: string;
          description?: string;
        };
      };
    };
  };
  byStatGroup: {
    [key in StatGroup]: {
      label?: string;
      helpLabel?: string;
      errorMessage?: string;
    };
  };
};

export type TicketTypeTranslations = {
  [key in Locale]: TicketTypesForFlow;
};

export default {
  [Locale.EN_US]: {
    sunset: 'Ticket prices vary based on time of day.',
    continue: 'Continue',
    reserving: 'Reserving...',
    forFlow: {
      [Flow.TOR_VIP]: {
        copy: '{{ ticket | icon }}The VIP Pass includes: \n-A guided tour of Top of the Rock \n- Expedited entry and security checkpoint \n- Priority elevator access \n- The Beam Experience \n- The Photo Pass \n- 20% off at the Gift Shop',
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        timeStep: 'Tour Time',
        copy: "{{ ticket | icon }}The VIP Rock Pass includes: \n- A guided tour of Rockefeller Center and Top of the Rock Observation Deck \n- Exclusive access to one of Rockefeller Center's hidden rooftop gardens \n- Expedited entry and security checkpoint \n- Priority elevator access \n- The Beam Experience \n- The Photo Pass \n- 20% off at the Gift Shop",
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        timeStep: 'Tour Time',
        copy: "{{ ticket | icon }}The VIP Rock Pass: Holiday Edition includes: \n- A guided tour of Rockefeller Center and Top of the Rock Observation Deck \n- Exclusive access to one of Rockefeller Center's hidden rooftop gardens \n- Expedited entry and security checkpoint \n- Priority elevator access \n- The Beam Experience \n- The Photo Pass \n- 20% off at the Gift Shop",
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        heroTitle: 'Rockefeller Center Tour Jr - Kids & Family',
        copy: 'Rockefeller Center Tour Jr., a family-friendly version of the tour takes place on Saturdays and Sundays at 10:30am.',
        modalCard: {
          title: 'See it all. And learn it all',
          subTitle: '$56 per Adult',
          description:
            'Take the Rockefeller Center Tour, and learn about its incredible art and history, with a visit to all its major landmarks and artworks. Then visit Top of the Rock for the incredible 360-degree views of the NYC skyline. It’s two one-of-a-kind Rockefeller Center experiences in one.',
          cta: {
            label: 'Upgrade to rock pass',
            url: '/buy-tickets/rock-pass/',
          },
          link: {
            label: 'Learn more',
            url: '/special-offers/combo-tickets/rock-pass/',
          },
          image: {
            src: '../images/statue-left-top-rock-right.jpg',
            alt: 'A man looks out at a view of the Empire State Building from Top of the Rock.',
          },
        },
        modalCard2: {
          title: 'See it all. And learn it all',
          subTitle: '$56 per Adult',
          description:
            'Take the Rockefeller Center Tour, and learn about its incredible art and history, with a visit to all its major landmarks and artworks. Then visit Top of the Rock for the incredible 360-degree views of the NYC skyline. It’s two one-of-a-kind Rockefeller Center experiences in one.',
          cta: {
            label: 'Upgrade to rock pass',
            url: '/buy-tickets/rock-pass/',
          },
          image: {
            src: '../images/statue-left-top-rock-right.jpg',
            alt: 'A man looks out at a view of the Empire State Building from Top of the Rock.',
          },
        },
        modalCard3: {
          title: 'Desired time sold out?',
          description:
            'Upgrade to VIP and come at this time, even though it is sold out.',
          cta: {
            label: 'Get the vip access pass',
            url: '/buy-tickets/vip/',
          },
        },
      },
      [Flow.RINK]: {
        heroTitle: 'General Skate',
        description: 'Pricing varies by date and time of skate.',
        continueText:
          'All Tots must be accompanied by a skating Adult. All Children must be accompanied by either a Chaperone or a skating Adult.',
      },
      [Flow.RINK_MEMBERSHIP]: {
        heroTitle: 'Season Pass',
        copy: 'Make The Rink yours with unlimited skating all season long.',
      },
      [Flow.RINK_VIP]: {
        copy: '{{ ticket | icon }}The Premium Ticket includes: \n- Extended skate time of 90 minutes \n- Complimentary skate rental \n- 20% off rink merchandise',
        ticketStep: {
          title: 'Ticket Quantity',
          description:
            '{{ ticket | icon }}The Premium Ticket includes: \n- Extended skate time of 90 minutes  \n- Complimentary skate rental \n- 20% off rink merchandise',
          ticketTypes: [],
        },
      },
      [Flow.CHALETS]: {
        heroTitle: 'Holiday Chalets',
        stepTitle: 'Chalet Reservation',
        copy: 'A 50-minute reservation in your own private Après Skate Chalet Presented by Balsam Hill. Enjoy food and beverage service from Other Half Brewing while overlooking the iconic ice rink and Rockefeller Center Christmas Tree. 9am – 12:30pm reservations are subject to a $30 food and beverage minimum from the breakfast menu.',
      },
      [Flow.RC_ROCK_PASS]: {
        heroTitle: 'Rock Pass',
        stepTitle: 'Rock Pass Quantity',
      },
      [Flow.ON_THE_ROCKS]: {
        heroTitle: 'On the Rocks',
        stepTitle: 'On the Rocks Quantity',
      },
      [Flow.TREE_PHOTO]: {
        heroTitle: 'The Official Christmas Tree Photo',
        // stepTitle: 'Chalet Reservation',
        // copy: 'A 50-minute reservation in your own private Après Skate Chalet Presented by Balsam Hill. Enjoy food and beverage service from Other Half Brewing while overlooking the iconic ice rink and Rockefeller Center Christmas Tree.',
      },
      [Flow.SANTA_PHOTO]: {
        heroTitle: 'Santa Photo Experience',
        // stepTitle: 'Chalet Reservation',
        // copy: 'A 50-minute reservation in your own private Après Skate Chalet Presented by Balsam Hill. Enjoy food and beverage service from Other Half Brewing while overlooking the iconic ice rink and Rockefeller Center Christmas Tree.',
      },
    },
    byStatGroup: {
      [StatGroup.GA_ADULT]: {
        label: 'Adult',
        helpLabel: '(Ages 13+)',
      },
      [StatGroup.GA_CHILD]: {
        label: 'Child',
        helpLabel: '(Ages 6-12)',
      },
      [StatGroup.GA_SENIOR]: {
        label: 'Senior',
        helpLabel: '(Ages 62+)',
      },
      [StatGroup.ON_THE_ROCKS]: {
        label: 'On the Rocks',
      },
      [StatGroup.RINK_ADULT]: {
        label: 'Adult',
        helpLabel: 'Ages 13+ Adult Rink Admission',
      },
      [StatGroup.RINK_CHAPERONE]: {
        label: 'Chaperone',
        helpLabel: 'Non-Skating Admission for accompanying Child Skate',
      },
      [StatGroup.RINK_CHILD]: {
        label: 'Child',
        helpLabel: 'Ages 6-12 years old (Requires Adult or Chaperone)',
        errorMessage: '* 1 Adult or Chaperone is required',
      },
      [StatGroup.RINK_TODDLER]: {
        label: 'Tot',
        helpLabel: 'Ages 5 & Under (Requires Adult ticket)',
        errorMessage: '* 1 Adult is required',
      },
      [StatGroup.RINK_MEMBERSHIP]: {
        helpLabel: '',
      },
    },
  },
} as unknown as TicketTypeTranslations;
