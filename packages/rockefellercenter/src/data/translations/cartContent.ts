import {
  Flow,
  StatGroup,
  FlowPrettyPrint,
} from '../../services/viva/constants';
import { Locale } from '../../utils';

export type ContentTranslationsForFlow = {
  [key in Flow | 'addOns']: {
    title: string;
    ticketTypes: {
      [key in StatGroup]?: {
        title: string;
      };
    } & {
      title: string;
    };
  };
};

export type CartContentTranslationsForFlow = {
  title?: string;
  ticketTypes: {
    [key in StatGroup]?: {
      title: string;
    };
  };
  cartModalFooter: {
    upsellToAnotherFlow: string;
  };
};

export type CartContentTranslationsForLocale = {
  byFlow: {
    [key in Flow]: CartContentTranslationsForFlow;
  };
  title: string;
  cartModalFooter: {
    checkout: string;
    viewCart: string;
    upsellToAnotherFlow: string;
  };
  addOns: CartContentTranslationsForFlow;
};

export type CartContentTranslations = {
  [key in Locale]: CartContentTranslationsForLocale;
};

export default {
  [Locale.EN_US]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Adult',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Child',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Senior',
          },
        },
      },
      [Flow.ON_THE_ROCKS]: {
        title: 'On the Rocks',
        ticketTypes: {
          [StatGroup.ON_THE_ROCKS]: {
            title: 'On the Rocks',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Child',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Senior',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Child',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Child',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Toddler',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Chaperone',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Top of the Rock Tickets',
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.RINK_VIP]: {
        title: FlowPrettyPrint[Flow.RINK_VIP],
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: FlowPrettyPrint[Flow.RINK_VIP],
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Top of the Rock Tickets',
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Photo Package',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Skate Rental',
        },
        [StatGroup.BEANIE]: {
          title: 'Beanie',
        },
        [StatGroup.ORNAMENT]: {
          title: 'Rockefeller Center Ornament',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Hot Cocoa',
        },
        [StatGroup.TREE_PHOTO]: {
          title: 'Tree Photo',
        },
        [StatGroup.SANTA_PHOTO]: {
          title: 'Santa Photo Experience',
        },
        [StatGroup.BUNNY_PHOTO]: {
          title: 'Easter Bunny Photo Pass',
        },
        [StatGroup.RESTAURANT_JUPITER]: {
          title: '3 Course Meal at Jupiter',
        },
        [StatGroup.FIVE_ACRES]: {
          title: '5 Acres',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.DE_DE]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Erw',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Kind',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Senior',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Erw',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Kind',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Senior',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Child',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Erwachsene',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Kind',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Kleinkind',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Aufsichtsperson',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP-Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: FlowPrettyPrint[Flow.RINK_VIP],
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'VIP-Ticket',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Fotopass',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Leih-Schlittschuhe',
        },
        [StatGroup.BEANIE]: {
          title: 'Beanie-Mütze',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Kekse und heißer Kakao',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.ES_US]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Adulto',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Niño',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Jubilado',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Adulto',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Niño',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Jubilado',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Pases Rock',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Pases Rock',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Child',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Adultos',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Niños',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Niños pequeños',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Acompañante',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'El Pase VIP',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: 'Entrada VIP',
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'Entrada VIP',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Pase fotográfico',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Alquiler de patines',
        },
        [StatGroup.BEANIE]: {
          title: 'Gorro',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Galletas y chocolate caliente',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.FR_FR]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Adulte',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Enfant',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Sénior',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Adulte',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Enfant',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Sénior',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Child',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Adulte',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Enfant',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Jeune enfant',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Accompagnateur',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: 'Billet VIP',
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'Billet VIP',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Pass Photos',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Location des patins',
        },
        [StatGroup.BEANIE]: {
          title: 'Bonnet',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Biscuit et chocolat chaud',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.IT_IT]: {
    title: 'Il tuo carrello',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Adulto',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Bambino',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Oltre 65 anni',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Adulto',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Bambino',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Oltre 65 anni',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adult',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Child',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Adulti',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Ragazzi',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Bambini',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Accompagnatore',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: 'Biglietto VIP',
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'Biglietto VIP',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'Rock Pass VIP',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'Rock Pass VIP',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Pass fotografico',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Noleggio pattini',
        },
        [StatGroup.BEANIE]: {
          title: 'Berretto',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Cioccolata calda con biscotti',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.JA_JP]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: '大人',
          },
          [StatGroup.ALLINPSKYC]: {
            title: '子供',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'シニア',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: '大人',
          },
          [StatGroup.GA_CHILD]: {
            title: '子供',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'シニア',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: '大人',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: '子供',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: '大人',
          },
          [StatGroup.RINK_CHILD]: {
            title: '子供',
          },
          [StatGroup.RINK_TODDLER]: {
            title: '幼児',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: '付き添い',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: 'VIPチケットの特典',
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'VIPチケットの特典',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'フォトパス',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'スケート靴レンタル',
        },
        [StatGroup.BEANIE]: {
          title: 'ビーニーハット',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'クッキーとホットココア',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.KO_KR]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: '성인',
          },
          [StatGroup.ALLINPSKYC]: {
            title: '어린이',
          },
          [StatGroup.ALLINPSKYS]: {
            title: '노인',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: '성인',
          },
          [StatGroup.GA_CHILD]: {
            title: '어린이',
          },
          [StatGroup.GA_SENIOR]: {
            title: '노인',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: '성인',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: '어린이',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: '성인',
          },
          [StatGroup.RINK_CHILD]: {
            title: '어린이',
          },
          [StatGroup.RINK_TODDLER]: {
            title: '유아',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: '보호자',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: 'VIP 티켓',
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: 'VIP 티켓',
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Photo Pass',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: '스케이트 대여',
        },
        [StatGroup.BEANIE]: {
          title: '비니',
        },
        [StatGroup.HOT_COCOA]: {
          title: '쿠키 및 핫초코',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.PT_BR]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: 'Adulto',
          },
          [StatGroup.ALLINPSKYC]: {
            title: 'Criança',
          },
          [StatGroup.ALLINPSKYS]: {
            title: 'Idoso',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: 'Adulto',
          },
          [StatGroup.GA_CHILD]: {
            title: 'Criança',
          },
          [StatGroup.GA_SENIOR]: {
            title: 'Idoso',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: 'Adulto',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: 'Criança',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: 'Adultos',
          },
          [StatGroup.RINK_CHILD]: {
            title: 'Crianças',
          },
          [StatGroup.RINK_TODDLER]: {
            title: 'Bebês',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: 'Acompanhante',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: FlowPrettyPrint[Flow.RINK_VIP],
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: FlowPrettyPrint[Flow.RINK_VIP],
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Photo Pass',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: 'Locação de patins',
        },
        [StatGroup.BEANIE]: {
          title: 'Gorro',
        },
        [StatGroup.HOT_COCOA]: {
          title: 'Cookie e chocolate quente',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.ZH_CN]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: '成年人',
          },
          [StatGroup.ALLINPSKYC]: {
            title: '儿童',
          },
          [StatGroup.ALLINPSKYS]: {
            title: '老年人',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: '成年人',
          },
          [StatGroup.GA_CHILD]: {
            title: '儿童',
          },
          [StatGroup.GA_SENIOR]: {
            title: '老年人',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: '成年人',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: '儿童',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: '成人',
          },
          [StatGroup.RINK_CHILD]: {
            title: '儿童',
          },
          [StatGroup.RINK_TODDLER]: {
            title: '幼儿',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: '陪伴人',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: FlowPrettyPrint[Flow.RINK_VIP],
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: FlowPrettyPrint[Flow.RINK_VIP],
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Photo Pass',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: '冰鞋租借',
        },
        [StatGroup.BEANIE]: {
          title: '便帽',
        },
        [StatGroup.HOT_COCOA]: {
          title: '曲奇与热可可',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  [Locale.ZH_TW]: {
    title: 'Your Cart',
    cartModalFooter: {
      checkout: 'Checkout',
      viewCart: 'View Cart',
      upsellToAnotherFlow: 'Add More Tickets',
    },
    byFlow: {
      [Flow.ALL_IN_PASS]: {
        title: 'All In Pass',
        ticketTypes: {
          [StatGroup.ALLINPSKYA]: {
            title: '成人',
          },
          [StatGroup.ALLINPSKYC]: {
            title: '兒童',
          },
          [StatGroup.ALLINPSKYS]: {
            title: '年長者',
          },
        },
      },
      [Flow.TOR_GA]: {
        title: 'Top of the Rock Observation Deck',
        ticketTypes: {
          [StatGroup.GA_ADULT]: {
            title: '成人',
          },
          [StatGroup.GA_CHILD]: {
            title: '兒童',
          },
          [StatGroup.GA_SENIOR]: {
            title: '年長者',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.THE_BEAM_PASS]: {
        title: 'The Beam Pass',
        ticketTypes: {
          [StatGroup.THE_BEAM_PASS]: {
            title: 'The Beam Pass',
          },
        },
        cartModalFooter: {
          upsellToAnotherFlow: 'Add Rink Tickets',
        },
      },
      [Flow.TOR_EXPRESS]: {
        title: 'Top of the Rock Express Pass',
        ticketTypes: {
          [StatGroup.TOR_XPASS]: {
            title: 'Top of the Rock Express Pass',
          },
        },
      },
      [Flow.RC_TOUR]: {
        title: 'Rockefeller Center Tour',
        ticketTypes: {
          [StatGroup.RC_TOUR]: {
            title: 'Tour',
          },
        },
      },
      [Flow.RC_ROCK_PASS]: {
        title: 'Rock Pass',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS]: {
            title: 'Rock Pass',
          },
        },
      },
      [Flow.CITY_PASS]: {
        title: 'CityPASS®',
        ticketTypes: {
          [StatGroup.CITY_PASS_ADULT]: {
            title: '成人',
          },
          [StatGroup.CITY_PASS_CHILD]: {
            title: '兒童',
          },
        },
      },
      [Flow.RINK]: {
        title: 'The Rink',
        ticketTypes: {
          [StatGroup.RINK_ADULT]: {
            title: '成年人',
          },
          [StatGroup.RINK_CHILD]: {
            title: '兒童',
          },
          [StatGroup.RINK_TODDLER]: {
            title: '嬰孩',
          },
          [StatGroup.RINK_CHAPERONE]: {
            title: '陪伴人',
          },
        },
      },
      [Flow.TOR_VIP]: {
        title: 'VIP Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP]: {
            title: 'VIP Pass',
          },
        },
      },
      [Flow.RINK_VIP]: {
        title: FlowPrettyPrint[Flow.RINK_VIP],
        ticketTypes: {
          [StatGroup.RINK_VIP]: {
            title: FlowPrettyPrint[Flow.RINK_VIP],
          },
        },
      },
      [Flow.RINK_MEMBERSHIP]: {
        title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
        ticketTypes: {
          [StatGroup.RINK_MEMBERSHIP]: {
            title: FlowPrettyPrint[Flow.RINK_MEMBERSHIP],
          },
          [StatGroup.RINK_MEM_WITH_SKATES]: {
            title: FlowPrettyPrint[Flow.RINK_MEM_WITH_SKATES],
          },
        },
      },
      [Flow.CHALETS]: {
        title: 'Chalets',
        ticketTypes: {
          [StatGroup.CHALETS]: {
            title: 'Chalets',
          },
        },
      },
      [Flow.RC_TOUR_KIDS_AND_FAMILY]: {
        title: 'Rockefeller Center Tour Jr.',
        ticketTypes: {
          [StatGroup.RC_TOUR_KIDS_AND_FAMILY]: {
            title: 'Rockefeller Center Tour Jr.',
          },
        },
      },
      [Flow.TOR_VIP_ROCKSTAR]: {
        title: 'VIP Rock Pass',
        ticketTypes: {
          [StatGroup.TOR_VIP_ROCKSTAR]: {
            title: 'VIP Rock Pass',
          },
        },
      },
      [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: {
        title: 'VIP Rock Pass: Holiday Edition',
        ticketTypes: {
          [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
            title: 'VIP Rock Pass: Holiday Edition',
          },
        },
      },
      [Flow.TREE_PHOTO]: {
        title: 'Tree Photo',
        ticketTypes: {
          [StatGroup.TREE_PHOTO]: {
            title: 'Tree Photo',
          },
        },
      },
      [Flow.SANTA_PHOTO]: {
        title: 'Santa Photo Experience',
        ticketTypes: {
          [StatGroup.SANTA_PHOTO]: {
            title: 'Santa Photo Experience',
          },
        },
      },
    },
    addOns: {
      title: 'Enhance your experience',
      ticketTypes: {
        [StatGroup.THE_BEAM]: {
          title: 'The Beam',
        },
        [StatGroup.PHOTO_PACKAGE]: {
          title: 'Photo Pass',
        },
        [StatGroup.CHAMPAGNE_TOAST]: {
          title: 'Champagne Toast',
        },
        [StatGroup.SKATE_RENTAL]: {
          title: '溜冰鞋租賃',
        },
        [StatGroup.BEANIE]: {
          title: '毛線帽',
        },
        [StatGroup.HOT_COCOA]: {
          title: '餅乾與熱可可',
        },
        [StatGroup.SKYLIFT]: {
          title: 'Skylift',
        },
      },
    },
  },
  // TODO: Translate below
} as CartContentTranslations;
