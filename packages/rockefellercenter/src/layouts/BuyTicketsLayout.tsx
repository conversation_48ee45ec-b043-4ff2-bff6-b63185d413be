/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, ThemeProvider, getThemeByName } from '@tishman/components';
import React, {
  createContext,
  Fragment,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Global } from '@emotion/react';
import { parse, startOfDay } from 'date-fns';
import { navigate } from 'gatsby';
import { getWindow } from '@hzdg/dom-utils';

import { Translatable, useTranslations } from '../utils/use-translations';
import { prepareTicketStepData } from '../buy-tickets/utils';
import { SessionExpiredModal } from '../components/BuyTickets/Modals/Cart/SessionExpiredModal';
import { CountdownTimerProvider } from '../utils/countdown-timer-context-provider';
import { getAvailabilities } from '../components/BuyTickets/SegmentedTimePicker/Availabilities/utils';
import { Oljs } from '../components/Oljs';
import { TicketsErrorModal } from '../components/BuyTickets/Modals/TicketsErrorModal';
import {
  BuyTicketsModal,
  BuyTicketsModalProvider,
  useBuyTicketsModal,
} from '../components/BuyTickets';
import {
  useCheckBasketMutation,
  useFindAllPerformanceByCategoriesQuery,
  useFlowProductsQuery,
  useGetDayPerformancesMutation,
  useGetDaysAvailabilityQuery,
  useFindAllProductByStatGroup,
} from '../services/viva';
import {
  OrderInitializationContextProvider,
  OrderInitializationContextProviderProps,
} from '../utils/order-initialization-context-provider';
import {
  putInArray,
  sanitizeAndSortProductsToTicketTypes,
  sanitizeProductsToTicketTypes,
} from '../services/viva/utils';
import {
  CHALET_AK,
  COMBO_FLOWS,
  DYNAMIC_PRICING_FLOWS,
  Flow,
  TIERED_FLOWS,
  checkAdaptivePricing,
  DiscountedStatGroupByFlow,
  StatGroup,
  PerformanceCategory,
} from '../services/viva/constants';
import {
  actions,
  selectFlow,
  selectPerformances,
  selectShopCartDate,
  selectShopCartSelections,
  useAppDispatch,
  useAppSelector,
  selectWizardDatePicker,
  selectEvent,
  selectPerformancesForFlow,
  selectShopCartAddOnSelections,
  selectShopCartDateTime,
  selectWizardCurrentStep,
} from '../store';
import { useGetEditQueryParams } from '../utils/use-ticket-edit-mode';
import useAutomaticCompAddon from '../utils/use-automatic-comp-addon';
import { getDateString } from '../utils/format-date';
import { newRelic } from '../utils/new-relic';

import { useReloadSessionStorage } from './useReloadSessionStorage';

import type { ThemeUIStyleObject } from 'theme-ui';
import type { TicketStepProps } from '../blocks/VivaBuyTickets';
import type {
  PerformanceResponseObject,
  TicketType,
} from '../services/viva/types';
import type { ComponentPropsWithoutRef } from 'react';
import type { TishmanThemeName } from '@tishman/components';
import type {
  AddOnItem,
  BuyTicketsVariantPageData,
} from '../components/BuyTickets';
import type { TicketTypesForFlow } from '../data/translations/ticketType';
import type { ProductTicketType } from '../services/viva/types/response';
import type { Availability } from '../components/BuyTickets/SegmentedTimePicker/Availabilities';

const OLD_VIP_TICKET_TYPE_ID = 100;

type LayoutProps = {
  sx?: ThemeUIStyleObject;
  theme?: TishmanThemeName;
  /** An optional map of styled components to use for rich text, etc. */
  components?: Record<string, React.ComponentType>;
  data: BuyTicketsVariantPageData;
  flow: Flow;
} & Omit<OrderInitializationContextProviderProps, 'children'> &
  ComponentPropsWithoutRef<'main'>;

type BuyTicketsLayoutContextType = {
  tickets?: TicketType[];
  sunsetTickets?: TicketType[];
  ticketStepData?: TicketStepProps;
  heroData?: {
    title?: string;
    description?: string;
    link?: {
      label?: string;
      modal?: string;
      url?: string;
    };
  };
  getDaysAvailabilityQuery?: ReturnType<typeof useGetDaysAvailabilityQuery>;
  getDayPerformances?: (
    dateString: string,
    _comboIndex?: number,
  ) => Promise<PerformanceResponseObject[]>;
  dayAvailabilities?: Availability[];
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isAddOnLoading: boolean;
  setIsAddOnLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isSunsetSlotSelected: boolean;
  selectedTimePeriod: number;
  setSelectedTimePeriod: React.Dispatch<React.SetStateAction<number>>;
  isTimeSlotsAnimating: boolean;
  setIsTimeSlotsAnimating: React.Dispatch<React.SetStateAction<boolean>>;
};

export const BuyTicketsLayoutContext =
  createContext<BuyTicketsLayoutContextType>({
    isLoading: false,
    setIsLoading: () => {
      /* noop */
    },
    isAddOnLoading: false,
    setIsAddOnLoading: () => {
      /* noop */
    },
    isSunsetSlotSelected: false,
    selectedTimePeriod: 0,
    setSelectedTimePeriod: () => {
      /* noop */
    },
    isTimeSlotsAnimating: false,
    setIsTimeSlotsAnimating: () => {
      /* noop */
    },
  });

const RETRY_LIMIT = 6;

const BuyTicketsLayoutProvider = ({
  children,
  data,
  flow,
}: {
  data: BuyTicketsVariantPageData;
  children: React.ReactNode;
  flow: Flow;
}) => {
  // TODO: Remove after testing for now
  // useTieredPricing();

  const shopCartDate = useAppSelector(selectShopCartDate);
  const shopCartDateTime = useAppSelector(selectShopCartDateTime);
  const orderBosEvent = useAppSelector(selectEvent);
  const wizardDatePicker = useAppSelector(selectWizardDatePicker);
  const stepNumber = useAppSelector(selectWizardCurrentStep);
  const selectedPerformances = useAppSelector(selectPerformances);
  const selectedPerformancesForFlow = useAppSelector(selectPerformancesForFlow);
  const currentShopCartAddOnSelections = useAppSelector(
    selectShopCartAddOnSelections,
  );
  const comboIndex = useAppSelector((state) => state.order.combo.comboIndex);
  const products = useFlowProductsQuery();
  const checkBasketMutation = useCheckBasketMutation(flow);
  const dispatch = useAppDispatch();
  const { editMode } = useGetEditQueryParams();
  const translations = useTranslations<TicketTypesForFlow>(
    Translatable.TicketType,
  );
  const translatedTitle = translations?.forFlow[flow]?.heroTitle ?? '';

  const [selectedTimePeriod, setSelectedTimePeriod] = useState(0);
  const [isTimeSlotsAnimating, setIsTimeSlotsAnimating] = useState(false);
  const [performanceList, setPerformanceList] = useState<
    PerformanceResponseObject[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddOnLoading, setIsAddOnLoading] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isInitialFutureMonth, setIsInitialFutureMonth] = useState(true);
  const [shouldFetchNextMonth, setShouldFetchNextMonth] = useState(false);
  const [shouldFetchRetries, setShouldFetchRetries] = useState(RETRY_LIMIT);
  const [isFirstAvailableDateSet, setIsFirstAvailableDateSet] = useState(false);

  const memoizedDate = useMemo(
    () =>
      `${shopCartDate.year}-${String(shopCartDate.month + 1).padStart(
        2,
        '0',
      )}-${String(shopCartDate.day).padStart(2, '0')}`,
    [shopCartDate.year, shopCartDate.month, shopCartDate.day],
  );

  // Add URL parameter handling
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const dateParam = urlParams.get('date');
    const periodParam = urlParams.get('period');

    if (dateParam) {
      const [year, month, day] = dateParam.split('-').map(Number);
      if (year && month && day) {
        // Update shop cart date
        dispatch(
          actions.order.setShopCartDate({
            day,
            month: month - 1, // Convert to 0-based month
            year,
            time: 0,
          }),
        );
        dispatch(
          actions.order.setShopCartDateTime(
            new Date(year, month - 1, day).valueOf(),
          ),
        );
        setIsFirstAvailableDateSet(true);

        // // Update wizard date picker
        dispatch(actions.wizard.setSelectedYear(year));
        dispatch(actions.wizard.setSelectedMonth(month - 1)); // Convert to 0-based month
        dispatch(actions.wizard.setSelectedDay(day));
      }
    }

    if (periodParam) {
      const period = parseInt(periodParam, 10);
      if (!isNaN(period)) {
        setSelectedTimePeriod(period);
      }
    }
  }, []);

  useEffect(() => {
    // This is for reloading the performances when the dateParam is set, originally done for the 4th of July feature
    const window = getWindow();

    const dateParam =
      window?.location.search &&
      new URLSearchParams(window.location.search).get('date');

    if (dateParam) {
      getDayPerformances?.(memoizedDate);
    }
  }, [memoizedDate, stepNumber]);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const urlParams = new URLSearchParams(window.location.search);
    const periodParam = urlParams.get('period');

    if (periodParam) {
      const period = parseInt(periodParam, 10);
      if (!isNaN(period)) {
        const tabEl = document.getElementById(`-faq-tab-${period}`);
        tabEl?.click();
      }
    }
  }, [performanceList]);

  const hasAdaptivePricing = checkAdaptivePricing(flow);

  const { isLoading: isCompAddOnLoading } = useAutomaticCompAddon();
  const getDaysAvailabilityQuery = useGetDaysAvailabilityQuery({
    bosEvent: orderBosEvent ?? undefined,
    fetchNextMonth: shouldFetchNextMonth,
    monthToFetch: wizardDatePicker.month,
  });

  const sunsetPerformancesQuery = useFindAllPerformanceByCategoriesQuery({
    categories: [PerformanceCategory.TOR_GA_SUNSET],
    date: memoizedDate,
    options: {
      enabled: [Flow.ALL_IN_PASS, Flow.TOR_GA].includes(flow),
    },
  });

  const fireworksPerformancesQuery = useFindAllPerformanceByCategoriesQuery({
    categories: [PerformanceCategory.FIREWORKS],
    date: memoizedDate,
    options: {
      enabled: [Flow.ALL_IN_PASS, Flow.TOR_GA, shopCartDate.day === 4].includes(
        flow,
      ),
    },
  });

  const findAllPerformanceByCategoriesQuery =
    useFindAllPerformanceByCategoriesQuery({
      categories: [PerformanceCategory.ALL_IN_PASS_POST_PEAK],
      date: memoizedDate,
    });

  // Get discounted product data if we have a flow and it has discounted stat groups
  const discountedStatGroups = flow
    ? DiscountedStatGroupByFlow[flow as keyof typeof DiscountedStatGroupByFlow]
    : undefined;
  const flattenedStatGroups = discountedStatGroups?.flat() as
    | StatGroup[]
    | undefined;
  const discountedProductsQuery = useFindAllProductByStatGroup({
    statGroups: flattenedStatGroups || [],
    enabled: true,
  });

  const heroData = {
    ...data?.page?.hero,
    title: translatedTitle || data?.page?.hero?.title || '',
    link: {
      label: data?.page?.hero?.link?.label ?? undefined,
      modal: data?.page?.hero?.link?.modal ?? undefined,
      url: data?.page?.hero?.link?.url ?? undefined,
    },
  };

  const sanitizedTickets = TIERED_FLOWS.includes(flow)
    ? sanitizeAndSortProductsToTicketTypes(
        products?.data?.PRODUCTLIST?.PRODUCT ?? [],
        flow,
      )
    : sanitizeProductsToTicketTypes(products?.data?.PRODUCTLIST?.PRODUCT ?? []);

  const tickets =
    flow === Flow.CHALETS
      ? sanitizedTickets.filter((t) => t.productAK === CHALET_AK)
      : sanitizedTickets;

  const ticketStepData = prepareTicketStepData({
    flow,
    data,
    tickets,
  });

  const modal = useBuyTicketsModal();

  const handleError = useCallback(
    (error: Error) => {
      modal.setValue(error);
      setIsLoading(false);
      modal.setIsOpen(true); // Explicitly set modal to open
    },
    [modal],
  );

  const getUpdatedTickets = useCallback(async () => {
    if (!tickets?.length || !performanceList?.length) return;

    const performancesWithAvailability = performanceList.filter(
      (performance) => parseInt(performance.AVAILABILITY.AVAILABLE) > 0,
    );

    if (!performancesWithAvailability.length) return;

    try {
      setIsLoading(true);
      const basket = await checkBasketMutation.mutateAsync({
        items: [
          {
            AK: tickets[0].productAK,
            QTY: 1,
            performances: [performancesWithAvailability[0].AK],
          },
        ],
        capacityManagement: false,
      });

      const updatedProducts = putInArray(basket?.ITEMLIST?.ITEM);

      updatedProducts?.forEach((element) => {
        if (element?.AK) {
          // TODO: Remove after testing for now
          // dispatch(
          //   actions.order.updateShopCartSelectionsPrice({
          //     productAK: element.AK,
          //     price: parseFloat((element.PRICE.NET * 100).toFixed(2)),
          //     tax: parseFloat((element.PRICE.TAX * 100).toFixed(2)),
          //   }),
          // );
        }
      });
    } catch (error) {
      console.error('Error updating tickets:', error);
      handleError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [tickets, performanceList, handleError]);

  // This effect is responsible for restoring session state when the page is reloaded.
  // This ensures that the user is placed back into the correct state after a page refresh, without duplicating tickets.
  useReloadSessionStorage();

  // Log when the user has navigated away from the flow
  useEffect(() => {
    if (!window) return;

    const callback = () => {
      newRelic.logCartAction('Leaving Buy Tickets flow', 'beforeunload');
    };

    window.addEventListener('beforeunload', callback);

    return () => window.removeEventListener('beforeunload', callback);
  }, []);

  // useEffect(() => {
  //   if (DYNAMIC_PRICING_FLOWS.includes(flow)) {
  //     getUpdatedTickets();
  //   }
  // }, [flow, selectedPerformances]);

  useEffect(() => {
    if (DYNAMIC_PRICING_FLOWS.includes(flow)) {
      getUpdatedTickets();
    }

    // If we change the selected performance then we need to set add-on quantities
    // to 0 for add-ons in the shopCart AddOnSelections.
    // This helps protect when we choose a performance that has different availability
    // for the add-ons.
    const updatedAddOns = currentShopCartAddOnSelections.map((addon) => {
      const addOnItemDTO: AddOnItem = {
        ...addon,
        quantity: 0,
      };
      return addOnItemDTO;
    });

    dispatch(actions.order.setShopCartAddOnSelections(updatedAddOns));
    dispatch(
      actions.checkout.setAddOns({
        addOns: updatedAddOns,
        dateTime: shopCartDateTime,
        flow,
      }),
    );
  }, [flow, selectedPerformancesForFlow]);

  useEffect(() => {
    setIsAddOnLoading(isCompAddOnLoading);
  }, [isCompAddOnLoading]);

  const dayPerformanceMutation = useGetDayPerformancesMutation();
  const getDayPerformances = useCallback(
    async (
      dateString: string,
      _comboIndex?: number,
    ): Promise<PerformanceResponseObject[]> => {
      try {
        setIsLoading(true);

        const dateStringSplit = dateString
          .split('-')
          .map((str) => parseInt(str));
        const date = getDaysAvailabilityQuery.data?.get(dateString)?.DATE;

        if (!date) return [];

        const response = await dayPerformanceMutation.mutateAsync({
          day: dateStringSplit[2],
          month: dateStringSplit[1],
          year: dateStringSplit[0],
          _comboIndex,
        });

        setPerformanceList(response);

        // Express Pass has no time picker, so we need to set the performance AKs
        if (flow === Flow.TOR_EXPRESS && response?.[0]?.AK) {
          const performances = {
            [flow]: [`${response[0].AK}`],
          } as { [key in Flow]: string[] };

          dispatch(actions.order.setPerformances({ data: performances, flow }));
        }
        return response;
      } catch (err) {
        console.error('Error in getDayPerformances:', err);
        handleError(err as Error);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [dayPerformanceMutation, getDaysAvailabilityQuery.data, handleError],
  );

  // Get performances for combo steps
  useEffect(() => {
    if (!COMBO_FLOWS.includes(flow)) return;

    if (
      getDaysAvailabilityQuery.isSuccess &&
      !getDaysAvailabilityQuery.isLoading
    ) {
      const formattedDate = `${shopCartDate.year}-${String(
        shopCartDate.month + 1,
      ).padStart(2, '0')}-${String(shopCartDate.day).padStart(2, '0')}`;
      getDayPerformances(formattedDate);
    }
  }, [
    comboIndex,
    flow,
    getDaysAvailabilityQuery.isSuccess,
    getDaysAvailabilityQuery.isLoading,
    shopCartDate.year,
    shopCartDate.month,
    shopCartDate.day,
  ]);

  const dayAvailabilities = useMemo(() => {
    // selectedDateTime is an epoch time
    // if the performance list is not empty, get them and map them to our internal availability type
    const { availabilities } =
      performanceList !== undefined && performanceList.length > 0
        ? getAvailabilities(
            performanceList,
            flow,
            {
              discountedPerformances: findAllPerformanceByCategoriesQuery.data,
              discountedProducts:
                discountedProductsQuery.data?.PRODUCTLIST?.PRODUCT,
            },
            sunsetPerformancesQuery.data,
            fireworksPerformancesQuery.data,
          )
        : { availabilities: [] };
    return availabilities as Availability[];
  }, [
    performanceList,
    findAllPerformanceByCategoriesQuery.data,
    discountedProductsQuery.data,
    sunsetPerformancesQuery.data,
    fireworksPerformancesQuery.data,
    flow,
  ]);
  // Temporary data for isSunsetSlotSelected until sunset pricing returns
  const isSunsetSlotSelected = false;

  // Updates the Redux store with the current set of available ticket products for the selected flow
  useEffect(() => {
    const baseProducts = tickets.map((ticket) => ({
      ...ticket,
      priceRange:
        ticket.type === 'chaperone' ? { high: 0, low: 0 } : ticket.priceRange,
    }));
    dispatch(
      actions.order.setFlowBaseProducts(baseProducts as ProductTicketType[]),
    );
  }, [products.data]);

  // First useEffect for initial data fetching and retry logic
  useEffect(() => {
    if (
      !isInitialLoad ||
      !getDaysAvailabilityQuery.isSuccess ||
      getDaysAvailabilityQuery.isLoading
    ) {
      return;
    }

    // Check if there are any available dates in current month
    const hasAvailability = Array.from(
      getDaysAvailabilityQuery.data?.values() ?? [],
    ).some((day) => {
      const [year, month] = day.DATE.split('-').map(Number);
      return (
        day.available &&
        month === wizardDatePicker.month + 1 &&
        year === wizardDatePicker.year
      );
    });

    // If NO availability (either no data returned or no available dates) and we still have retries, continue cycling
    if (!hasAvailability && shouldFetchRetries > 0) {
      // First check if we need to increment the year
      const nextMonth = (wizardDatePicker.month + 1) % 12;
      if (nextMonth === 0) {
        // We're moving from December to January, increment the year first
        dispatch(actions.wizard.setSelectedYear(wizardDatePicker.year + 1));
      }

      // Then update the month
      dispatch(actions.wizard.setSelectedMonth(nextMonth));

      setShouldFetchNextMonth(true);
      setShouldFetchRetries((prev) => prev - 1);
    } else {
      setIsInitialLoad(false);
    }
  }, [
    wizardDatePicker.month,
    wizardDatePicker.year,
    getDaysAvailabilityQuery.data,
    getDaysAvailabilityQuery.isSuccess,
    getDaysAvailabilityQuery.isLoading,
    shouldFetchRetries,
    isInitialLoad,
  ]);

  const getDayPerformancesOnShopCartDate = () => {
    const { day, month, year } = shopCartDate;
    getDayPerformances(getDateString(year, month, day));
  };

  // Second useEffect for handling successful data fetch and updates
  useEffect(() => {
    if (
      !getDaysAvailabilityQuery.isSuccess ||
      getDaysAvailabilityQuery.isLoading ||
      (getDaysAvailabilityQuery.data?.size ?? 0) === 0
    ) {
      return;
    }
    // const dataSize = getDaysAvailabilityQuery.data?.size ?? 0;

    // if (dataSize > 0) {
    const firstDay = getDaysAvailabilityQuery.data.values().next().value;
    if (!firstDay) return;

    const firstAvailableDate = startOfDay(
      parse(firstDay.DATE, 'yyyy-MM-dd', new Date()),
    );

    if (!isInitialLoad && !editMode) {
      if (comboIndex === 0) {
        if (!isFirstAvailableDateSet) {
          dispatch(
            actions.order.setShopCartDate({
              day: firstAvailableDate.getDate(),
              month: firstAvailableDate.getMonth(),
              year: firstAvailableDate.getFullYear(),
              time: 0,
            }),
          );
          dispatch(
            actions.order.setShopCartDateTime(firstAvailableDate.valueOf()),
          );
          setIsFirstAvailableDateSet(true);

          newRelic.logCartAction(
            'BuyTicketsLayout - Set shopping cart date',
            'setShopCartDate',
            {
              shopCartDate: {
                day: firstAvailableDate.getDate(),
                month: firstAvailableDate.getMonth(),
                year: firstAvailableDate.getFullYear(),
                time: 0,
              },
            },
          );
          newRelic.logCartAction(
            'BuyTicketsLayout - Set shopping cart datetime',
            'setShopCartDateTime',
            firstAvailableDate.valueOf(),
          );

          getDayPerformances(firstDay.DATE)
            .then((response: PerformanceResponseObject[] | void) => {
              if (hasAdaptivePricing && response) {
                putInArray(response[0]?.PRODUCTLIST?.PRODUCT).forEach(
                  (product) => {
                    dispatch(
                      actions.order.updateShopCartSelectionsPrice({
                        productAK: product?.AK,
                        price: product?.PRICE * 100,
                        tax: 0,
                      }),
                    );
                  },
                );
              }
            })
            .catch((error) => {
              console.error('Error in getDayPerformances:', error);
              handleError(error as Error);
            });
          setIsInitialFutureMonth(false);
        }
        return;
      }
    } else if (editMode) {
      getDayPerformancesOnShopCartDate();
    }

    setIsInitialLoad(false);
    setShouldFetchNextMonth(false);
  }, [
    getDaysAvailabilityQuery.isLoading,
    getDaysAvailabilityQuery.isSuccess,
    getDaysAvailabilityQuery.data,
    isInitialLoad,
    isInitialFutureMonth,
    isFirstAvailableDateSet,
  ]);

  useEffect(() => {
    getDayPerformancesOnShopCartDate();
  }, [shopCartDate]);

  // Separate useEffect to handle fetching next month
  useEffect(() => {
    if (shouldFetchNextMonth) {
      getDaysAvailabilityQuery.refetch().catch((error) => {
        console.error('Error refetching availability:', error);
        handleError(error as Error);
        setShouldFetchNextMonth(false);
      });
    }
  }, [shouldFetchNextMonth, handleError]);

  // invariant(!!data?.page, `Expected valid json data for ${flow}`);

  return (
    <BuyTicketsLayoutContext.Provider
      value={{
        dayAvailabilities,
        getDaysAvailabilityQuery,
        getDayPerformances,
        heroData,
        isLoading,
        isAddOnLoading,
        isSunsetSlotSelected,
        isTimeSlotsAnimating,
        selectedTimePeriod,
        setIsLoading,
        setIsAddOnLoading,
        setSelectedTimePeriod,
        setIsTimeSlotsAnimating,
        ticketStepData,
        tickets,
      }}
    >
      {children}
    </BuyTicketsLayoutContext.Provider>
  );
};

const BuyTicketsLayoutMain = ({
  children,
  sx,
  ...props
}: Omit<LayoutProps, 'data'>) => {
  const flow = useAppSelector(selectFlow);

  return (
    <Box as="main" data-gtm-flow={flow} sx={sx} {...props}>
      {children}
    </Box>
  );
};

const ChildrenMarkup = ({
  children,
  data,
  modal,
}: {
  modal: ReturnType<typeof useBuyTicketsModal>;
  children: React.ReactNode;
  data: LayoutProps['data'];
}) => {
  const shopCartSelections = useAppSelector(selectShopCartSelections);
  const { getDaysAvailabilityQuery } = useContext(BuyTicketsLayoutContext);

  return (
    <ErrorBoundary
      fallbackRender={({ error, resetErrorBoundary }) => (
        <Fragment>
          {children}
          <BuyTicketsModal
            data={data}
            error={error}
            isOpen={true}
            onClose={resetErrorBoundary}
            shopCartSelections={shopCartSelections}
          />
        </Fragment>
      )}
      onReset={() => {
        modal.setIsOpen(false);
        modal.setValue(undefined);
      }}
    >
      {getDaysAvailabilityQuery?.isError && <TicketsErrorModal />}
      {children}
      <BuyTicketsModal
        data={data}
        error={modal.value}
        isOpen={modal.isOpen}
        onClose={() => modal.setIsOpen(false)}
        onCloseFinished={() => modal.setValue(undefined)}
        shopCartSelections={shopCartSelections}
      />
    </ErrorBoundary>
  );
};

export function BuyTicketsLayout({
  theme,
  components,
  children,
  sx: withSx,
  data,
  attraction,
  flow,
  event,
  ...props
}: LayoutProps): React.JSX.Element {
  const modal = useBuyTicketsModal();
  const [isLoading, setIsLoading] = useState(false);

  const handleError = useCallback(
    (error: Error) => {
      modal.setValue(error);
      setIsLoading(false);
      modal.setIsOpen(true);
    },
    [modal],
  );

  const dispatch = useAppDispatch();

  const wizard = useAppSelector((state) => state.wizard);

  useEffect(() => {
    const referrer =
      typeof document !== 'undefined' ? document.referrer : undefined;
    const currentPath =
      typeof window !== 'undefined' ? window.location.pathname : undefined;
    const currentUrl =
      typeof window !== 'undefined' ? window.location.href : undefined;

    newRelic.logCartAction(
      'BuyTicketsLayout - Entering Buy Tickets flow',
      'flow',
      {
        flow,
        referrer,
        currentPath,
        currentUrl,
      },
    );
  }, []);

  useEffect(() => {
    // reset the timer and kick user back to step 1 when the timer ends
    if (wizard.counterEndTime === 0) {
      dispatch(actions.order.reset());
      dispatch(actions.wizard.reset());
      newRelic.logCartAction(
        'BuyTicketsLayout - Cart Timer Expired',
        'cartReset',
      );
      const error = new Error('10 mins remaining generic error');
      error.name = 'session timeout generic error';

      handleError(error);
    }
  }, [wizard.counterEndTime, dispatch, handleError]);

  useEffect(() => {
    if (
      data.page?.ticketStep?.ticketTypes?.some((ticketType) => {
        const ticketTypeId = ticketType?.ticketTypeId;
        if (!ticketTypeId) return false;

        return (
          typeof ticketTypeId !== 'string' ||
          ticketTypeId === `T${OLD_VIP_TICKET_TYPE_ID}`
        );
      })
    ) {
      window.Sentry?.captureException(
        new Error('old VIP ticketTypeId detected'),
      );

      void navigate('/buy-tickets');
    }
  });

  return (
    <Fragment>
      <Oljs />
      <BuyTicketsLayoutProvider data={data} flow={flow}>
        <OrderInitializationContextProvider
          attraction={attraction}
          event={event}
          flow={flow}
        >
          <Global
            styles={{
              'body, #___gatsby, #gatsby-focus-wrapper': {
                minHeight: '100svh',
              },
              '#gatsby-focus-wrapper': {
                'display': 'flex',
                'flexDirection': 'column',
                '> div:first-of-type': {
                  'flex': 1,
                  'display': 'flex',
                  'flexDirection': 'column',
                  '> [data-module="App"]': {
                    'flex': 1,
                    'display': 'flex',
                    'flexDirection': 'column',
                    '> main': {
                      'flex': 1,
                      'display': 'flex',
                      'flexDirection': 'column',
                      '[data-themeui-nested-provider="true"]': {
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                      },
                    },
                  },
                },
              },
            }}
          />
          <CountdownTimerProvider>
            <BuyTicketsModalProvider value={modal}>
              {theme || components ? (
                <ThemeProvider theme={theme ? getThemeByName(theme) : {}}>
                  <BuyTicketsLayoutMain flow={flow} sx={withSx} {...props}>
                    <ChildrenMarkup data={data} modal={modal}>
                      {children}
                    </ChildrenMarkup>
                  </BuyTicketsLayoutMain>
                </ThemeProvider>
              ) : (
                <BuyTicketsLayoutMain flow={flow} sx={withSx} {...props}>
                  <ChildrenMarkup data={data} modal={modal}>
                    {children}
                  </ChildrenMarkup>
                </BuyTicketsLayoutMain>
              )}
              <SessionExpiredModal />
            </BuyTicketsModalProvider>
          </CountdownTimerProvider>
        </OrderInitializationContextProvider>
      </BuyTicketsLayoutProvider>
    </Fragment>
  );
}
