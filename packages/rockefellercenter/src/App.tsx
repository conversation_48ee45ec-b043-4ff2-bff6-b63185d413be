/** @jsxImportSource theme-ui @jsxRuntime classic */
import { useLocation } from '@reach/router';
import React, {
  useState,
  useCallback,
  useMemo,
  useEffect,
  Fragment,
} from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Provider as ReduxProvider } from 'react-redux';
import { Global } from '@emotion/react';
import { enableMapSet } from 'immer';
import useSize from '@hzdg/use-size';
import { FocusScope } from '@hzdg/focus-scope';
import {
  Box,
  Link,
  ThemeProvider,
  getThemeByName,
  Modal,
} from '@tishman/components';
import {
  store as componentsStore,
  Context as ComponentsStoreContext,
} from '@tishman/components/store';
import * as Icons from '@tishman/icons';

import AlertBanner from './components/AlertBanner';
import FooterBlock from './blocks/FooterBlock';
import { Meta } from './components/Meta';
import { MenuBar, Menu, SearchMenu, MENU, SEARCH } from './components/menu';
import { useMenuLinkGroups, useSearchSuggestionLinks } from './blocks/queries';
import { usePageConfig } from './PageConfig';
import {
  ctaLocaleMap,
  getLocalStoragePreferredLocale,
  PageContextProvider,
  PageDataProvider,
  useI18n,
} from './utils';
import { useLocalizedLink } from './utils/use-localized-link';
import { store } from './store';
import { NewsletterSignUpModal } from './blocks/NewsletterSignupModalBlock/NewsletterSignupModal';

import type { PageProps } from 'gatsby';
import type { ReactNode, Ref } from 'react';
import type { PageConfig } from './PageConfig';
import type { MenuBarProps, OverlayType } from './components/menu';

// import type { Translations } from './utils';

enableMapSet();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: 1200000, // 20 minutes
    },
  },
});

interface MenuBarContainerProps extends MenuBarProps {
  menuBarRef: Ref<HTMLDivElement>;
  pageConfig: PageConfig;
}

/**
 * A container for the `MenuBar` that memoizes its render state
 * and makes sure it remains interactive while overlays are open.
 */
function MenuBarContainer({
  menuBarRef,
  pageConfig,
  ...menuBarProps
}: MenuBarContainerProps) {
  const location = useLocation();
  const getLocalizedLink = useLocalizedLink();
  const { t } = useI18n();
  const locale = getLocalStoragePreferredLocale();

  /** The CTA to render in the `MenuBar` for the current page. */
  const cta = useMemo(() => {
    if (pageConfig.cta) {
      const { label, to, ...props } = pageConfig.cta;

      // Temporary solution for translating the homepage CTA only
      if (location.pathname === '/') {
        // set CTA text and href based on current locale
        const { text, href } =
          ctaLocaleMap[locale as keyof typeof ctaLocaleMap] ||
          ctaLocaleMap.default;

        return (
          <Link
            href={href}
            id="headerCTA"
            sx={{
              'variant': 'buttons.inverted',
              'fontWeight': 'medium',
              'border': 'none',
              '&:focus': {
                backgroundColor: '#A98363',
              },
            }}
            {...props}
          >
            {text}
          </Link>
        );
      }

      return (
        <Link
          href={getLocalizedLink(to)}
          id="headerCTA"
          sx={{
            'variant': 'buttons.inverted',
            'fontWeight': 'medium',
            'border': 'none',
            '&:focus': {
              backgroundColor: '#A98363',
            },
          }}
          {...props}
        >
          {label.endsWith(' | t') ? t(label.replace(/ \| t$/, '')) : label}
        </Link>
      );
    }
    return null;
  }, [pageConfig.cta]);

  /** The CTA to render in the `MenuBar` for the current page. */
  const secondaryCta = useMemo(() => {
    if (pageConfig.secondaryCta) {
      const { label, to, ...props } = pageConfig.secondaryCta;

      // Temporary solution for translating the homepage CTA only
      if (location.pathname === '/') {
        // set CTA text and href based on current locale
        const { text, href } =
          ctaLocaleMap[locale as keyof typeof ctaLocaleMap] ||
          ctaLocaleMap.default;

        return (
          <Link
            href={href}
            id="headerSecondaryCTA"
            key="secondaryCta"
            sx={{
              'variant': 'buttons.inverted',
              'fontWeight': 'medium',
              'border': 'none',
              'display': ['none', 'none', 'none', 'flex'],
              '&:focus': {
                backgroundColor: '#A98363',
              },
            }}
            {...props}
          >
            {text}
          </Link>
        );
      }

      return (
        <Link
          href={getLocalizedLink(to)}
          id="headerCTA"
          sx={{
            'variant': 'links.underline',
            'fontWeight': 'medium',
            'border': 'none',
            '&:focus': {
              backgroundColor: '#A98363',
            },
          }}
          {...props}
        >
          {label.endsWith(' | t') ? t(label.replace(/ \| t$/, '')) : label}
        </Link>
      );
    }
    return null;
  }, [pageConfig.secondaryCta]);

  /** The right nav element to render in the `MenuBar` for the current page. */
  const rightNav = useMemo(() => {
    if (pageConfig.rightNav) {
      const { label, to, ...props } = pageConfig.rightNav;

      return (
        <Link
          key="right-nav"
          to={getLocalizedLink(to)}
          variant="underline"
          {...props}
        >
          {label}
        </Link>
      );
    }
    return null;
  }, [pageConfig.rightNav]);

  /** The left nav element to render in the `MenuBar` for the current page. */
  const leftNav = useMemo(() => {
    if (pageConfig.leftNav) {
      const { label, to, ...props } = pageConfig.leftNav;
      // let _to = to;
      let _to = getLocalizedLink(to);

      if (
        location.hostname?.endsWith('topoftherocknyc.com') &&
        _to.startsWith('/buy-tickets/')
      ) {
        _to = _to.replace('/buy-tickets/', '/ticket-menu/');
      }

      return (
        <Link key="left-nav" to={_to} variant="menuBarBackArrow" {...props}>
          {label}
          <Icons.Arrow aria-hidden />
        </Link>
      );
    }
    return null;
  }, [location.hostname, pageConfig.leftNav]);

  return (
    <Box
      ref={menuBarRef}
      style={{ pointerEvents: 'auto' }}
      sx={{
        'bg':
          getThemeByName(pageConfig.theme ?? 'Rock Center').colors
            ?.background ?? 'white',
        'position': 'relative',
        'zIndex': ['overlay', 'fixed'],
        '@media print': {
          display: 'none',
        },
      }}
    >
      <MenuBar
        {...menuBarProps}
        cta={cta}
        hideSearchToggleButton={pageConfig.hideSearchToggleButton}
        leftNav={leftNav}
        logo={pageConfig.logo}
        logoLink={pageConfig.logoLink}
        menuOpenBGColor={pageConfig.menuOpenBGColor}
        pageName={pageConfig.pageName}
        rightNav={rightNav}
        secondaryCta={secondaryCta}
      />
    </Box>
  );
}

function AppInner({
  alert = null,
  children,
  pagePath,
}: {
  alert?: ReactNode;
  children: ReactNode;
  pagePath: string;
}) {
  const pageConfig = usePageConfig();
  const { menuLinkGroups, menuSecondaryLinks } = useMenuLinkGroups();
  const searchSuggestionLinks = useSearchSuggestionLinks();
  const [isOpen, openOverlay] = useState<false | OverlayType>(false);
  const [menuBarSize, menuBarRef] = useSize();
  const pageTheme = getThemeByName(pageConfig.theme ?? 'Rock Center');
  const closeOverlay = useCallback(() => openOverlay(false), []);

  // Close any open overlay when the page changes.
  useEffect(closeOverlay, [closeOverlay, pagePath]);

  const hideNavAndFooter = pageConfig.hideNavAndFooter ?? false;

  /** Memoized render of the `MenuBar` and `Menu` modal. */
  const mainMenuMarkup = (
    <FocusScope
      onKeyPress={
        /** Handle keypresses that dismiss the currently open overlay. */
        isOpen === MENU
          ? (event: KeyboardEvent) => {
              if (!event.defaultPrevented && event.key === 'Escape') {
                closeOverlay();
                event.preventDefault();
              }
            }
          : undefined
      }
      trap={isOpen === MENU}
    >
      {alert}

      {!hideNavAndFooter && (
        <MenuBarContainer
          isOpen={isOpen}
          menuBarRef={menuBarRef}
          onClose={closeOverlay}
          onOpen={openOverlay}
          pageConfig={pageConfig}
        />
      )}
      <Box sx={{ position: 'relative' }}>
        <Modal
          id="main-menu"
          isOpen={isOpen === MENU}
          onClose={closeOverlay}
          style={{
            height: `calc(100vh - ${menuBarSize.height}px)`,
            overflow: 'auto',
          }}
          sx={{
            /* This transform is applied to the containing div to establish
             * a new containing block, so that we can fix the position of
             * the Menu Modal to it rather than the viewport.
             * See https://developer.mozilla.org/en-US/docs/Web/CSS/position#fixed
             * See https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_Block#Identifying_the_containing_block
             */
            transform: 'translateY(0px)',
            position: 'relative',
          }}
          trap={false}
        >
          <Menu
            isOpen={isOpen === MENU}
            linkGroups={menuLinkGroups}
            secondaryLinks={menuSecondaryLinks}
          />
        </Modal>
      </Box>
    </FocusScope>
  );

  /** Memoized render of the `SearchMenu`. */
  const searchMenuMarkup = (
    <ThemeProvider key="search" theme={getThemeByName('Rock Center Black')}>
      <Modal id="search-menu" isOpen={isOpen === SEARCH} onClose={closeOverlay}>
        <SearchMenu
          onClose={closeOverlay}
          suggestions={searchSuggestionLinks}
          to="/search/"
        />
      </Modal>
    </ThemeProvider>
  );
  return (
    <Fragment>
      <Meta {...pageConfig.meta} pagePath={pagePath} />
      <ThemeProvider theme={pageTheme}>
        <Global
          styles={{
            backgroundColor: pageTheme?.colors?.background,
            color: pageTheme?.colors?.text,
            fontFamily: pageTheme?.fonts?.body,
            fontWeight: pageTheme?.fontWeights?.body,
            lineHeight: pageTheme?.lineHeights?.body,
          }}
        />
        <NewsletterSignUpModal />
        <Box
          data-module="App"
          sx={{ backgroundColor: pageTheme.colors?.background }}
        >
          {mainMenuMarkup}
          {searchMenuMarkup}
          {children}
          <ReactQueryDevtools />
        </Box>
        {!hideNavAndFooter && <FooterBlock />}
      </ThemeProvider>
    </Fragment>
  );
}

export type AppProps = Omit<
  PageProps<
    Record<string, unknown> & {
      alert?: { alert: Queries.Maybe<Queries.SanityAlert> };
      translations?: {
        edges: {
          node: {
            content: string;
            namespace: string;
          };
        }[];
      };
    },
    Record<string, unknown>
  >,
  'children'
> & {
  children?: ReactNode;
};

/**
 * `App` renders the current page within the 'chrome' of the site;
 * all the stuff that is omnipresent or outside of the flow of
 * any individual page, including the `MenuBar`, the `Footer`, and overlays.
 */
export function App({
  children,
  data,
  pageContext,
  path,
}: AppProps): React.JSX.Element {
  return (
    <PageContextProvider pageContext={pageContext}>
      <PageDataProvider data={data}>
        {/* TODO : fix type - replace any */}
        <ReduxProvider
          context={ComponentsStoreContext as any}
          store={componentsStore}
        >
          <ReduxProvider store={store}>
            <QueryClientProvider client={queryClient}>
              <AppInner
                alert={!!data?.alert && <AlertBanner data={data} />}
                pagePath={path}
              >
                {children}
              </AppInner>
            </QueryClientProvider>
          </ReduxProvider>
        </ReduxProvider>
      </PageDataProvider>
    </PageContextProvider>
  );
}
