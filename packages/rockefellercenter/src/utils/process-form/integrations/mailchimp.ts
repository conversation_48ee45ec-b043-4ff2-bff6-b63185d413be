import fetch from 'node-fetch';
import * as Sentry from '@sentry/node';

import {
  FILM_PHOTO_AUDIENCE_ID,
  MAILCHIMP_API_KEY,
  MAILCHIMP_AUDIENCE_ID,
  MAILCHIMP_SKYLIFT_AUDIENCE_ID,
  MAILCHIMP_URL,
} from '../../../environment';

import type { IntegrationHandler, MailchimpIntegration } from '..';

export const mailchimp: IntegrationHandler<MailchimpIntegration> = async ({
  data,
  fields,
  id,
  options,
}) => {
  if (!MAILCHIMP_URL) {
    return { status: 500, data: 'MAILCHIMP_URL is not specified' };
  }
  if (!<PERSON><PERSON><PERSON>IMP_AUDIENCE_ID) {
    return { status: 500, data: '<PERSON><PERSON><PERSON><PERSON>P_AUDIENCE_ID is not specified' };
  }
  if (!MAILCHIMP_API_KEY) {
    return { status: 500, data: 'MAILCHIMP_API_KEY is not specified' };
  }

  const { tags, customFields = {}, interests = {}, updateFields } = options;

  const newData = updateFields?.(data, fields) ?? data;
  if ('error' in newData) {
    return {
      status: 500,
      data: `Error in updateFields function for ${id}: ${newData.error}`,
    };
  }

  const { newsletter } = newData;

  if (newsletter !== undefined && newsletter === 'false') {
    return { status: 200, data: 'NewsLetter Not Subscribed' };
  }

  const {
    email,
    customTags = [],
    customInterests = {},
    ...mergeFields
  } = newData;

  delete mergeFields.files;

  const body = {
    email_address: email,
    tags: [...tags, ...(customTags as string[])],
    interests: {
      ...interests,
      ...(customInterests as Record<string, boolean>),
    },
    merge_fields: { ...mergeFields, ...customFields },
  };

  let status = 500;
  let responseText = null;
  let tagResponseText = null;
  let extraContext = null;
  let audienceID = MAILCHIMP_AUDIENCE_ID;

  // Handle dynamic audience ID from DYNAMIC_AUDIENCE: tag
  const dynamicAudienceTag = body.tags.find(
    (tag) => typeof tag === 'string' && tag.startsWith('DYNAMIC_AUDIENCE:'),
  );

  if (dynamicAudienceTag) {
    audienceID = (dynamicAudienceTag as string).replace(
      'DYNAMIC_AUDIENCE:',
      '',
    );
    // Remove the DYNAMIC_AUDIENCE tag from the final tags list since it's not a real Mailchimp tag
    body.tags = body.tags.filter((tag) => tag !== dynamicAudienceTag);
  } else {
    // Existing tag-based audience ID logic
    if (tags.includes('SOURCE:FilmPhotoSignUp')) {
      if (!FILM_PHOTO_AUDIENCE_ID) {
        return { status: 500, data: 'FILM_PHOTO_AUDIENCE_ID is not specified' };
      }
      audienceID = FILM_PHOTO_AUDIENCE_ID;
    }
    if (tags.includes('Source:SKYLIFT')) {
      if (!MAILCHIMP_SKYLIFT_AUDIENCE_ID) {
        return {
          status: 500,
          data: 'MAILCHIMP_SKYLIFT_AUDIENCE_ID is not specified',
        };
      }
      audienceID = MAILCHIMP_SKYLIFT_AUDIENCE_ID;
    }
  }

  const memberEndpoint = `/lists/${audienceID}/members`;

  // Add/Update Member
  try {
    const response = await fetch(`${MAILCHIMP_URL}${memberEndpoint}/${email}`, {
      method: 'PUT',
      headers: {
        'authorization': `Bearer ${MAILCHIMP_API_KEY}`,
        'content-type': 'application/json',
      },
      body: JSON.stringify({ ...body, status: 'subscribed' }),
    });
    responseText = (await response.json()) as { title: string };

    if (response.status >= 400) {
      console.error('mailchimp response', responseText);
    }

    // User-friendly logging for common email issues
    if (
      response.status === 400 &&
      responseText.title === 'Member In Compliance State'
    ) {
      console.warn(
        `📧 Email Notice: ${email} is in compliance state (unsubscribed/bounced) - not a system error`,
      );
    }

    if (
      response.status === 400 &&
      responseText.title === 'Forgotten Email Not Subscribed'
    ) {
      extraContext =
        'User must fill out a Mailchimp hosted form in order to be added back to the list. To read more on permanently deleted contacts go to https://mailchimp.com/help/delete-contacts/#Archive_vs._Remove';
    }

    status = response.status;
  } catch (error) {
    responseText = error;
    Sentry.setTag('integration_error', 'mailchimp_add_member');
    Sentry.setTag('email', email as string);
    Sentry.captureException(error);
  }

  // Update tags
  try {
    const tagResponse = await fetch(
      `${MAILCHIMP_URL}${memberEndpoint}/${email}/tags`,
      {
        method: 'POST',
        headers: {
          'authorization': `Bearer ${MAILCHIMP_API_KEY}`,
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          tags: body.tags.map((tag) => ({ name: tag, status: 'active' })),
        }),
      },
    );

    // Handle 204 No Content response (success with no body)
    if (tagResponse.status === 204) {
      tagResponseText = { message: 'Tags updated successfully' };
    } else {
      tagResponseText = await tagResponse.json();
    }

    if (tagResponse.status >= 400) {
      console.error('mailchimp updateTags error', tagResponseText);
    }

    // Give this secondary request higher priority
    if (status === 200) {
      status = tagResponse.status;
    }
  } catch (error) {
    tagResponseText = error;
    Sentry.setTag('integration_error', 'mailchimp_update_tags');
    Sentry.setTag('email', email as string);
    Sentry.captureException(error);
  }

  return {
    status,
    data: {
      addMember: responseText,
      updateTags: tagResponseText,
    },
    extraContext,
  };
};
