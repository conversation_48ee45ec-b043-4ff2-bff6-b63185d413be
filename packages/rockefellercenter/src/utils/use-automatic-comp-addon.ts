import { useEffect, useMemo, useState } from 'react';

import {
  useFindAllProductByStatGroup,
  useGetDayPerformancesMutation,
} from '../services/viva';
import {
  AddOnAKForStatGroup,
  AddOnStatGroupsByFlowFlat,
  BosEvent,
  COMP_TICKETS,
  Flow,
  StatGroup,
} from '../services/viva/constants';
import { getStatGroupKeyByValue, putInArray } from '../services/viva/utils';
import {
  actions,
  selectFirstTimeSlot,
  selectFlow,
  selectShopCartDate,
  selectShopCartDateTime,
  selectShopCartSelections,
  selectTotalQuantity,
  selectWizardCurrentStep,
  useAppDispatch,
  useAppSelector,
} from '../store';

import { useCapacityAddon } from './use-capacity-addon';

import type { AddOnItem } from '../components/BuyTickets';

/**
 * This hook is used to automatically add the beam addon to the cart
 * when a user selects a time.
 **/
const useAutomaticCompAddon = () => {
  const dispatch = useAppDispatch();
  const shopCartDate = useAppSelector(selectShopCartDate);
  const shopCartDateTime = useAppSelector(selectShopCartDateTime);
  const shopCartSelections = useAppSelector(selectShopCartSelections);
  const totalQuantity = useAppSelector(selectTotalQuantity);
  const timeSlot = useAppSelector(selectFirstTimeSlot);
  const flow = useAppSelector(selectFlow);
  const currentStep = useAppSelector(selectWizardCurrentStep);
  const [currentStepState, setCurrentStepState] = useState<number | null>(
    currentStep,
  );
  const getDayPerformanceMutation = useGetDayPerformancesMutation();
  const [isLoading, setIsLoading] = useState(false);

  const addOnQuery = useFindAllProductByStatGroup({
    statGroups: AddOnStatGroupsByFlowFlat[flow] ?? [],
    enabled: Boolean(AddOnStatGroupsByFlowFlat[flow]),
  });

  useCapacityAddon();

  const addOns = useMemo(() => {
    const _addOns = putInArray(addOnQuery.data?.PRODUCTLIST?.PRODUCT);

    return _addOns
      .filter((_addOn) => _addOn?.CODE !== 'JUPTORWEB') // Jupiter Dinner should not be added automatically
      .map((_addOn) => ({
        ak: _addOn?.AK,
        description: _addOn?.DESCRIPTION ?? '',
        price: parseFloat(_addOn?.PRICE.GROSS) * 100 ?? 0,
        marketingText: _addOn?.ADDITIONALDESC ?? '',
        quantity: shopCartSelections.reduce(
          (sum, selection) => sum + selection.count,
          0,
        ),
        statGroup: getStatGroupKeyByValue(AddOnAKForStatGroup, _addOn?.AK),
      })) as AddOnItem[];
  }, [addOnQuery.data, shopCartSelections]);

  // Avoid infinite loop of re-renders by only updating state when the current step changes
  useEffect(() => {
    if (currentStep !== currentStepState) {
      setCurrentStepState(currentStep);
    }
  }, [currentStep]);

  useEffect(() => {
    if (
      !addOns ||
      ![
        Flow.TOR_VIP,
        Flow.RC_ROCK_PASS_VIP_HOLIDAY,
        Flow.TOR_VIP_ROCKSTAR,
      ].includes(flow)
    ) {
      return;
    }
    const asyncEffect = async () => {
      try {
        setIsLoading(true);
        const performanceDateForBeam =
          await getDayPerformanceMutation.mutateAsync({
            bosEvent: BosEvent.THE_BEAM,
            day: shopCartDate.day,
          });
        const performanceDateForSkylift =
          await getDayPerformanceMutation.mutateAsync({
            bosEvent: BosEvent.SKYLIFT,
            day: shopCartDate.day,
          });
        const performanceDateForPhotoPackage =
          await getDayPerformanceMutation.mutateAsync({
            bosEvent: BosEvent.PHOTO_PACKAGE,
            day: shopCartDate.day,
          });
        const performanceDateForChampagne =
          await getDayPerformanceMutation.mutateAsync({
            bosEvent: BosEvent.CHAMPAGNE_TOAST,
            day: shopCartDate.day,
          });

        setIsLoading(false);

        const firstAvailablePerfForBeam = performanceDateForBeam[0];
        const firstAvailablePerfForSkylift = performanceDateForSkylift[0];
        const firstAvailablePerfForPhotoPackage =
          performanceDateForPhotoPackage[0];
        const firstAvailablePerfForChampagne = performanceDateForChampagne[0];

        const performanceLookup: { [key: string]: string[] } = {
          [StatGroup.THE_BEAM_COMP]: [firstAvailablePerfForBeam?.AK],
          [StatGroup.PHOTO_PACKAGE_COMP]: [
            firstAvailablePerfForPhotoPackage?.AK,
          ],
          [StatGroup.CHAMPAGNE_TOAST_COMP]: [
            firstAvailablePerfForChampagne?.AK,
          ],
          [StatGroup.SKYLIFT_COMP]: [firstAvailablePerfForSkylift?.AK],
        };

        const filteredAddOns = addOns.filter((addOn) => Boolean(addOn.ak));

        if (filteredAddOns && filteredAddOns.length > 0) {
          const updatedAddOns = filteredAddOns.map((addOn) => {
            return {
              ...addOn,
              performances: performanceLookup[addOn.statGroup] ?? [],
            };
          });

          const updatedAddOnsWithCount = updatedAddOns.map((addOn) => ({
            ...addOn,
            quantity: COMP_TICKETS.includes(addOn.statGroup)
              ? (totalQuantity as number)
              : 0,
          }));

          dispatch(
            actions.checkout.setAddOns({
              addOns: updatedAddOnsWithCount,
              dateTime: shopCartDateTime,
              flow,
            }),
          );
        }
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    };

    if (timeSlot) {
      asyncEffect();
    }
  }, [addOns, currentStepState, shopCartDate.day, timeSlot, flow]);

  return { isLoading };
};

export default useAutomaticCompAddon;
