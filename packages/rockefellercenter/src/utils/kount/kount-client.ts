import { v4 as uuidv4 } from 'uuid';
import axios, { type AxiosInstance } from 'axios';

import { newRelic } from '../../utils/new-relic';

import { hashCreditCard } from './hash-credit-card';
import { KOUNT } from './constants';

import type { TModeQAttributes } from './mode-q-params';

type KountResponse = {
  AUTO: string; // Auto response recommendation
  TRAN: string; // Transaction ID
  SCOR: number; // Legacy score (0-100)
  OMNISCORE: number; // New Omniscore (0-1)
  RULES_TRIGGERED: string[]; // List of triggered rules
  WARNING_CODES: string[]; // List of warning codes
};

/**
 * The `KountClient` class provides an interface for interacting with the Kount 360 Payments Fraud
 * system through our API.
 *
 * This class supports sending two types of requests to Kount:
 *   - Mode Q: Used for querying risk scores based on transaction details.
 *   - Mode U: Used for updating Kount with the final status of the transaction (e.g., approved or declined).
 *
 * Each instance of `KountClient` manages its own session and transaction identifiers, which are essential
 * for tracking individual sessions and transactions across API calls.
 *
 * ## Features
 * - **Session Management**: Automatically generates a session ID upon initialization, which is used
 *   for all subsequent API requests to maintain session consistency.
 * - **Transaction Tracking**: Captures and stores the transaction ID from Mode Q responses, which is required
 *   for subsequent Mode U requests.
 * - **Omniscore Support**: Handles the new Kount 360 Omniscore (0-1) in addition to legacy scores.
 * - **OAuth 2.0 Authentication**: Supports the new Kount 360 Payments Fraud v2.0 authentication method.
 */

export class KountClient {
  private sessionId: string;
  private transactionId: string | undefined;
  private httpClient: AxiosInstance;
  private bearerToken: string = '';
  private tokenExpiration: number = 0;

  constructor(httpClient: AxiosInstance = axios) {
    this.sessionId = uuidv4().replace(/-/g, '');
    this.transactionId = undefined;
    this.httpClient = httpClient;
  }

  /**
   * Gets a bearer token for Kount 360 Payments Fraud v2.0 API authentication
   * Tokens expire after 20 minutes
   */
  private async getBearerToken(): Promise<string> {
    // Check if we have a valid token
    const now = Date.now();
    if (this.bearerToken && now < this.tokenExpiration) {
      return this.bearerToken;
    }

    try {
      // Use our server-side proxy endpoint instead of direct Kount auth URL
      const response = await this.httpClient({
        url: '/api/kount/token',
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.data.access_token) {
        throw new Error(
          'No access token returned from Kount authentication service',
        );
      }

      this.bearerToken = response.data.access_token;
      // Token expires in 20 minutes (response.data.expires_in), but we'll set it to 19 minutes to be safe
      this.tokenExpiration = now + 19 * 60 * 1000;
      return this.bearerToken;
    } catch (error: unknown) {
      const axiosError = error as {
        message: string;
        response?: { status?: number; data?: unknown };
      };
      console.error('Error getting Kount bearer token:', axiosError.message);
      console.error('Error details:', axiosError.response?.data);
      throw new Error(
        `Failed to get Kount bearer token: ${axiosError.message}`,
      );
    }
  }

  /**
   * Sends a Mode Q request to evaluate a transaction's risk
   */
  public async sendModeQRequest(params: Omit<TModeQAttributes, 'sessionId'>) {
    if (!this.sessionId) {
      throw new Error('Session ID must be set before sending Mode Q request');
    }

    const { cardNumber, total, products, orderNumber } = params;
    const protectedCard = hashCreditCard(cardNumber.toString());

    newRelic.logCartAction('Kount', 'sendModeQRequest', {
      orderNumber,
      products,
      total,
      mode: KOUNT.MODE.Q,
      sessionId: this.sessionId,
    });

    try {
      // For Kount 360 Payments Fraud v2.0, use the new API format
      const token = await this.getBearerToken();

      // Format the request according to Payments Fraud v2.0
      const paramsTyped = params as unknown as Record<string, string>;
      const cardBin = paramsTyped.cardBin || '';
      const cardLast4 =
        paramsTyped.cardLast4 ||
        (paramsTyped.cardNumber ? paramsTyped.cardNumber.slice(-4) : '');
      const firstName = paramsTyped.firstName || '';
      const lastName = paramsTyped.lastName || '';
      const subtotal = paramsTyped.subtotal || total;
      const taxAmount = paramsTyped.taxAmount || '0';
      const outOfStateTaxAmount = paramsTyped.outOfStateTaxAmount || '0';
      const isTaxable = true;
      const creationDateTime = new Date().toISOString();

      const items: Record<string, unknown>[] = products.map((product) => {
        const item: Record<string, unknown> = {
          price: product.price.toString(),
          description: product.description,
          name: product.item,
          quantity: product.quantity.toString(),
          category: product.type,
          id: product.item,
          isService: true,
          isDigital: true,
          subCategory: 'TICKET',
        };
        return item;
      });

      const payment: Record<string, unknown> = {
        type: 'CREDIT_CARD',
        paymentToken: protectedCard,
        bin: cardBin,
        last4: cardLast4,
      };

      const billedPerson: Record<string, unknown> = {
        phoneNumber: `${params.phone}`,
        emailAddress: params.email,
        address: {
          line1: params.billingAddress,
          line2: '',
          city: params.billingCity,
          region: params.billingState,
          countryCode: params.billingCountryCode,
          postalCode: params.billingPostalCode,
        },
        dateOfBirth: '',
        name: {
          first: firstName,
          family: lastName,
          preferred: firstName,
          middle: '',
          prefix: '',
          suffix: '',
        },
      };

      const paymentsFraudRequest: Record<string, unknown> = {
        merchantOrderId: (params.orderNumber || `ORDER${Date.now()}`).replace(
          /[^a-zA-Z0-9]/g,
          '',
        ),
        channel: 'WEB',
        deviceSessionId: this.sessionId,
        creationDateTime,
        userIp: params.remoteAddress,
        account: {
          id: params.customerID,
          type: 'CUSTOMER',
          creationDateTime,
          username: params.email,
          accountIsActive: true,
        },
        items,
        fulfillment: [],
        transactions: [
          {
            processor: 'Shift4',
            processorMerchantId:
              process.env.KOUNT_PROCESSOR_MERCHANT_ID || 'UNKNOWN',
            payment,
            subtotal,
            orderTotal: total,
            currency: 'USD',
            tax: {
              isTaxable,
              taxableCountryCode: params.billingCountryCode,
              taxAmount: taxAmount.toString(),
              outOfStateTaxAmount,
            },
            billedPerson,
            transactionStatus: 'AUTHORIZED',
            authorizationStatus: {
              authResult: 'UNKNOWN',
              dateTime: new Date().toISOString(),
              verificationResponse: {
                cvvStatus: 'UNKNOWN',
                avsStatus: 'U',
              },
              processorAuthCode: '',
              processorTransactionId: '',
            },
            merchantTransactionId: (
              params.orderNumber || `ORDER${Date.now()}`
            ).replace(/[^a-zA-Z0-9]/g, ''),
            items: products.map((product) => ({
              id: product.item,
              quantity: product.quantity,
            })),
          },
        ],
        promotions: [],
        loyalty: {},
        customFields: {
          DATE_OF_TICKET: new Date(params.ticketStartDate).toISOString(),
          DAY_OF_TICKET: isToday(params.ticketStartDate) === 'Y',
          DAYS_UNTIL_TICKET: daysUntil(params.ticketStartDate),
        },
      };

      // Call Kount 360 Payments Fraud API via server-side endpoint
      let kountResponse;
      try {
        kountResponse = await this.httpClient.post(
          '/api/kount/orders',
          paymentsFraudRequest,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
              'SessionId': this.sessionId,
            },
            params: {
              riskInquiry: true,
            },
          },
        );

        // Store the transaction ID for Mode U requests
        // Check if the response has the expected structure for Kount v2 API
        if (kountResponse.data.order?.orderId) {
          // New Kount v2 API format
          this.transactionId = kountResponse.data.order.orderId;

          // Format response to maintain compatibility with existing code
          return {
            AUTO: this.mapDecisionToLegacyAuto(
              kountResponse.data.order.riskInquiry?.decision,
            ),
            TRAN: kountResponse.data.order.orderId,
            SCOR: Math.round(
              (kountResponse.data.order.riskInquiry?.omniscore || 0) * 100,
            ),
            OMNISCORE: kountResponse.data.order.riskInquiry?.omniscore || 0,
            RULES_TRIGGERED: [], // Not directly available in the new format
            WARNING_CODES: [], // Not directly available in the new format
          };
        } else {
          // Legacy format or unexpected response structure
          this.transactionId = kountResponse.data.kountOrderId;

          // Format response to maintain compatibility with existing code
          return {
            AUTO: this.mapDecisionToLegacyAuto(
              kountResponse.data.riskInquiry?.[0]?.decision,
            ),
            TRAN: kountResponse.data.kountOrderId,
            SCOR: Math.round(
              (kountResponse.data.riskInquiry?.[0]?.omniscore || 0) * 100,
            ),
            OMNISCORE: kountResponse.data.riskInquiry?.[0]?.omniscore || 0,
            RULES_TRIGGERED:
              kountResponse.data.riskInquiry?.[0]?.triggeredRules || [],
            WARNING_CODES: kountResponse.data.riskInquiry?.[0]?.warnings || [],
          };
        }
      } catch (kountError: unknown) {
        const axiosError = kountError as {
          message: string;
          response?: { status?: number; data?: unknown };
        };
        console.error('Error from Kount API:', axiosError.message);
        console.error('Error details:', axiosError.response?.data);
        throw kountError;
      }
    } catch (error: unknown) {
      const axiosError = error as {
        message: string;
        response?: { status?: number; data?: unknown };
      };
      console.error('Kount 360 Payments Fraud v2.0 API error:', {
        message: axiosError.message,
        status: axiosError.response?.status,
        data: axiosError.response?.data,
      });

      throw new Error(
        `Kount API error: ${axiosError.message}${
          axiosError.response?.status
            ? ` (Status: ${axiosError.response.status})`
            : ''
        }`,
      );
    }
  }

  /**
   * Sends a Mode U request to update a transaction's status
   */
  public async sendModeURequest(authorizationStatus: string) {
    if (!this.transactionId) {
      throw new Error('Transaction ID is required for Mode U request');
    }

    try {
      // For Kount 360 Payments Fraud v2.0, use the new API format
      const token = await this.getBearerToken();

      const updateRequest = {
        transactions: [
          {
            authorizationStatus: {
              authResult: authorizationStatus === 'A' ? 'APPROVED' : 'DECLINED',
            },
          },
        ],
      };

      // Call Kount 360 Payments Fraud Update API via server-side endpoint
      const response = await this.httpClient.patch(
        '/api/kount/orders/update',
        updateRequest,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'SessionId': this.sessionId,
          },
          params: {
            orderId: this.transactionId,
          },
        },
      );

      return { status: 'success', data: response.data };
    } catch (error) {
      const axiosError = error as {
        message: string;
        response?: { status?: number; data?: unknown };
      };
      console.error('Kount 360 Payments Fraud v2.0 API error:', {
        message: axiosError.message,
        status: axiosError.response?.status,
        data: axiosError.response?.data,
      });

      throw new Error(
        `Kount API error: ${axiosError.message}${
          axiosError.response?.status
            ? ` (Status: ${axiosError.response.status})`
            : ''
        }`,
      );
    }
  }

  public getSessionId() {
    return this.sessionId;
  }

  public getTransactionId() {
    return this.transactionId;
  }

  public setTransactionId(transactionId: string) {
    this.transactionId = transactionId;
  }

  public getRiskLevel(
    response: KountResponse,
  ): 'APPROVE' | 'REVIEW' | 'DECLINE' {
    const omniscore = response.OMNISCORE;

    if (omniscore >= KOUNT.OMNISCORE.APPROVE) {
      return 'APPROVE';
    } else if (omniscore >= KOUNT.OMNISCORE.REVIEW) {
      return 'REVIEW';
    } else {
      return 'DECLINE';
    }
  }

  private requestConfig = () => {
    return {
      headers: {
        SessionId: this.sessionId,
      },
    };
  };

  /**
   * Maps a Kount 360 decision to a legacy auto code
   * @param decision Kount 360 decision (APPROVE, REVIEW, DECLINE)
   * @returns Legacy auto code (A, R, D)
   */
  private mapDecisionToLegacyAuto(decision: string | undefined): string {
    if (!decision) return 'R'; // Default to review if no decision

    switch (decision) {
      case KOUNT.DECISIONS.APPROVE:
        return 'A';
      case KOUNT.DECISIONS.DECLINE:
        return 'D';
      case KOUNT.DECISIONS.REVIEW:
      default:
        return 'R';
    }
  }
}

const isToday = (dateString: string) => {
  const inputDate = new Date(dateString);
  const today = new Date();

  // Set hours, minutes, seconds and milliseconds to 0 to compare only the date part
  inputDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  return inputDate.getTime() === today.getTime() ? 'Y' : 'N';
};

const daysUntil = (dateString: string): number => {
  // Create dates in local timezone
  const futureDate = new Date(dateString + 'T00:00:00');
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const diffInMilliseconds = futureDate.getTime() - today.getTime();
  const diffInDays = diffInMilliseconds / (1000 * 60 * 60 * 24);
  const result = Math.ceil(diffInDays);

  return result;
};
