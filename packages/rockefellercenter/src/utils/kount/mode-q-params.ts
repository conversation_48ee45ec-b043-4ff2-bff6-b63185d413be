import { KOUNT_MID } from '../../environment';

import { KOUNT } from './constants';

const { PENC, CURR, PTYP, SITE } = KOUNT.MODE_Q;

const MODE_Q_DEFAULT_PARAMS = {
  AUTH: KOUNT.AUTHORIZATION_STATUS.A, // Authorization Status returned to merchant from processor. A: Authorized, D: Declined, hardcoded to A
  VERS: KOUNT.VERS, // Specifies version of Kount, built into SDK, must be supplied by merchant if not using the SDK.
  MERC: KOUNT_MID, // Merchant ID assigned to the merchant by Kount.
  MODE: KOUNT.MODE.Q, // Specifies the mode of the request.
  MACK: KOUNT.MACK.Y, // Merchants acknowledgement to ship/process the order; Y or N
  PENC, // RIS Parameter for Payment Encoding
  CURR, // Country of currency submitted on order. 3 digit ISO code.
  PTYP, // Payment Type submitted by merchant
  SITE, // Website identifier of where order originated.
};

export type TProduct = {
  price: number;
  quantity: number;
  type: string;
  item: string;
  description: string;
};

type TModeQParams = {
  [key: string]: string | number | string[] | number[];
};

export type TModeQAttributes = {
  sessionId: string;
  total: string;
  email: string;
  cardNumber: string;
  products: TProduct[];
  remoteAddress: string;
  name: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingPostalCode: string;
  billingCountryCode: string;
  phone: string;
  orderNumber: string;
  customerID: string;
  ticketStartDate: string;
  cardBin?: string;
  cardLast4?: string;
  subtotal?: string;
  taxAmount?: string;
  outOfStateTaxAmount?: string;
  firstName?: string;
  lastName?: string;
};

export const modeQParams = (attributes: TModeQAttributes) => {
  const {
    sessionId,
    total,
    email,
    cardNumber,
    products,
    remoteAddress,
    name,
    billingAddress,
    billingCity,
    billingState,
    billingPostalCode,
    billingCountryCode,
    phone,
    orderNumber,
    customerID,
    ticketStartDate,
  } = attributes;

  let params: TModeQParams = {
    ...MODE_Q_DEFAULT_PARAMS,
    'SESS': sessionId,
    'TOTL': total,
    'EMAL': email,
    'PTOK': cardNumber,
    'IPAD': remoteAddress,
    'NAME': name,
    'B2A1': billingAddress,
    'B2CI': billingCity,
    'B2ST': billingState,
    'B2PC': billingPostalCode,
    'B2CC': billingCountryCode,
    'B2PN': phone,
    'ORDR': orderNumber,
    'UNIQ': customerID,
    'UDF[DATE_OF_TICKET]': ticketStartDate,
    'UDF[DAY_OF_TICKET]': isToday(ticketStartDate),
    'UDF[DAYS_UNTIL_TICKET]': daysUntil(ticketStartDate),
  };

  params = addProducts(params, products);

  return params;
};

const addProducts = (
  params: TModeQParams,
  products: TProduct[] | undefined,
): TModeQParams => {
  // Guard against undefined products
  if (!products || !Array.isArray(products)) {
    console.warn('No valid products array provided to addProducts');
    return params;
  }

  products.forEach((product, index) => {
    params[`PROD_TYPE[${index}]`] = product.type;
    params[`PROD_ITEM[${index}]`] = product.item;
    params[`PROD_DESC[${index}]`] = product.description;
    params[`PROD_PRICE[${index}]`] = product.price;
    params[`PROD_QUANT[${index}]`] = product.quantity;
  });
  return params;
};

const isToday = (dateString: string) => {
  const inputDate = new Date(dateString);
  const today = new Date();

  // Set hours, minutes, seconds and milliseconds to 0 to compare only the date part
  inputDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  return inputDate.getTime() === today.getTime() ? 'Y' : 'N';
};

const daysUntil = (dateString: string): number => {
  const futureDate = new Date(dateString);
  const today = new Date();

  // Set hours, minutes, seconds and milliseconds to 0 to compare only the date part
  futureDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);

  const diffInMilliseconds = futureDate.getTime() - today.getTime();
  const diffInDays = diffInMilliseconds / (1000 * 60 * 60 * 24);

  return Math.ceil(diffInDays);
};
