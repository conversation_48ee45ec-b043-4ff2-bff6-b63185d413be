/** @jsxImportSource theme-ui @jsxRuntime classic */
import React from 'react';
import { graphql, type HeadFC } from 'gatsby';

import { Layout } from '../layouts';
import HomeHeroBlock from '../blocks/HomeHeroBlock';
import OnlyHereBlock from '../blocks/OnlyHereBlock';
import HomeFeaturedEventsBlock from '../blocks/HomeFeaturedEventsBlock';
import HomeFlexSpaceBlock from '../blocks/HomeFlexSpaceBlock';
import HomeFeaturedStoriesBlock from '../blocks/HomeFeaturedStoriesBlock';
import ShareYourExperienceBlock from '../blocks/ShareYourExperienceBlock';
import { SitelinksSchema } from '../components/schema-dot-org/SitelinksSchema';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import type { PageConfig } from '../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Rock Center',
  logo: 'Rockefeller Center',
  logoLink: null,
  cta: {
    to: '/buy-tickets',
    label: 'buy_tickets | t',
  },
};

export const HomeMetaQuery = graphql`
  query HomeMeta {
    meta: sanityHomePage {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FULL_WIDTH
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
      }
    }
  }
`;

export default function Home(): React.JSX.Element {
  return (
    <Layout>
      <HomeHeroBlock id="home-hero" />
      <OnlyHereBlock id="home-only-here" theme="Rock Center Cream" />
      <HomeFeaturedEventsBlock />
      <HomeFlexSpaceBlock id="home-flex-space" />
      <HomeFeaturedStoriesBlock
        id="home-featured-stories"
        theme="Rock Center Navy"
      />
      <ShareYourExperienceBlock
        hashTags={['#RockefellerCenter']}
        id="share-your-experience"
        theme="Rock Center Cream"
        title="Want to share your experience at Rockefeller Center?"
      />
      <SitelinksSchema />
    </Layout>
  );
}

export const Head: HeadFC = () => <title>Home Page</title>;
