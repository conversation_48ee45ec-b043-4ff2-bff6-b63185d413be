/** @jsxImportSource theme-ui @jsxRuntime classic */

import { graphql } from 'gatsby';
import React, { useMemo } from 'react';
import {
  Box,
  Container,
  Faqs,
  Flex,
  Grid,
  HoursAndAddress,
  Link,
  SecondaryMenuBar,
  Section,
  useBreakpointIndex,
  Text,
  IntrinsicImage,
  AnchorSection,
} from '@tishman/components';
import { Block } from '@sanity/block-content-to-react';
import { IGatsbyImageData } from 'gatsby-plugin-image';
import { H } from '@hzdg/sectioning';

import { usePageData } from '../../utils';
import { PageConfig } from '../../PageConfig';
import { Layout } from '../../layouts';
import IntroText from '../../components/IntroText';
import {
  TicketOfferingTicket,
  TicketOfferings,
} from '../../components/TicketOfferings';
import {
  TicketOffering,
  getIncludedTicketsArray,
} from '../../utils/get-included-tickets';
import { ImageOrVideo } from '../../components/ImageOrVideo';
import { Oljs } from '../../components/Oljs';

import Dining from './dining';

export const pageConfig: PageConfig = {
  theme: 'Rock Center Black',
  logo: 'The Beam',
};

// Function to get included tickets array
const getIncludedTicketsArrayOnce = (
  theBeamLP: Queries.TheBeamLPQuery['sanityTheBeamLp'],
) => {
  return getIncludedTicketsArray(
    theBeamLP?.ticketOfferingsSection?.ticketOfferings as TicketOffering[],
    theBeamLP?.ticketOfferingsSection,
  );
};

export default function TheBeamPage(): React.JSX.Element {
  // QUERY VARIABLE
  const { sanityTheBeamLp } = usePageData<Queries.TheBeamLPQuery>();
  const isMobile = useBreakpointIndex() < 2;

  // TICKET OFFERINGS ARRAY LOGIC
  const includedTicketsArray = useMemo(
    () => getIncludedTicketsArrayOnce(sanityTheBeamLp),
    [sanityTheBeamLp],
  );

  // MEMOIZED SUBNAV LINKS
  const subnavLinks = useMemo(
    () =>
      sanityTheBeamLp?.subnavLinks?.map((link) => ({
        url: link?.url ?? '',
        label: link?.caption ?? '',
      })),
    [sanityTheBeamLp],
  );

  return (
    <Layout>
      <Oljs />
      <SecondaryMenuBar
        cta={pageConfig.cta}
        links={subnavLinks}
        sticky
        threshold={0.5}
        title="Explore the Beam"
      />
      <Box
        sx={{
          paddingTop: [5, 4],
          width: '100%',
        }}
      >
        {/* IMAGE OR VIDEO */}
        <Box
          sx={{
            position: 'relative',
            height: isMobile ? 'auto' : 758,
          }}
        >
          <ImageOrVideo
            aspectRatio="1500 / 752"
            image={
              sanityTheBeamLp?.heroImage?.asset?.gatsbyImageData ??
              ({} as IGatsbyImageData)
            }
          />
        </Box>
      </Box>
      {/* INTRO TEXT, HOURS, ADDRESS, LINKS */}
      <Section id="the-beam">
        <Container
          sx={{
            display: 'flex',
            flexDirection: ['column', null, 'row'],
            justifyContent: 'space-between',
            columnGap: [3],
            pt: [3, 4, 6],
            pb: [6, null, 8],
            px: [3, 4, 5],
          }}
        >
          <IntroText
            caption={sanityTheBeamLp?.heroSection?.subtitle ?? ''}
            links={[
              {
                label: 'Buy Tickets',
                url: '/buy-tickets',
              },
            ]}
            title={sanityTheBeamLp?.heroSection?.title ?? ''}
          />
          <HoursAndAddress
            addressHeading="Directions & Contact"
            closed={false}
            contactsInfo={[
              {
                type: 'phone',
                value: '************',
              },
              {
                type: 'email',
                value: '<EMAIL>',
              },
            ]}
            hours={[sanityTheBeamLp?.hour?.hourText ?? '']}
            hoursHeading="Hours"
            location={{
              address1: sanityTheBeamLp?.location?.address1 ?? '',
              address2: sanityTheBeamLp?.location?.address2 ?? '',
            }}
          />
        </Container>
      </Section>
      <Section theme="Rock Center">
        {/* TICKET OFFERINGS */}
        <TicketOfferings
          id={(sanityTheBeamLp?.subnavLinks?.[0]?.url ?? '').replace('#', '')}
          includedTicketsArray={includedTicketsArray as TicketOfferingTicket[]}
          theme="Rock Center"
          title={
            sanityTheBeamLp?.ticketOfferingsSection?.ticketOfferingsTitle ??
            'Experiences'
          }
        />
        {/* THE MEZZANINE CTA */}
        {/* ON MOBILE IMAGE GOES FIRST & IS FULL WIDTH */}
        <Box sx={{ display: ['block', 'none'] }}>
          {sanityTheBeamLp?.mezzanineCTA?.photo?.asset?.gatsbyImageData && (
            <IntrinsicImage
              alt={sanityTheBeamLp?.mezzanineCTA?.photo?.alt ?? ''}
              image={
                sanityTheBeamLp?.mezzanineCTA?.photo?.asset?.gatsbyImageData ??
                ({} as IGatsbyImageData)
              }
              sx={{ maxHeight: 400 }}
            />
          )}
        </Box>
        <Container sx={{ my: 4 }}>
          <Grid columns={[1, '2fr 3fr']} gap={4}>
            <Flex sx={{ flexDirection: 'column', my: 'auto' }}>
              {/* TITLE */}
              <Box>
                <H
                  sx={{ variant: 'styles.h1', fontFamily: 'headingSecondary' }}
                >
                  {sanityTheBeamLp?.mezzanineCTA?.title}
                </H>
              </Box>
              {/* DESCRIPTION */}
              <Box paddingY={3}>
                <Text variant="smallP">
                  {sanityTheBeamLp?.mezzanineCTA?.description}
                </Text>
              </Box>
            </Flex>
            {/* IMAGE */}
            <Box
              sx={{
                display: ['none', 'block'],
                height: '100%',
                maxHeight: '453px',
              }}
            >
              {sanityTheBeamLp?.mezzanineCTA?.photo?.asset?.gatsbyImageData && (
                <IntrinsicImage
                  alt={sanityTheBeamLp?.mezzanineCTA?.photo?.alt ?? ''}
                  image={
                    sanityTheBeamLp?.mezzanineCTA?.photo?.asset
                      ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                  }
                  sx={{ height: '100%' }}
                />
              )}
            </Box>
          </Grid>
        </Container>
        {/* THE WEATHER ROOM CTA */}
        {/* ON MOBILE IMAGE GOES FIRST & IS FULL WIDTH */}
        <Box sx={{ display: ['block', 'none'] }}>
          {sanityTheBeamLp?.weatherRoomCTA?.photo?.asset?.gatsbyImageData && (
            <IntrinsicImage
              alt={sanityTheBeamLp?.weatherRoomCTA?.photo?.alt ?? ''}
              image={
                sanityTheBeamLp?.weatherRoomCTA?.photo?.asset
                  ?.gatsbyImageData ?? ({} as IGatsbyImageData)
              }
              sx={{ maxHeight: 400 }}
            />
          )}
        </Box>
        <Container sx={{ my: 4 }}>
          <Grid columns={[1, '3fr 2fr']} gap={4}>
            {/* DESKTOP IMAGE */}
            <Box
              sx={{
                display: ['none', 'block'],
                height: '100%',
                maxHeight: '453px',
              }}
            >
              {sanityTheBeamLp?.weatherRoomCTA?.photo?.asset
                ?.gatsbyImageData && (
                <IntrinsicImage
                  alt={sanityTheBeamLp?.weatherRoomCTA?.photo?.alt ?? ''}
                  image={
                    sanityTheBeamLp?.weatherRoomCTA?.photo?.asset
                      ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                  }
                  sx={{ height: '100%' }}
                />
              )}
            </Box>
            <Flex sx={{ flexDirection: 'column', my: 'auto' }}>
              <Box>
                <H
                  sx={{ variant: 'styles.h1', fontFamily: 'headingSecondary' }}
                >
                  {sanityTheBeamLp?.weatherRoomCTA?.title}
                </H>
              </Box>
              <Box paddingY={3}>
                <Text variant="smallP">
                  {sanityTheBeamLp?.weatherRoomCTA?.description}
                </Text>
              </Box>
              <Flex
                paddingTop={4}
                sx={{ flexDirection: isMobile ? 'column' : 'row', rowGap: 3 }}
              >
                {sanityTheBeamLp?.weatherRoomCTA?.button1Text && (
                  <Link
                    sx={{
                      mr: [0, null, 3],
                      px: 5,
                      py: [3, 3],
                      textAlign: isMobile ? 'center' : 'left',
                    }}
                    to={
                      sanityTheBeamLp?.weatherRoomCTA?.button1Link ??
                      '/buy-tickets'
                    }
                    variant="button"
                  >
                    {sanityTheBeamLp?.weatherRoomCTA?.button1Text}
                  </Link>
                )}
                {sanityTheBeamLp?.weatherRoomCTA?.button2Text && (
                  <Link
                    sx={{
                      mb: [4, 0],
                      px: 5,
                      py: [3, 3],
                      textAlign: isMobile ? 'center' : 'left',
                    }}
                    to={
                      sanityTheBeamLp?.weatherRoomCTA?.button2Link ??
                      '/buy-tickets'
                    }
                    variant="buttonBorder"
                  >
                    {sanityTheBeamLp?.weatherRoomCTA?.button2Text}
                  </Link>
                )}
              </Flex>
            </Flex>
          </Grid>
        </Container>

        {/* TOP OF THE ROCK CALLOUT */}
        <Section sx={{ m: 'auto', py: 4 }} theme="Top of the Rock Blue">
          <Container>
            <Grid columns={[1, 1, '1.8fr 1.2fr']} gap={2} padding={3}>
              <Grid columns={[1, '2fr 3fr']}>
                <Box>
                  {sanityTheBeamLp?.topOfTheRockCTA?.photo?.asset
                    ?.gatsbyImageData && (
                    <IntrinsicImage
                      alt={
                        sanityTheBeamLp?.topOfTheRockCTA?.photo?.asset
                          ?.altText ?? ''
                      }
                      image={
                        sanityTheBeamLp?.topOfTheRockCTA?.photo.asset
                          .gatsbyImageData as IGatsbyImageData
                      }
                      ratio={160 / 110}
                    />
                  )}
                </Box>
                <Box paddingX={3}>
                  <Box marginY={3}>
                    <H
                      sx={{
                        variant: 'styles.h2',
                        fontSize: 7,
                        fontFamily: 'headingSecondary',
                      }}
                    >
                      {sanityTheBeamLp?.topOfTheRockCTA?.title}
                    </H>
                  </Box>
                  <Box>
                    <Text sx={{ fontSize: 3 }}>
                      {sanityTheBeamLp?.topOfTheRockCTA?.description}
                    </Text>
                  </Box>
                </Box>
              </Grid>
              <Flex
                paddingY={[4, 0]}
                sx={{
                  alignItems: 'center',
                  flexDirection: ['column', 'row'],
                  justifyContent: 'flex-end',
                }}
              >
                {sanityTheBeamLp?.topOfTheRockCTA?.button1Text &&
                  sanityTheBeamLp?.topOfTheRockCTA?.button1Link && (
                    <Link
                      sx={{
                        alignItems: 'center',
                        color: 'white',
                        display: 'flex',
                        justifyContent: 'center',
                        maxHeight: '58px',
                        width: ['100%', 'auto'],
                      }}
                      to={sanityTheBeamLp?.topOfTheRockCTA?.button1Link}
                      variant="button"
                    >
                      {sanityTheBeamLp?.topOfTheRockCTA?.button1Text}
                    </Link>
                  )}
                {sanityTheBeamLp?.topOfTheRockCTA?.button2Text &&
                  sanityTheBeamLp?.topOfTheRockCTA?.button2Link && (
                    <Link
                      sx={{
                        border: ['none', '1px solid #000'],
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginLeft: [0, 3],
                        maxHeight: '58px',
                        width: ['100%', 'auto'],
                      }}
                      to={sanityTheBeamLp?.topOfTheRockCTA?.button2Link}
                      variant="buttonBorder"
                    >
                      {sanityTheBeamLp?.topOfTheRockCTA?.button2Text}
                    </Link>
                  )}
              </Flex>
            </Grid>
          </Container>
        </Section>

        {/* NEWS SECTION */}
        <Section sx={{ py: 6 }} theme="Rock Center Cream">
          <Container>
            <Flex
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 4,
              }}
            >
              <H sx={{ variant: 'styles.h2', fontFamily: 'headingSecondary' }}>
                The Beam Experience in the News
              </H>
              <Link href="/news" variant="underline">
                SEE ALL
              </Link>
            </Flex>
            <Grid columns={[1, 2, 4]} gap={4}>
              {sanityTheBeamLp?.featuredNewsPress?.map((item, index) => (
                <Link
                  href={
                    (item?.externalLinks?.[0] as { url?: string })?.url ??
                    '/news'
                  }
                  key={index}
                  sx={{ textDecoration: 'none', color: 'inherit' }}
                >
                  <Box>
                    {item?.image?.asset?.gatsbyImageData && (
                      <IntrinsicImage
                        alt={item?.image?.alt ?? ''}
                        image={item?.image?.asset?.gatsbyImageData}
                        ratio={1}
                      />
                    )}
                    <Flex sx={{ justifyContent: 'space-between', my: 3 }}>
                      <Text sx={{ fontSize: 1, fontWeight: 500 }}>
                        {item?.source}
                      </Text>
                      <Text sx={{ fontSize: 1, fontWeight: 500 }}>
                        {item?.publishAt
                          ? (() => {
                              const date = new Date(item.publishAt);
                              const month = (date.getUTCMonth() + 1)
                                .toString()
                                .padStart(2, '0');
                              const day = date
                                .getUTCDate()
                                .toString()
                                .padStart(2, '0');
                              const year = date
                                .getUTCFullYear()
                                .toString()
                                .slice(-2);
                              return `${month}.${day}.${year}`;
                            })()
                          : ''}
                      </Text>
                    </Flex>
                    <Text sx={{ fontSize: 2, fontWeight: '500' }}>
                      {item?.excerpt}
                    </Text>
                  </Box>
                </Link>
              ))}
            </Grid>
          </Container>
        </Section>

        {/* FAQS */}
        <AnchorSection id="faqs" sx={{ py: 6 }}>
          <Container>
            <Faqs
              faqs={sanityTheBeamLp?._rawFaqs as unknown as Block[]}
              title="FAQs"
            />
          </Container>
        </AnchorSection>
      </Section>

      {/* DINING SECTION */}
      <Section id="dining" theme="Rock Center Navy">
        <Dining
          cards={(sanityTheBeamLp?.threeColumnGrid?.grid as never[]) ?? []}
          description={
            sanityTheBeamLp?.threeColumnGrid?.sectionDescription ?? ''
          }
          id="dining"
          sectionLink={
            sanityTheBeamLp?.threeColumnGrid?.sectionLink ?? '/dining'
          }
          title={sanityTheBeamLp?.threeColumnGrid?.sectionTitle ?? ''}
        />
      </Section>
    </Layout>
  );
}

// QUERY FOR SANITY DATA
export const query = graphql`
  query TheBeamLP {
    meta: sanityTheBeamLp {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FULL_WIDTH
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
      }
    }
    sanityTheBeamLp {
      heroImage {
        alt
        asset {
          gatsbyImageData(layout: FULL_WIDTH)
        }
      }
      heroSection {
        title
        subtitle
      }
      hour {
        hourText
      }
      subnavLinks {
        caption
        url
      }
      ticketOfferingsSection {
        ticketOfferingsTitle
        ticketOfferings {
          ... on SanityTicketCategory {
            title
            tickets {
              title
              url {
                url
                caption
              }
              image {
                alt
                asset {
                  gatsbyImageData(
                    layout: CONSTRAINED
                    placeholder: BLURRED
                    formats: [AUTO, WEBP]
                    width: 1080
                  )
                }
              }
              description {
                children {
                  text
                }
              }
              slugId
            }
          }
        }
        includedTickets {
          categoryTitle
          order
          slugId
        }
      }
      mezzanineCTA {
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      weatherRoomCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      topOfTheRockCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            altText
            gatsbyImageData(layout: FULL_WIDTH)
          }
        }
        title
      }
      featuredNewsPress {
        source
        excerpt
        publishAt
        externalLinks {
          ... on SanityUrlType {
            url
            caption
          }
        }
        image {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
          caption
        }
      }
      _rawFaqs
      threeColumnGrid {
        sectionTitle
        sectionDescription
        sectionLink
        grid {
          title
          description
          buttonText
          buttonLink
          imageOrVideo {
            ... on SanityImageOrVideoType {
              image {
                alt
                asset {
                  gatsbyImageData(
                    layout: CONSTRAINED
                    placeholder: BLURRED
                    formats: [AUTO, WEBP]
                  )
                }
              }
            }
          }
        }
      }
      location {
        title
        address1
        address2
      }
    }
  }
`;
