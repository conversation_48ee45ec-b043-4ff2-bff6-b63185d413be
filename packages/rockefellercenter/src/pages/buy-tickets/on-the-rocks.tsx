/** @jsxImportSource theme-ui @jsxRuntime classic */
import { graphql } from 'gatsby';
import React from 'react';

import {
  BuyTicketsIntroBlock,
  BuyTicketsDateAndTimeStepBlock,
  BuyTicketsAddOnStepBlock,
  BuyTicketsTicketStepBlock,
} from '../../blocks/VivaBuyTickets';
import { BuyTicketsVariantPageData, Wizard } from '../../components/BuyTickets';
import { BuyTicketsLayout } from '../../layouts/BuyTicketsLayout';
import { useTicketEditMode } from '../../utils/use-ticket-edit-mode';
import { Attraction, BosEvent, Flow } from '../../services/viva/constants';

import type { PageConfig } from '../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Top of the Rock Blue',
  pageName: null,
  logo: 'Observation Deck',
  hideSearchToggleButton: true,
  leftNav: {
    to: '/buy-tickets',
    label: 'BACK',
    variant: 'buyTicketsMenuBarBackArrow',
  },
};

interface OnTheRocksBuyTicketsProps {
  data: Queries.BuyTicketsTourQuery;
}

export default function OnTheRocksBuyTickets({
  data,
}: OnTheRocksBuyTicketsProps): React.JSX.Element {
  useTicketEditMode(Flow.ON_THE_ROCKS);

  return (
    <BuyTicketsLayout
      attraction={Attraction.Rink}
      data={data as BuyTicketsVariantPageData}
      event={BosEvent.ON_THE_ROCKS}
      flow={Flow.ON_THE_ROCKS}
    >
      <BuyTicketsIntroBlock />
      <Wizard totalSteps={3}>
        <BuyTicketsTicketStepBlock stepNumber={1} width="wide" />
        <BuyTicketsDateAndTimeStepBlock stepNumber={2} width="wide" />
        <BuyTicketsAddOnStepBlock
          allowedFlows={[Flow.ON_THE_ROCKS]}
          data={data as BuyTicketsVariantPageData}
          stepNumber={3}
          width="wide"
        />
      </Wizard>
    </BuyTicketsLayout>
  );
}

export const query = graphql`
  query BuyOnTheRocksTickets {
    page: buyTicketsFlowOnTheRocksJson(jsonId: { eq: "en-US" }) {
      hero {
        title
      }
      ticketStep {
        title
        description
        ticketTypes {
          label
          ticketTypeId
        }
      }
      dateStep {
        title
        description
      }
      timeStep {
        title
        description
        disclaimer
      }
      purchaseStep {
        title
      }
    }
    meta: buyTicketsFlowOnTheRocksJson(jsonId: { eq: "en-US" }) {
      meta {
        title
        description
      }
    }
    allSanityTicketCategory(
      filter: { tickets: { elemMatch: { slugId: { eq: "on-the-rocks" } } } }
    ) {
      nodes {
        tickets {
          slugId
          _rawWhatsIncluded
        }
      }
    }
  }
`;
