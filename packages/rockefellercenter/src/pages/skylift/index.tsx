/** @jsxImportSource theme-ui @jsxRuntime classic */

import { graphql } from 'gatsby';
import React, { useMemo } from 'react';
import {
  Box,
  Container,
  Faqs,
  Flex,
  Grid,
  HoursAndAddress,
  Link,
  SecondaryMenuBar,
  Section,
  useBreakpointIndex,
  Text,
  IntrinsicImage,
  AnchorSection,
} from '@tishman/components';
import { Block } from '@sanity/block-content-to-react';
import { IGatsbyImageData } from 'gatsby-plugin-image';
import { H } from '@hzdg/sectioning';

import { usePageData } from '../../utils';
import { PageConfig } from '../../PageConfig';
import { Layout } from '../../layouts';
import { ImageOrVideo } from '../../components/ImageOrVideo';
import {
  TicketOfferingTicket,
  TicketOfferings,
} from '../../components/TicketOfferings';
import {
  TicketOffering,
  getIncludedTicketsArray,
} from '../../utils/get-included-tickets';
import { Oljs } from '../../components/Oljs';

import Dining from './dining';

export const pageConfig: PageConfig = {
  theme: 'Rock Center Black',
  logo: 'Skylift',
};

const ratioTopOfTheRock = [2, 2, 2, 1.5];
const ratioAttractions = 1;
const sectionsSpacing = [4, 4, 4, 6];

export default function SkyliftPage(): React.JSX.Element {
  const { sanitySkyliftLp } = usePageData<Queries.SkyliftLPQuery>();
  const isMobile = useBreakpointIndex() < 2;

  // MEMOIZED SUBNAV LINKS
  const subNavLinks = useMemo(
    () =>
      sanitySkyliftLp?.subnavLinks?.map((link) => ({
        url: link?.url ?? '',
        label: link?.caption ?? '',
      })),
    [sanitySkyliftLp],
  );

  const includedTicketsArray = getIncludedTicketsArray(
    [...(sanitySkyliftLp?.ticketOfferings ?? [])] as TicketOffering[],
    sanitySkyliftLp,
  );

  return (
    <Layout {...pageConfig}>
      <Oljs />
      {sanitySkyliftLp?.subNavigationFeatureFlag ? (
        <SecondaryMenuBar
          links={subNavLinks}
          sticky
          threshold={0.5}
          title="Explore Skylift"
        />
      ) : null}

      {/* IMAGE */}
      <Box
        sx={{
          paddingTop: [5, 4],
          width: '100%',
        }}
      >
        {/* IMAGE OR VIDEO */}
        <Box
          sx={{
            position: 'relative',
            height: isMobile ? 'auto' : 858,
          }}
        >
          <Box sx={{ position: 'relative', height: [211, 858] }}>
            <ImageOrVideo
              aspectRatio="1500 / 752"
              image={
                sanitySkyliftLp?.heroImage?.asset?.gatsbyImageData ??
                ({} as IGatsbyImageData)
              }
            />
          </Box>
        </Box>
      </Box>

      {/* INTRO TEXT, HOURS, ADDRESS */}
      <Section id="skylift">
        <Container
          sx={{
            display: 'flex',
            flexDirection: ['column', null, 'row'],
            justifyContent: 'space-between',
            columnGap: 12,
            py: sectionsSpacing,
          }}
        >
          <Flex sx={{ flexDirection: 'column', maxWidth: '660px' }}>
            <Text
              sx={{
                fontSize: [30, 30, 30, 46],
                letterSpacing: '-0.46px',
                lineHeight: '113%',
                fontWeight: 500,
                marginBottom: '32px',
              }}
            >
              {sanitySkyliftLp?.heroTitle}
            </Text>
            <Text sx={{ fontSize: '18px' }} variant="headingSecondary">
              {sanitySkyliftLp?.heroDescription &&
                typeof sanitySkyliftLp.heroDescription === 'string' && (
                  <Text
                    dangerouslySetInnerHTML={{
                      __html: sanitySkyliftLp.heroDescription,
                    }}
                  />
                )}
            </Text>
            <Link
              href="/buy-tickets/"
              sx={{
                'fontWeight': 'medium',
                'my': '32px',
                'maxWidth': ['100%', '200px'],
                'textAlign': 'center',
                'variant': 'buttons.inverted',
                '&:hover': {
                  textDecoration: 'none',
                },
              }}
            >
              Buy Tickets
            </Link>
          </Flex>
          <HoursAndAddress
            addressHeading="Directions & Contact"
            closed={false}
            contactsInfo={[
              {
                type: 'phone',
                value: '************',
              },
              {
                type: 'email',
                value: '<EMAIL>',
              },
            ]}
            hours={[sanitySkyliftLp?.hour?.hourText ?? '']}
            hoursHeading="Hours"
            location={{
              address1: sanitySkyliftLp?.location?.address1 ?? '',
              address2: sanitySkyliftLp?.location?.address2 ?? '',
            }}
          />
        </Container>
      </Section>

      {/* TICKET OFFERINGS */}
      <Section id="tickets">
        <TicketOfferings
          id="tickets"
          includedTicketsArray={includedTicketsArray as TicketOfferingTicket[]}
          title="Ticket Offerings"
        />
      </Section>

      {/* NEWSLETTER */}
      {/* This may return */}
      {/* <Section theme="Top of the Rock Blue">
        <Container py={[40, 40, 40, 120]}>
          <Grid columns={[1, 1, '1fr 1fr']} gap={2}>
            <H
              sx={{
                variant: 'styles.h2',
                fontFamily: 'headingSecondary',
                fontSize: [30, 30, 30, 46],
              }}
            >
              {sanitySkyliftLp?.newsletterCTA}
            </H>
            <SkyliftNewsletter />
          </Grid>
        </Container>
      </Section> */}

      {/* ATTRACTIONS */}
      <Section sx={{ py: [4, 4, 4, 6] }} theme="Rock Center">
        <Container>
          <Grid columns={[1, 1, 1, 1, 2]} gap={[0, 0, 20, 20, 140]}>
            <Box sx={{ order: [1, 1, 1, 1, 2] }}>
              {sanitySkyliftLp?.attractionsHeader && (
                <H
                  sx={{
                    fontSize: [32, 32, 32, 60],
                    fontStyle: 'normal',
                    fontWeight: 500,
                    fontFamily: 'headingSecondary',
                    lineHeight: '112.5%',
                    letterSpacing: '-1.017px',
                    paddingBottom: [24, 24, 48],
                  }}
                >
                  {sanitySkyliftLp.attractionsHeader}
                </H>
              )}

              {sanitySkyliftLp?.thBeamCTA?.photo?.asset?.gatsbyImageData && (
                <IntrinsicImage
                  alt={sanitySkyliftLp?.thBeamCTA?.photo?.alt ?? ''}
                  image={
                    sanitySkyliftLp?.thBeamCTA?.photo?.asset?.gatsbyImageData ??
                    ({} as IGatsbyImageData)
                  }
                  ratio={ratioAttractions}
                  sx={{ overflow: 'hidden' }}
                />
              )}
              <H
                sx={{
                  variant: 'styles.h2',
                  fontFamily: 'headingSecondary',
                  paddingTop: '24px',
                  paddingBottom: '18px',
                  fontSize: '30px',
                }}
              >
                {sanitySkyliftLp?.thBeamCTA?.title}
              </H>
              <Text
                sx={{
                  color: 'colorTextPrimary',
                  fontFamily: 'body',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 'light',
                  lineHeight: '24px',
                  letterSpacing: '-0.457px',
                  opacity: '0.8',
                  marginBottom: '24px',
                }}
              >
                {sanitySkyliftLp?.thBeamCTA?.description}
              </Text>
              <Flex
                paddingTop={4}
                sx={{ flexDirection: isMobile ? 'column' : 'row', rowGap: 3 }}
              >
                {sanitySkyliftLp?.thBeamCTA?.button1Text && (
                  <Link
                    sx={{
                      mr: [0, null, 3],
                      px: 5,
                      py: [3, 3],
                      textAlign: isMobile ? 'center' : 'left',
                    }}
                    to={
                      sanitySkyliftLp?.thBeamCTA?.button1Link ?? '/buy-tickets'
                    }
                    variant="button"
                  >
                    {sanitySkyliftLp?.thBeamCTA?.button1Text}
                  </Link>
                )}
                {sanitySkyliftLp?.thBeamCTA?.button2Text && (
                  <Link
                    sx={{
                      mb: [4, 0],
                      px: 5,
                      py: [3, 3],
                      textAlign: isMobile ? 'center' : 'left',
                    }}
                    to={
                      sanitySkyliftLp?.thBeamCTA?.button2Link ?? '/buy-tickets'
                    }
                    variant="buttonBorder"
                  >
                    {sanitySkyliftLp?.thBeamCTA?.button2Text}
                  </Link>
                )}
              </Flex>
            </Box>
            <Box py={[24, 24, 48]} sx={{ order: [2, 2, 2, 2, 1] }}>
              <Box>
                {sanitySkyliftLp?.weatherRoomCTA?.photo?.asset
                  ?.gatsbyImageData && (
                  <IntrinsicImage
                    alt={sanitySkyliftLp?.weatherRoomCTA?.photo?.alt ?? ''}
                    image={
                      sanitySkyliftLp?.weatherRoomCTA?.photo?.asset
                        ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                    }
                    pt={50}
                    ratio={ratioAttractions}
                    sx={{ overflow: 'hidden' }}
                  />
                )}
                <H
                  sx={{
                    variant: 'styles.h2',
                    fontFamily: 'headingSecondary',
                    paddingTop: '24px',
                    paddingBottom: '18px',
                    fontSize: '30px',
                  }}
                >
                  {sanitySkyliftLp?.weatherRoomCTA?.title}
                </H>
                <Text
                  sx={{
                    color: 'colorTextPrimary',
                    fontSize: '16px',
                    fontStyle: 'normal',
                    fontWeight: 'light',
                    lineHeight: '150%',
                    opacity: '0.8',
                    marginBottom: '24px',
                  }}
                >
                  {sanitySkyliftLp?.weatherRoomCTA?.description}
                </Text>
                <Flex
                  paddingTop={4}
                  sx={{ flexDirection: isMobile ? 'column' : 'row', rowGap: 3 }}
                >
                  {sanitySkyliftLp?.weatherRoomCTA?.button1Text && (
                    <Link
                      sx={{
                        mr: [0, null, 3],
                        px: 5,
                        py: [3, 3],
                        textAlign: isMobile ? 'center' : 'left',
                      }}
                      to={
                        sanitySkyliftLp?.weatherRoomCTA?.button1Link ??
                        '/buy-tickets'
                      }
                      variant="button"
                    >
                      {sanitySkyliftLp?.weatherRoomCTA?.button1Text}
                    </Link>
                  )}
                  {sanitySkyliftLp?.weatherRoomCTA?.button2Text && (
                    <Link
                      sx={{
                        mb: [4, 0],
                        px: 5,
                        py: [3, 3],
                        textAlign: isMobile ? 'center' : 'left',
                      }}
                      to={
                        sanitySkyliftLp?.weatherRoomCTA?.button2Link ??
                        '/buy-tickets'
                      }
                      variant="buttonBorder"
                    >
                      {sanitySkyliftLp?.weatherRoomCTA?.button2Text}
                    </Link>
                  )}
                </Flex>
              </Box>
            </Box>
          </Grid>
        </Container>
      </Section>

      {/* FAQS */}
      {sanitySkyliftLp?.faqsFeatureFlag ? (
        <AnchorSection
          id="faqs"
          sx={{ py: sectionsSpacing }}
          theme="Rock Center"
        >
          <Container>
            <Faqs
              faqs={sanitySkyliftLp?._rawFaqs as unknown as Block[]}
              title="FAQs"
            />
          </Container>
        </AnchorSection>
      ) : null}

      {/* NEWS SECTION */}
      {sanitySkyliftLp?.newsFeatureFlag ? (
        <Section sx={{ py: sectionsSpacing }} theme="Rock Center Cream">
          <Container>
            <Flex
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 4,
              }}
            >
              <H sx={{ variant: 'styles.h2', fontFamily: 'headingSecondary' }}>
                What&apos;s Happening at Rockefeller Center
              </H>
              <Link href="/news" variant="underline">
                SEE ALL
              </Link>
            </Flex>
            <Grid columns={[1, 2, 4]} gap={4}>
              {sanitySkyliftLp?.featuredNewsPress?.map((item, index) => (
                <Link
                  href={
                    (item?.externalLinks?.[0] as { url?: string })?.url ??
                    '/news'
                  }
                  key={index}
                  sx={{ textDecoration: 'none', color: 'inherit' }}
                >
                  <Box>
                    {item?.image?.asset?.gatsbyImageData && (
                      <IntrinsicImage
                        alt={item?.image?.alt ?? ''}
                        image={item?.image?.asset?.gatsbyImageData}
                        ratio={1}
                      />
                    )}
                    <Flex sx={{ justifyContent: 'space-between', my: 3 }}>
                      <Text sx={{ fontSize: 1, fontWeight: 500 }}>
                        {item?.source}
                      </Text>
                      <Text sx={{ fontSize: 1, fontWeight: 500 }}>
                        {item?.publishAt
                          ? (() => {
                              const date = new Date(item.publishAt);
                              const month = (date.getUTCMonth() + 1)
                                .toString()
                                .padStart(2, '0');
                              const day = date
                                .getUTCDate()
                                .toString()
                                .padStart(2, '0');
                              const year = date
                                .getUTCFullYear()
                                .toString()
                                .slice(-2);
                              return `${month}.${day}.${year}`;
                            })()
                          : ''}
                      </Text>
                    </Flex>
                    <Text sx={{ fontSize: 2, fontWeight: '500' }}>
                      {item?.excerpt}
                    </Text>
                  </Box>
                </Link>
              ))}
            </Grid>
          </Container>
        </Section>
      ) : null}

      {/* DINING SECTION */}
      <Section
        id="dining"
        sx={{ py: sectionsSpacing }}
        theme="Rock Center Navy"
      >
        <Dining
          cards={(sanitySkyliftLp?.threeColumnGrid?.grid as never[]) ?? []}
          description={
            sanitySkyliftLp?.threeColumnGrid?.sectionDescription ?? ''
          }
          id="dining"
          sectionLink={sanitySkyliftLp?.threeColumnGrid?.sectionLink ?? '/dine'}
          title={sanitySkyliftLp?.threeColumnGrid?.sectionTitle ?? ''}
        />
      </Section>
    </Layout>
  );
}

// QUERY FOR SANITY DATA
export const query = graphql`
  query SkyliftLP {
    meta: sanitySkyliftLp(_id: { eq: "skyliftLP" }) {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FULL_WIDTH
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
      }
    }
    sanitySkyliftLp(_id: { eq: "skyliftLP" }) {
      subNavigationFeatureFlag
      faqsFeatureFlag
      newsFeatureFlag
      heroImage {
        alt
        asset {
          gatsbyImageData(layout: FULL_WIDTH)
        }
      }
      heroTitle
      heroDescription
      hour {
        hourText
      }
      subnavLinks {
        caption
        url
      }
      ticketOfferings {
        title
        tickets {
          title
          url {
            url
            caption
          }
          image {
            alt
            asset {
              gatsbyImageData(
                layout: CONSTRAINED
                placeholder: BLURRED
                formats: [AUTO, WEBP]
                width: 1080
              )
            }
          }
          description {
            children {
              text
            }
          }
          slugId
        }
      }
      includedTickets {
        categoryTitle
        order
        slugId
      }
      newsletterCTA
      attractionsHeader
      thBeamCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      weatherRoomCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      topOfTheRockCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            altText
            gatsbyImageData(layout: FULL_WIDTH)
          }
        }
        title
      }
      featuredNewsPress {
        source
        excerpt
        publishAt
        externalLinks {
          ... on SanityUrlType {
            url
            caption
          }
        }
        image {
          asset {
            gatsbyImageData(layout: CONSTRAINED, width: 300)
          }
          alt
          caption
        }
      }
      _rawFaqs
      threeColumnGrid {
        sectionTitle
        sectionDescription
        sectionLink
        grid {
          title
          description
          buttonText
          buttonLink
          imageOrVideo {
            ... on SanityImageOrVideoType {
              image {
                alt
                asset {
                  gatsbyImageData(layout: CONSTRAINED, width: 300)
                }
              }
            }
          }
        }
      }
      location {
        title
        address1
        address2
      }
    }
  }
`;
