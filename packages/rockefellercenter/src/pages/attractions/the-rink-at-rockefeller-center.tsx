/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  AnchorSection,
  CalloutRows,
  SecondaryMenuBar,
} from '@tishman/components';
import { H } from '@hzdg/sectioning';
import { graphql } from 'gatsby';
import { Helmet } from 'react-helmet';
import React, { Fragment } from 'react';

import { Layout } from '../../layouts';
import RinkHeroBlock from '../../blocks/RinkHeroBlock';
import RinkFaqBlock from '../../blocks/RinkFaqBlock';
import RinkWideCtaBlock from '../../blocks/RinkWideCtaBlock';
import RinkTicketOfferingsBlock from '../../blocks/RinkTicketOfferingsBlock';
import RinkCalloutGridBlock from '../../blocks/RinkCalloutGridBlock';
import RinkCrossLinkBlock from '../../blocks/RinkCrossLinkBlock';
import RinkHistoryBlock from '../../blocks/RinkHistoryBlock';
import RinkFeaturedEventsBlock from '../../blocks/RinkFeaturedEventsBlock';
import { Oljs } from '../../components/Oljs';

import type { PageConfig } from '../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'The Rink Navy',
  logo: 'The Rink',
  logoLink: null,
  // enabled in the rink season
  // cta: {
  //   to: '/buy-tickets/#the-rink',
  //   label: 'Buy Tickets',
  // },
  cta: { to: '/contact/private-events/', label: 'Plan An Event' },
};

export default function TheRinkAtRockefellerCenterPage({
  data: { meta, events, sanityTicketCategory },
}: {
  data: Queries.AttractionRinkQuery;
}): React.JSX.Element {
  const menuLinks = [
    Boolean(sanityTicketCategory?.enabled) && {
      url: '#ticket-offerings',
      label: 'Ticket Offerings',
    },
    Boolean(events?.featuredEvents?.length) && {
      url: '#events',
      label: 'Events',
    },

    { url: '#experiences', label: 'Summer Highlights' },
    { url: '#winter-rink', label: 'Winter Skating' },
    { url: '#history', label: 'Rink History' },
  ].filter((menuLink): menuLink is Exclude<typeof menuLink, false> =>
    Boolean(menuLink),
  );

  const structuredJSON = {
    '@context': 'https://schema.org',
    '@type': ['TouristAttraction', 'LocalBusiness'],
    'name': meta?.seo?.title,
    'description': meta?.seo?.description,
    'address': {
      '@type': 'PostalAddress',
      'streetAddress': meta?.location?.address1,
      'addressLocality': 'New York',
      'postalCode': '10020',
      'addressRegion': 'NY',
      'addressCountry': 'US',
      // telephone: '(*************',
    },
    'url':
      'https://www.rockefellercenter.com/attractions/the-rink-at-rockefeller-center/',
    'publicAccess': true,
    'isAccessibleForFree': false,
    // openingHours: ['The Rink is closed for the season.'],
    'currenciesAccepted': 'USD',
    'priceRange': '$30-$85',
    'paymentAccepted': 'CREDIT CARD',
  };

  if (!meta?.gallery?.items) throw new Error('Expected valid hero image');
  const metaImages = meta?.gallery?.items[0];
  if (metaImages?.__typename !== 'SanityImageType')
    throw new Error('unreachable');
  const metaImage = metaImages.asset?.gatsbyImageData?.images?.fallback?.src;

  return (
    <Fragment>
      <Oljs />
      <Layout theme="The Rink Navy">
        <SecondaryMenuBar
          cta={pageConfig.cta}
          links={menuLinks}
          // enabled in the rink season
          // rightNav={{
          //   to: '/private-events/venue/summer-rink/',
          //   label: 'Plan an Event',
          // }}
          sticky
          threshold={0.5}
          title="Explore the Rink"
        ></SecondaryMenuBar>

        <H sx={{ variant: 'text.hidden' }}>The Rink at Rockefeller Center</H>
        <RinkHeroBlock theme="The Rink Navy" />
        {/*
          ICE SKATING CTA
          This section will likely come back in summer 2025
        */}
        {/*
        <Section theme="Rock Center Black">
          {/* ON MOBILE IMAGE NEEDS TO BE OUTSIDE OF THE CONTAINER */}
        {/*
          <Box sx={{ display: ['block', 'none'] }}>
            {sanityAttractionRink?.iceSkatingCTA?.photo?.asset
              ?.gatsbyImageData && (
              <IntrinsicImage
                alt={sanityAttractionRink?.iceSkatingCTA?.photo?.alt ?? ''}
                image={
                  sanityAttractionRink?.iceSkatingCTA?.photo?.asset
                    ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                }
                sx={{ maxHeight: 400 }}
              />
            )}
          </Box>
          <Container sx={{ py: 6 }}>
            <Grid columns={[1, '3fr 3fr']} gap={4}>
              {/* TITLE, DESCRIPTION, BUTTONS */}
        {/*
              <Flex sx={{ flexDirection: 'column', my: 'auto' }}>
                <Box>
                  <H
                    sx={{
                      variant: 'styles.h1',
                      fontFamily: 'headingSecondary',
                    }}
                  >
                    {sanityAttractionRink?.iceSkatingCTA?.title}
                  </H>
                </Box>
                <Box paddingY={3}>
                  <Text variant="smallP">
                    {sanityAttractionRink?.iceSkatingCTA?.description}
                  </Text>
                </Box>
                <Flex
                  paddingTop={4}
                  sx={{ flexDirection: isMobile ? 'column' : 'row', rowGap: 3 }}
                >
                  <Link
                    sx={{
                      mr: [0, null, 3],
                      px: 5,
                      py: '24px',
                      textAlign: 'center',
                    }}
                    to={sanityAttractionRink?.iceSkatingCTA?.button1Link ?? ''}
                    variant="button"
                  >
                    {sanityAttractionRink?.iceSkatingCTA?.button1Text}
                  </Link>
                  <Link
                    sx={{
                      mb: [4, 0],
                      px: 5,
                      py: '24px',
                      textAlign: 'center',
                    }}
                    to={sanityAttractionRink?.iceSkatingCTA?.button2Link ?? ''}
                    variant="buttonBorder"
                  >
                    {sanityAttractionRink?.iceSkatingCTA?.button2Text}
                  </Link>
                </Flex>
              </Flex>
              {/* DESKTOP IMAGE */}
        {/*
              <Box
                sx={{
                  display: ['none', 'block'],
                  height: '100%',
                  maxHeight: '453px',
                }}
              >
                {sanityAttractionRink?.iceSkatingCTA?.photo?.asset
                  ?.gatsbyImageData && (
                  <IntrinsicImage
                    alt={sanityAttractionRink?.iceSkatingCTA?.photo?.alt ?? ''}
                    image={
                      sanityAttractionRink?.iceSkatingCTA?.photo?.asset
                        ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                    }
                    sx={{ height: '100%' }}
                  />
                )}
              </Box>
            </Grid>
          </Container>
        </Section>
        */}
        {Boolean(sanityTicketCategory?.enabled) && (
          <RinkTicketOfferingsBlock id="ticket-offerings" theme="The Rink" />
        )}
        {Boolean(events?.wideCtaFeatureFlag) && (
          <RinkWideCtaBlock theme="The Rink" />
        )}
        <RinkFeaturedEventsBlock id="events" theme="The Rink" />
        {Boolean(events?.calloutGridFeatureFlag) && (
          <RinkCalloutGridBlock id="experiences" theme="The Rink" />
        )}

        <AnchorSection id="winter-rink">
          {events?.calloutRowsToggle && (
            <CalloutRows calloutRows={events?.calloutRows ?? []} />
          )}
        </AnchorSection>

        <RinkFaqBlock id="faqs" theme="The Rink" />
        <RinkHistoryBlock id="history" theme="The Rink Navy" />
        <RinkCrossLinkBlock theme="The Rink Blue" />
        <Helmet
          meta={[
            {
              property: `og:image`,
              content: metaImage,
            },
            {
              property: `twitter:image`,
              content: metaImage,
            },
          ]}
        >
          <script type="application/ld+json">
            {JSON.stringify(structuredJSON)}
          </script>
        </Helmet>
      </Layout>
    </Fragment>
  );
}

export const query = graphql`
  query AttractionRink {
    meta: sanityAttractionRink {
      seo {
        title: metaTitle
        description: metaDescription
      }
      location {
        address1
      }
      gallery {
        items {
          __typename
          ... on SanityImageType {
            asset {
              gatsbyImageData(width: 1600, layout: CONSTRAINED)
            }
            alt
            caption
          }
        }
      }
    }
    alert: sanityAttractionRink {
      alert {
        title
        description
        link {
          url
          caption
        }
        type
      }
    }
    events: sanityAttractionRink(
      _id: { eq: "theRink" }
      _type: { eq: "attraction.rink" }
    ) {
      wideCtaFeatureFlag
      calloutGridFeatureFlag
      wideCta {
        title
        caption
        link {
          caption
          url
        }
      }
      faqTitle
      _rawFaqs
      calloutGridTitle
      calloutGridCards {
        title
        caption
        image {
          asset {
            gatsbyImageData(width: 1600, layout: CONSTRAINED)
          }
          alt
          caption
        }
        externalLinks {
          ... on SanityUrlType {
            _type
            url
            caption
          }
          ... on SanityFileType {
            _type
            asset {
              url
            }
            caption
          }
        }
      }

      calloutRowsToggle
      calloutRows {
        _key
        title
        colorSchema
        _rawLongText
        image {
          alt
          asset {
            gatsbyImageData(
              width: 800
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
        cta {
          url
          caption
        }
      }

      featuredEvents {
        ... on SanityEventTreeLighting {
          id
        }
        ... on SanityEvent {
          id
        }
      }
      crossLinks {
        title
        image {
          caption
          alt
          asset {
            gatsbyImageData(width: 1600, layout: CONSTRAINED)
          }
        }
        link {
          caption
          url
        }
      }
    }
    sanityTicketCategory(slug: { current: { eq: "the-rink" } }) {
      enabled
      tickets {
        description: _rawDescription(resolveReferences: { maxDepth: 10 })
        id: _key
        image {
          alt
          asset {
            gatsbyImageData(width: 1600, layout: CONSTRAINED)
          }
        }
        includedFeatures
        price
        tertiaryUrl {
          caption
          url
        }
        title
        url {
          caption
          url
        }
      }
    }
    sanityAttractionRink {
      iceSkatingCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
    }
  }
`;
