/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  AnchorSection,
  Box,
  Flex,
  Grid,
  IntrinsicImage,
  Link,
  SecondaryMenuBar,
  Section,
  SocialCarouselCardProps,
  Text,
  ThemeProvider,
  getThemeByName,
  useCurator,
  Modal,
  // useThemeUI,
  // useMobileView,
  Container,
  useBreakpointIndex,
} from '@tishman/components';
import { H } from '@hzdg/sectioning';
import { graphql } from 'gatsby';
import { Helmet } from 'react-helmet-async';
// import { useLocation } from '@reach/router';
import React, { memo, useEffect, useMemo, useState } from 'react';
import * as Icons from '@tishman/icons';

// import JourneyToTheTopBlock from '../../../blocks/JourneyToTheTopBlock';

import TorAppDownloadBlock from '../../../blocks/TorAppDownloadBlock';
import TorCrossLinkBlock from '../../../blocks/TorCrossLinkBlock';
import TorFaqBlock from '../../../blocks/TorFaqBlock';
import TorFeaturedEventsBlock from '../../../blocks/TorFeaturedEventsBlock';
// import TorFeaturedGalleryBlock from '../../../blocks/TorFeaturedGalleryBlock';
import TorHeroBlock from '../../../blocks/TorHeroBlock';
import TorPlanYourVisitBlock from '../../../blocks/TorPlanYourVisitBlock';
import TorTicketInfoBlock from '../../../blocks/TorTicketInfoBlock';
import TorWideCtaBlock from '../../../blocks/TorWideCtaBlock';
import { Layout } from '../../../layouts';
// import { useNetlifyBotCheck } from '../../../utils';
import { Oljs } from '../../../components/Oljs';
import {
  TicketOfferings,
  TicketOfferingProps,
  TicketOfferingTicket,
} from '../../../components/TicketOfferings';
import {
  ReviewProps,
  TripAdvisorReviews,
} from '../../../components/TripAdvisorReviews';
import { TorSocialSection } from '../../../../src/components/TopOfTheRock/TorSocialSection';
import { getIncludedTicketsArray } from '../../../utils/get-included-tickets';
import { planYourVisitPathnames } from '../../../../translations/pathnames';
import { useLocalizedLink } from '../../../utils/use-localized-link';
import {
  TorExperienceSection,
  ExperienceProps,
} from '../../../blocks/TorExperienceSection';

import type { TicketOffering } from '../../../utils/get-included-tickets';
import type { IGatsbyImageData } from 'gatsby-plugin-image';
import type { PageConfig } from '../../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Top of the Rock',
  logo: 'Observation Deck',
  cta: {
    to: '/buy-tickets/',
    label: 'Buy Tickets',
  },
};

// Function to get included tickets array
const getIncludedTicketsArrayOnce = (
  sanityAttractionTor: Queries.AttractionTorQuery['sanityAttractionTor'],
) => {
  return getIncludedTicketsArray(
    [...(sanityAttractionTor?.ticketOfferings ?? [])] as TicketOffering[],
    sanityAttractionTor,
  );
};

export default function TopOfTheRockObservationDeckPage({
  data: { meta, site, sanityAttractionTor },
}: {
  data: Queries.AttractionTorQuery;
}): React.JSX.Element {
  const isMobile = useBreakpointIndex() < 2;

  const structuredJSON = {
    '@context': 'https://schema.org',
    '@type': ['TouristAttraction', 'LocalBusiness'],
    'name': meta?.seo?.title,
    'description': meta?.seo?.description,
    'image': '#',
    'address': {
      '@type': 'PostalAddress',
      'streetAddress': meta?.location?.address,
      'addressLocality': 'New York',
      'postalCode': '10112',
      'addressRegion': 'NY',
      'addressCountry': 'US',
      // telephone: '(*************',
    },
    'url':
      'https://www.rockefellercenter.com/attractions/top-of-the-rock-observation-deck/',
    'publicAccess': true,
    'isAccessibleForFree': false,
    // openingHours: [hour?.hourText],
    'currenciesAccepted': 'USD',
    'priceRange': '$32-$136',
    'paymentAccepted': 'CREDIT CARD',
  };

  // SOCIAL SECTION LOGIC
  const feedName = 'Top of the Rock';
  const useCuratorResult = useCurator({ feedName });

  const cards = useMemo(() => {
    if (useCuratorResult.error) return null;

    return useCuratorResult.data.reduce<SocialCarouselCardProps[]>((acc, d) => {
      if (!d) throw new Error('Expected valid data');
      if (!d.id) {
        console.warn('Expected valid Curator post id');
        return acc;
      }
      if (!d.text) {
        console.warn(`Expected valid caption, Curator post id: ${d.id}`);
        return acc;
      }
      if (!d.image) {
        console.warn(`Expected valid image, Curator post id: ${d.id}`);
        return acc;
      }
      if (!d.source_created_at) {
        console.warn(`Expected valid creation time, Curator post id: ${d.id}`);
        return acc;
      }

      acc.push({
        id: `${d.id}`,
        caption: d.text,
        image: d.image,
        url: d.url,
      });
      return acc;
    }, []);
  }, [useCuratorResult]);

  // Serve bots a flattened version of the page
  // const location = useLocation();
  // useNetlifyBotCheck(location.href);

  // TICKET OFFERINGS ARRAY LOGIC
  const includedTicketsArray = useMemo(
    () => getIncludedTicketsArrayOnce(sanityAttractionTor),
    [sanityAttractionTor],
  );

  // BEAM DATA -- This is used for the Beam experience & the pop up modal
  const beamGridData = sanityAttractionTor?.threeColumnGrid?.grid;

  // MODAL
  const setCookie = () => {
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1); // Set the cookie to expire in 1 year

    document.cookie = `the-beam-cta=visited; expires=${expirationDate.toUTCString()}; path=/`;
  };

  const checkCookie = () => {
    if (typeof document !== 'undefined') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie === 'the-beam-cta=visited') {
          return true;
        }
      }
    }
    return false;
  };

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!checkCookie()) {
      setIsVisible(true);
      setCookie();
    }
  }, []);

  const closeModal = () => {
    setIsVisible(false);
  };

  const PopUpModal = (): React.JSX.Element | null => {
    return beamGridData ? (
      <Flex
        data-close-exit-modal
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            closeModal();
          }
        }}
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 10,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {/* INNER MODAL */}
        <Flex
          sx={{
            maxHeight: '520px',
            width: ['350px', '637px'],
            backgroundColor: 'white',
          }}
        >
          <Box sx={{ border: '2px solid black', p: 2, width: '100%' }}>
            <Box
              sx={{
                border: '2px solid black',
                height: '100%',
                p: 1,
                width: '100%',
              }}
            >
              <Box
                sx={{
                  height: '100%',
                  maxHeight: ['150px', '260px'],
                  position: 'relative',
                }}
              >
                {beamGridData[0]?.imageOrVideo?.image?.asset
                  ?.gatsbyImageData && (
                  <IntrinsicImage
                    alt={beamGridData[0]?.imageOrVideo?.image?.alt ?? ''}
                    image={
                      beamGridData[0]?.imageOrVideo?.image?.asset
                        ?.gatsbyImageData
                    }
                    sx={{
                      'height': '100%',
                      '> .gatsby-image-wrapper': {
                        width: '100%',
                        height: '100%',
                        img: {
                          objectFit: 'contain',
                        },
                      },
                    }}
                  />
                )}
                <Box
                  onClick={closeModal}
                  sx={{
                    m: 2,
                    position: 'absolute',
                    right: 0,
                    top: 0,
                  }}
                >
                  <Icons.Close style={{ height: 12, width: 12 }} />
                </Box>
              </Box>
              <Flex
                sx={{
                  flexDirection: ['column', 'row'],
                  justifyContent: 'space-between',
                  alignItems: ['flex-start', 'center'],
                  paddingY: 2,
                  paddingX: 3,
                }}
              >
                <Box sx={{ width: ['100%', '70%'] }}>
                  <Text paddingY={2} sx={{ fontSize: 3 }} variant="smallTitle">
                    Introducing The Beam!
                  </Text>
                  <Text sx={{ fontSize: 1 }} variant="smallP">
                    {beamGridData[0]?.description}
                  </Text>
                </Box>
                <Flex sx={{ justifyContent: 'center' }}>
                  <Link
                    sx={{
                      width: '150px',
                      my: 'auto',
                      mt: 2,
                      py: 3,
                      px: 0,
                      textAlign: 'center',
                    }}
                    to={beamGridData[0]?.buttonLink ?? '/buy-tickets'}
                    variant="button"
                  >
                    {beamGridData[0]?.buttonText}
                  </Link>
                </Flex>
              </Flex>
            </Box>
          </Box>
        </Flex>
      </Flex>
    ) : null;
  };

  const TicketOfferingsComponent = ({
    id,
    includedTicketsArray,
    link,
    theme,
    title,
  }: TicketOfferingProps) => (
    <TicketOfferings
      id={id}
      includedTicketsArray={includedTicketsArray}
      link={link}
      theme={theme}
      title={title}
    />
  );

  const MemoizedTicketOfferings = memo(TicketOfferingsComponent);
  const getLocalizedLink = useLocalizedLink();

  return (
    <Layout theme="Top of the Rock">
      <SecondaryMenuBar
        backToTop="Back to Top"
        cta={{
          to: '/buy-tickets/',
          label: 'Buy Tickets',
        }}
        links={[
          { url: '#tor-ticket-offerings', label: 'Ticket Offerings' },
          { url: '#skylift', label: 'Skylift' },
          { url: '#the-beam-experience', label: 'The Beam' },
          { url: '#the-weather-room', label: 'The Weather Room' },
          { url: '#plan-your-visit', label: 'Plan Your Visit' },
          {
            url: getLocalizedLink(
              planYourVisitPathnames.TOR_PLAN_YOUR_VISIT_TICKETS ?? '',
            ),
            label: 'Compare Tickets',
          },
          { url: '#faqs', label: 'FAQs' },
        ]}
        sticky
        title="Explore Top of the Rock"
      />
      <H sx={{ variant: 'text.hidden' }}>{meta?.seo?.title}</H>
      <TorHeroBlock />
      <MemoizedTicketOfferings
        id="tor-ticket-offerings"
        includedTicketsArray={includedTicketsArray as TicketOfferingTicket[]}
        link={getLocalizedLink(
          '/attractions/top-of-the-rock-observation-deck/plan-your-visit/?tab=tickets&tor=true',
        )}
        theme="Rock Center"
        title="Get Tickets Today"
      />
      <TorTicketInfoBlock />
      <TorExperienceSection
        beamData={
          (sanityAttractionTor?.threeColumnGrid as ExperienceProps) ?? undefined
        }
        skyliftData={
          (sanityAttractionTor?.skylift ?? undefined) as
            | ExperienceProps
            | undefined
        }
      />
      {/* THE MEZZANINE CTA */}
      <Section theme="Rock Center">
        {/* ON MOBILE IMAGE GOES FIRST & IS FULL WIDTH */}
        <Box sx={{ display: ['block', 'none'] }}>
          {sanityAttractionTor?.mezzanineCTA?.photo?.asset?.gatsbyImageData && (
            <IntrinsicImage
              alt={sanityAttractionTor?.mezzanineCTA?.photo?.alt ?? ''}
              image={
                sanityAttractionTor?.mezzanineCTA?.photo?.asset
                  ?.gatsbyImageData ?? ({} as IGatsbyImageData)
              }
              sx={{ maxHeight: 400 }}
            />
          )}
        </Box>
        <Container sx={{ my: 4 }}>
          <Grid columns={[1, '2fr 3fr']} gap={4}>
            <Flex sx={{ flexDirection: 'column', my: 'auto' }}>
              {/* TITLE */}
              <Box>
                <H
                  sx={{ variant: 'styles.h1', fontFamily: 'headingSecondary' }}
                >
                  {sanityAttractionTor?.mezzanineCTA?.title}
                </H>
              </Box>
              {/* DESCRIPTION */}
              <Box paddingY={3}>
                <Text variant="smallP">
                  {sanityAttractionTor?.mezzanineCTA?.description}
                </Text>
              </Box>
            </Flex>
            {/* IMAGE */}
            <Box
              sx={{
                display: ['none', 'block'],
                height: '100%',
                maxHeight: '453px',
              }}
            >
              {sanityAttractionTor?.mezzanineCTA?.photo?.asset
                ?.gatsbyImageData && (
                <IntrinsicImage
                  alt={sanityAttractionTor?.mezzanineCTA?.photo?.alt ?? ''}
                  image={
                    sanityAttractionTor?.mezzanineCTA?.photo?.asset
                      ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                  }
                  sx={{ height: '100%' }}
                />
              )}
            </Box>
          </Grid>
        </Container>
      </Section>
      {/* THE WEATHER ROOM CTA */}
      <AnchorSection id="the-weather-room" theme="Rock Center">
        {/* ON MOBILE IMAGE GOES FIRST & IS FULL WIDTH */}
        {sanityAttractionTor?.weatherRoomCTA?.photo?.asset?.gatsbyImageData && (
          <Box sx={{ display: ['block', 'none'] }}>
            <IntrinsicImage
              alt={sanityAttractionTor?.weatherRoomCTA?.photo?.alt ?? ''}
              image={
                sanityAttractionTor?.weatherRoomCTA?.photo?.asset
                  ?.gatsbyImageData ?? ({} as IGatsbyImageData)
              }
              sx={{ maxHeight: 400 }}
            />
          </Box>
        )}
        <Container sx={{ my: 4 }}>
          <Grid columns={[1, '3fr 2fr']} gap={4}>
            {/* DESKTOP IMAGE */}
            <Box
              sx={{
                display: ['none', 'block'],
                height: '100%',
                maxHeight: '453px',
              }}
            >
              {sanityAttractionTor?.weatherRoomCTA?.photo?.asset
                ?.gatsbyImageData && (
                <IntrinsicImage
                  alt={sanityAttractionTor?.weatherRoomCTA?.photo?.alt ?? ''}
                  image={
                    sanityAttractionTor?.weatherRoomCTA?.photo?.asset
                      ?.gatsbyImageData ?? ({} as IGatsbyImageData)
                  }
                  sx={{ height: '100%' }}
                />
              )}
            </Box>
            <Flex sx={{ flexDirection: 'column', my: 'auto' }}>
              <Box>
                <H
                  sx={{ variant: 'styles.h1', fontFamily: 'headingSecondary' }}
                >
                  {sanityAttractionTor?.weatherRoomCTA?.title}
                </H>
              </Box>
              <Box paddingY={3}>
                <Text variant="smallP">
                  {sanityAttractionTor?.weatherRoomCTA?.description}
                </Text>
              </Box>
              <Flex
                paddingTop={4}
                sx={{ flexDirection: isMobile ? 'column' : 'row', rowGap: 3 }}
              >
                {sanityAttractionTor?.weatherRoomCTA?.button1Link &&
                  sanityAttractionTor?.weatherRoomCTA?.button1Text && (
                    <Link
                      sx={{
                        mr: [0, null, 3],
                        px: 5,
                        py: [3, 3],
                        textAlign: isMobile ? 'center' : 'left',
                      }}
                      to={sanityAttractionTor.weatherRoomCTA.button1Link}
                      variant="button"
                    >
                      {sanityAttractionTor.weatherRoomCTA.button1Text}
                    </Link>
                  )}

                {sanityAttractionTor?.weatherRoomCTA?.button2Link &&
                  sanityAttractionTor?.weatherRoomCTA?.button2Text && (
                    <Link
                      sx={{
                        mb: [4, 0],
                        px: 5,
                        py: [3, 3],
                        textAlign: isMobile ? 'center' : 'left',
                      }}
                      to={sanityAttractionTor.weatherRoomCTA.button2Link}
                      variant="buttonBorder"
                    >
                      {sanityAttractionTor.weatherRoomCTA.button2Text}
                    </Link>
                  )}
              </Flex>
            </Flex>
          </Grid>
        </Container>
      </AnchorSection>
      {/* PLAN YOUR VISIT */}
      <TorPlanYourVisitBlock id="plan-your-visit" />
      {/* WIDE CTA */}
      <TorWideCtaBlock theme="Top of the Rock" />
      {/* SOCIAL SECTION */}
      <TorSocialSection
        cards={cards}
        headline="How was your view from Top of the Rock?"
        subheadline="We'd love to see pics from your visit to Top of the Rock. Be sure to share them!"
      />
      <TripAdvisorReviews
        reviews={
          (sanityAttractionTor?.tripadvisorsReview || []) as ReviewProps[]
        }
        theme="Top of the Rock Blue"
      />
      {/* NOTHING BEATS THIS VIEW */}
      {/* TODO : These 2 are overengineered & can be simplified in the refactor for gatsby-plugin-image */}
      {/* <TorFeaturedGalleryBlock id="the-experience" theme="Top of the Rock" /> */}
      {/* <JourneyToTheTopBlock /> */}
      <TorFaqBlock id="faqs" />
      <TorFeaturedEventsBlock />
      <TorAppDownloadBlock id="audio-tour" theme="Top of the Rock Blue" />
      <TorCrossLinkBlock
        crossLinks={
          (sanityAttractionTor?.crossLinks ?? []) as Queries.SanityCrossLink[]
        }
      />
      {/* MODAL */}
      <ThemeProvider theme={getThemeByName('Rock Center')}>
        <Modal
          id="the-beam-popup-modal"
          isOpen={isVisible}
          onClose={closeModal}
        >
          {isVisible && beamGridData && <PopUpModal />}
        </Modal>
      </ThemeProvider>
      <Helmet htmlAttributes={{ lang: 'en-US' }}>
        <meta content="en-US" httpEquiv="content-language" />
        <link
          href={site!.siteMetadata!.siteUrl!}
          hrefLang="x-default"
          rel="alternate"
        />
        <script type="application/ld+json">
          {JSON.stringify(structuredJSON)}
        </script>
      </Helmet>
      <Oljs />
    </Layout>
  );
}

export const query = graphql`
  query AttractionTor {
    site {
      siteMetadata {
        siteUrl
      }
    }
    meta: sanityAttractionTor {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
      }
      location {
        address: address1
      }
    }
    alert: sanityAttractionTor {
      alert {
        title
        description
        link {
          url
          caption
        }
        type
      }
    }
    sanityAttractionTor {
      audioTour {
        title
      }
      crossLinks {
        title
        image {
          caption
          alt
          asset {
            gatsbyImageData(width: 1600, layout: CONSTRAINED)
          }
        }
        link {
          caption
          url
        }
      }
      contactsInfo {
        type
        value
      }
      tripadvisorsReview {
        review
        source
      }
      # featuredGallery {
      #   items {
      #     imageOrVideo {
      #       asset {
      #         gatsbyImageData(
      #           layout: FULL_WIDTH
      #           placeholder: BLURRED
      #           formats: [AUTO, WEBP]
      #         )
      #       }
      #     }
      #     title
      #   }
      #   title
      # }
      heroCTA {
        title
        bodyCopy
      }
      hour {
        hours {
          closesAt
          day
          opensAt
        }
        hourText
      }
      # journeyToTheTopGallery {
      #   items {
      #     imageOrVideo {
      #       ...GatsbySanityImageOrVideo
      #     }
      #     title
      #   }
      #   title
      # }
      location {
        address1
        address2
        title
      }
      ticketComparison {
        callout {
          bodyCopy
          cta {
            caption
            url
          }
          title
        }
        description: _rawDescription(resolveReferences: { maxDepth: 10 })
        title
      }
      ticketInfo {
        copy
        icon
        title
      }
      ticketOfferings {
        title
        tickets {
          title
          url {
            url
            caption
          }
          image {
            alt
            asset {
              gatsbyImageData(
                layout: CONSTRAINED
                placeholder: BLURRED
                formats: [AUTO, WEBP]
              )
            }
          }
          description {
            children {
              text
            }
          }
          slugId
        }
      }
      wideCta {
        caption
        link {
          caption
          url
        }
        title
      }
      planYourVisit {
        title
        description
        info {
          _key
          description {
            children {
              text
            }
          }
          externalLink {
            caption
          }
          heading
        }
        primaryCTA {
          caption
          url
        }
      }
      includedTickets {
        categoryTitle
        slugId
        order
      }
      mezzanineCTA {
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      weatherRoomCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            gatsbyImageData(
              layout: CONSTRAINED
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
          alt
        }
        title
      }
      skylift {
        grid {
          buttonText
          buttonLink
          title
          description
          imageOrVideo {
            image {
              asset {
                gatsbyImageData(
                  layout: CONSTRAINED
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                  width: 1200
                )
              }
              alt
            }
            video {
              videoFile {
                asset {
                  source {
                    url
                  }
                  url
                }
              }
            }
          }
        }
        sectionLink
        sectionTitle
      }
      threeColumnGrid {
        grid {
          buttonText
          buttonLink
          title
          description
          imageOrVideo {
            image {
              asset {
                gatsbyImageData(
                  layout: CONSTRAINED
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                  width: 1200
                )
              }
              alt
            }
            video {
              videoFile {
                asset {
                  source {
                    url
                  }
                  url
                }
              }
            }
          }
        }
        sectionLink
        sectionTitle
      }
    }
    allSanityTicketCategory(
      filter: {
        slug: {
          current: {
            in: [
              "citypass"
              "top-of-the-rock-observation-deck"
              "rockefeller-center-tour"
            ]
          }
        }
      }
    ) {
      nodes {
        availableFeatures
        slug {
          current
        }
        tickets {
          description: _rawDescription(resolveReferences: { maxDepth: 10 })
          id: _key
          image {
            alt
            asset {
              gatsbyImageData(
                layout: CONSTRAINED
                placeholder: BLURRED
                formats: [AUTO, WEBP]
                width: 572
                height: 516
              )
            }
          }
          includedFeatures
          price
          secondaryUrl {
            caption
            url
          }
          tertiaryUrl {
            caption
            url
          }
          title
          url {
            caption
            url
          }
          descriptionLink {
            url
            caption
          }
          torCitypassBanner {
            torAttractionsCitypassBannerImageDesktop {
              alt
              asset {
                gatsbyImageData(
                  layout: CONSTRAINED
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                  width: 1200
                )
              }
            }
            torAttractionsCitypassBannerImageMobile {
              alt
              asset {
                gatsbyImageData(
                  layout: CONSTRAINED
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                  width: 700
                )
              }
            }
            torAttractionsCitypassBannerUrl {
              url
              caption
            }
          }
        }
      }
    }
  }
`;
