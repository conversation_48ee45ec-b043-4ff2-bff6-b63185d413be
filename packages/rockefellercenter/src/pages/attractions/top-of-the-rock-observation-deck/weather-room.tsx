/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  Box,
  Container,
  Grid,
  HoursAndAddress,
  IntrinsicImage,
  Link,
  SecondaryMenuBar,
  Section,
  SocialCarouselCardProps,
  Text,
  useCurator,
} from '@tishman/components';
import { graphql } from 'gatsby';
import { Flex } from 'theme-ui';
import { H } from '@hzdg/sectioning';
import React, { useMemo } from 'react';

import { Layout } from '../../../layouts';
import { usePageData } from '../../../utils';
import { ImageOrVideo } from '../../../components/ImageOrVideo';
import IntroText from '../../../components/IntroText';
import {
  WeatherRoomPlanVisit,
  GridDataItem,
} from '../../../components/WeatherRoom/WeatherRoomPlanVisit';
import { SocialSection } from '../../../components/SocialSection';
import {
  ReviewProps,
  TripAdvisorReviews,
} from '../../../components/TripAdvisorReviews';
import {
  TicketOfferingTicket,
  TicketOfferings,
} from '../../../components/TicketOfferings';
import {
  TicketOffering,
  getIncludedTicketsArray,
} from '../../../utils/get-included-tickets';

import type { IGatsbyImageData } from 'gatsby-plugin-image';
import type { PageConfig } from '../../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Rock Center',
  logo: 'Weather Room',
  cta: {
    to: '/buy-tickets/',
    label: 'Buy tickets',
    sx: {
      bg: 'white',
      fontWeight: 'medium',
      variant: 'buttons.primary',
    },
  },
};

export default function WeatherRoomLP(): React.JSX.Element {
  const { sanityAttractionWeatherRoom } =
    usePageData<Queries.WeatherRoomQuery>();
  const feedName = 'Top of the Rock';
  const useCuratorResult = useCurator({ feedName });

  // HERO IMAGE OR VIDEO LOGIC
  const heroImageOrVideo =
    sanityAttractionWeatherRoom?.heroSection?.heroImageOrVideo;
  let heroImage;
  if (heroImageOrVideo?.image?.asset) {
    heroImage = heroImageOrVideo.image.asset.gatsbyImageData;
  }
  const heroVideo = heroImageOrVideo?.video?.videoFile?.asset?.url ?? '';

  // PLAN YOUR VISIT SECTION DATA
  const gridData = sanityAttractionWeatherRoom?.threeColumnGrid?.grid;

  // TICKET OFFERINGS ARRAY LOGIC
  const includedTicketsArray = getIncludedTicketsArray(
    [
      ...(sanityAttractionWeatherRoom?.ticketOfferings ?? []),
    ] as TicketOffering[],
    sanityAttractionWeatherRoom,
  );

  // SOCIAL SECTION LOGIC
  const cards = useMemo(() => {
    if (useCuratorResult.error) return null;

    return useCuratorResult.data.reduce<SocialCarouselCardProps[]>((acc, d) => {
      if (!d) throw new Error('Expected valid data');
      if (!d.id) {
        console.warn('Expected valid Curator post id');
        return acc;
      }
      if (!d.text) {
        console.warn(`Expected valid caption, Curator post id: ${d.id}`);
        return acc;
      }
      if (!d.image) {
        console.warn(`Expected valid image, Curator post id: ${d.id}`);
        return acc;
      }
      if (!d.source_created_at) {
        console.warn(`Expected valid creation time, Curator post id: ${d.id}`);
        return acc;
      }

      acc.push({
        id: `${d.id}`,
        caption: d.text,
        image: d.image,
        url: d.url,
      });
      return acc;
    }, []);
  }, [useCuratorResult]);

  return (
    <Layout>
      <SecondaryMenuBar
        cta={pageConfig.cta}
        links={[
          { url: '#plan-your-visit', label: 'Plan Your Visit' },
          { url: '#ticket-offerings', label: 'Ticket Offerings' },
        ]}
        sticky
        threshold={0.5}
        title={
          sanityAttractionWeatherRoom?.heroSection?.heroCTA?.title ??
          'The Weather Room'
        }
      />
      {/* IMAGE OR VIDEO */}
      {heroImage && (
        <Box
          sx={{
            paddingTop: [5, 4],
            width: '100%',
          }}
        >
          <Box sx={{ position: 'relative' }}>
            <ImageOrVideo
              height={752}
              image={heroImage}
              loading="eager"
              video={heroVideo}
              width={1500}
            />
            {!heroVideo && (
              <Flex
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '100%',
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background:
                    'linear-gradient(to top, rgba(0, 0, 0, 0.75), rgba(0, 0, 0, 0))',
                }}
              >
                <Text
                  sx={{
                    color: 'white',
                    fontSize: [6, 8],
                    fontWeight: 'bold',
                    textAlign: 'center',
                    variant: 'heading',
                  }}
                >
                  {sanityAttractionWeatherRoom?.heroSection?.heroCTA?.title?.toUpperCase()}
                </Text>
              </Flex>
            )}
          </Box>
        </Box>
      )}
      {/* INTRO TEXT & HOURS */}
      <Section theme="Rock Center">
        <Container
          sx={{
            background: 'white',
            display: 'flex',
            flexDirection: ['column', null, 'row'],
            justifyContent: 'space-between',
            columnGap: [3],
            pt: [3, 4, 6],
            pb: [6, null, 8],
            px: [3, 4, 5],
          }}
        >
          <IntroText
            caption={
              sanityAttractionWeatherRoom?.heroSection?.heroCTA?.bodyCopy
            }
            links={[
              {
                label:
                  sanityAttractionWeatherRoom?.heroSection?.buttonText ??
                  'Learn More',
                url:
                  sanityAttractionWeatherRoom?.heroSection?.menu?.asset?.url ??
                  '',
              },
            ]}
            sx={{ background: 'white', color: 'black' }}
            title={
              sanityAttractionWeatherRoom?.heroSection?.heroCTA?.title ?? ''
            }
          />
          {sanityAttractionWeatherRoom?.hour?.hourText && (
            <HoursAndAddress
              addressHeading="Directions & Contact"
              closed={false}
              contactLinkLabel={
                sanityAttractionWeatherRoom?.location?.link?.caption ?? ''
              }
              contactLinkUrl={
                sanityAttractionWeatherRoom?.location?.link?.url ?? ''
              }
              contactsInfo={[
                {
                  type: 'phone',
                  value: '************',
                },
                {
                  type: 'email',
                  value: '<EMAIL>',
                },
              ]}
              hours={[sanityAttractionWeatherRoom?.hour?.hourText]}
              hoursHeading="Hours"
              location={{
                address1: sanityAttractionWeatherRoom?.location?.address1 ?? '',
                address2: sanityAttractionWeatherRoom?.location?.address2 ?? '',
              }}
            />
          )}
          <Link
            sx={{ display: ['block', 'block', 'none'], mt: 4 }}
            to={
              sanityAttractionWeatherRoom?.heroSection?.menu?.asset?.url ?? ''
            }
            variant="button"
          >
            View Menu
          </Link>
        </Container>
      </Section>
      {/* PLAN YOUR VISIT */}
      {gridData && (
        <WeatherRoomPlanVisit
          gridData={gridData as GridDataItem[]}
          sectionLink={
            sanityAttractionWeatherRoom?.threeColumnGrid?.sectionLink ?? ''
          }
          sectionTitle={
            sanityAttractionWeatherRoom?.threeColumnGrid?.sectionTitle ?? ''
          }
        />
      )}
      {/* GET TICKETS TODAY */}
      <Section
        sx={{ m: 'auto', maxWidth: '1250px', py: 4 }}
        theme="Rock Center"
      >
        <Grid columns={[1, 1, '2fr 1fr']} gap={2} padding={3}>
          <Grid columns={[1, '1fr 2fr']}>
            <Box sx={{ width: ['100%', '232px'] }}>
              {sanityAttractionWeatherRoom?.getTicketsCTA?.photo?.asset
                ?.gatsbyImageData && (
                <IntrinsicImage
                  alt={
                    sanityAttractionWeatherRoom?.getTicketsCTA?.photo?.asset
                      ?.altText ?? ''
                  }
                  image={
                    sanityAttractionWeatherRoom?.getTicketsCTA?.photo.asset
                      .gatsbyImageData as IGatsbyImageData
                  }
                />
              )}
            </Box>
            <Box paddingX={3}>
              <Box marginY={3}>
                <H
                  sx={{
                    variant: 'styles.h3',
                    fontSize: 6,
                    fontFamily: 'headingSecondary',
                  }}
                >
                  {sanityAttractionWeatherRoom?.getTicketsCTA?.title}
                </H>
              </Box>
              <Box>
                <Text sx={{ fontSize: 3 }}>
                  {sanityAttractionWeatherRoom?.getTicketsCTA?.description}
                </Text>
              </Box>
            </Box>
          </Grid>
          <Flex
            paddingY={[4, 0]}
            sx={{
              alignItems: 'center',
              flexDirection: ['column', 'row'],
              justifyContent: 'flex-end',
            }}
          >
            <Link
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                maxHeight: '58px',
                width: ['100%', 'auto'],
              }}
              to={sanityAttractionWeatherRoom?.getTicketsCTA?.button1Link ?? ''}
              variant="button"
            >
              {sanityAttractionWeatherRoom?.getTicketsCTA?.button1Text}
            </Link>
            <Link
              sx={{
                border: ['none', '1px solid #000'],
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: [0, 3],
                maxHeight: '58px',
                width: ['100%', 'auto'],
              }}
              to={sanityAttractionWeatherRoom?.getTicketsCTA?.button2Link ?? ''}
              variant="buttonBorder"
            >
              {sanityAttractionWeatherRoom?.getTicketsCTA?.button2Text}
            </Link>
          </Flex>
        </Grid>
      </Section>
      {/* REVIEWS */}
      <TripAdvisorReviews
        reviews={
          sanityAttractionWeatherRoom?.tripadvisorsReview as ReviewProps[]
        }
        theme="Rock Center Black"
      />
      {/* SOCIAL SECTION */}
      <SocialSection
        cards={cards}
        copy="We'd love to see pics from your visit to the Weather Room Bar & Café. Be sure to share them!"
        hashtag="#topoftherocknyc"
        theme="Top of the Rock"
        title="How was your view from the Weather Room?"
      />
      {/* TICKET OFFERINGS */}
      <TicketOfferings
        id="ticket-offerings"
        includedTicketsArray={includedTicketsArray as TicketOfferingTicket[]}
        link="/buy-tickets"
        title="Ticket Offerings"
      />
    </Layout>
  );
}

export const query = graphql`
  query WeatherRoom {
    meta: sanityAttractionWeatherRoom {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FULL_WIDTH
              placeholder: BLURRED
              formats: [AUTO, WEBP]
            )
          }
        }
      }
    }
    sanityAttractionWeatherRoom {
      id
      hour {
        hourText
      }
      location {
        address1
        address2
        link {
          caption
          url
        }
      }
      heroSection {
        heroCTA {
          title
          bodyCopy
        }
        heroImageOrVideo {
          image {
            asset {
              gatsbyImageData(width: 2000)
            }
          }
          video {
            videoFile {
              asset {
                url
              }
            }
          }
        }
        menu {
          asset {
            url
          }
        }
        buttonText
      }
      includedTickets {
        slugId
        categoryTitle
        order
      }
      threeColumnGrid {
        grid {
          title
          description
          imageOrVideo {
            image {
              asset {
                gatsbyImageData
                altText
              }
            }
          }
        }
        sectionTitle
        sectionLink
      }
      tripadvisorsReview {
        review
        source
      }
      getTicketsCTA {
        button1Link
        button1Text
        button2Link
        button2Text
        description
        photo {
          asset {
            altText
            gatsbyImageData
          }
        }
        title
      }
      ticketOfferings {
        title
        tickets {
          title
          url {
            url
            caption
          }
          image {
            alt
            asset {
              gatsbyImageData(
                layout: CONSTRAINED
                placeholder: BLURRED
                formats: [AUTO, WEBP]
              )
            }
          }
          description {
            children {
              text
            }
          }
          slugId
        }
      }
    }
  }
`;
