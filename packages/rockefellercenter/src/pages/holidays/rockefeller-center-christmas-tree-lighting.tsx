/** @jsxImportSource theme-ui @jsxRuntime classic */
import { SecondaryMenuBar } from '@tishman/components';
import { graphql } from 'gatsby';
import React, { Fragment } from 'react';

import { Layout } from '../../layouts';
import TreeLightingEventHeroBlock from '../../blocks/TreeLightingEventHeroBlock';
import TreeCalloutGridBlock from '../../blocks/TreeCalloutGridBlock';
import TreeHistoryBlock from '../../blocks/TreeHistoryBlock';
import ShareYourExperienceBlock from '../../blocks/ShareYourExperienceBlock';
import TreeLightingEventFeaturedStoriesBlock from '../../blocks/TreeLightingEventFeaturedStoriesBlock';
import TreeLightingEventFaqBlock from '../../blocks/TreeLightingEventFaqBlock';
import { TicketOfferings } from '../../components/TicketOfferings';
import { Oljs } from '../../components/Oljs';

import type { PageConfig } from '../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Rock Center Green',
  pageName: 'The Christmas Tree',
  cta: { to: '/buy-tickets/', label: 'Buy Tickets' },
};

export default function RockefellerCenterChristmasTreeLightingPage({
  data: {
    nav: { subnavLabels },
    sanityEventTreeLighting,
  },
}: {
  data: {
    nav: { subnavLabels: string[] };
    sanityEventTreeLighting: Queries.SanityEventTreeLighting;
  };
}): React.JSX.Element {
  const menuLinks =
    subnavLabels && subnavLabels.length > 0
      ? subnavLabels?.map((nav: string) => ({
          url: `#${nav.toLowerCase().split(' ').join('-')}`,
          label: nav,
        }))
      : [
          { url: '#viewing-details', label: 'Viewing Details' },
          { url: '#holiday-activities', label: 'Holiday Activities' },
          { url: '#tree-history', label: 'Tree History' },
          { url: '#faqs', label: 'FAQs' },
        ];

  const { ticketOfferingsDescription, ticketOfferingsTitle } =
    sanityEventTreeLighting;

  const menuLinkAnchorIds = menuLinks.map((menuLink) =>
    menuLink.url.replace('#', ''),
  );

  // TICKET OFFERINGS ARRAY LOGIC
  const ticketOfferings = sanityEventTreeLighting?.ticketOfferings;
  const includedTicketsArray = ticketOfferings
    ?.reduce((accumulator, offering) => {
      const includedTickets: any = offering?.tickets
        ?.filter(
          (ticket) =>
            sanityEventTreeLighting &&
            sanityEventTreeLighting.includedTickets &&
            sanityEventTreeLighting.includedTickets.some(
              (includedTicket) => includedTicket?.slugId === ticket?.slugId,
            ),
        )
        .map((ticket) => {
          const sanityInclTickets =
            sanityEventTreeLighting && sanityEventTreeLighting.includedTickets;
          const foundedTicket =
            sanityInclTickets &&
            sanityInclTickets.find(
              (includedTicket) =>
                includedTicket && includedTicket.slugId === ticket?.slugId,
            );
          const order = foundedTicket && foundedTicket.order;

          return { ...ticket, order: order || 0 };
        });

      return includedTickets
        ? accumulator.concat(includedTickets)
        : accumulator;
    }, [])
    .sort((a: any, b: any) => a.order - b.order);

  return (
    <Fragment>
      <Oljs />
      <Layout theme="Rock Center Green">
        <SecondaryMenuBar
          cta={pageConfig.cta}
          links={menuLinks}
          sticky
          threshold={0.5}
          title="Explore Tree Lighting"
        />
        <TreeLightingEventHeroBlock id={menuLinkAnchorIds[0]} />
        <TicketOfferings
          description={ticketOfferingsDescription ?? ''}
          id={menuLinkAnchorIds[1]}
          includedTicketsArray={includedTicketsArray ?? []}
          theme="Rock Center Black"
          title={ticketOfferingsTitle ?? ''}
        />
        <TreeHistoryBlock id={menuLinkAnchorIds[2]} />
        <TreeLightingEventFaqBlock
          id={menuLinkAnchorIds[3]}
          theme="Rock Center"
        />
        <TreeCalloutGridBlock theme="Rock Center Black" />
        <TreeLightingEventFeaturedStoriesBlock />
        <ShareYourExperienceBlock theme="Rock Center Cream" />
      </Layout>
    </Fragment>
  );
}

export const query = graphql`
  query RockefellerCenterChristmasTreeLightingMeta {
    nav: sanityEventTreeLighting {
      subnavLabels
    }
    meta: sanityEventTreeLighting {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FIXED
              width: 1200
              formats: [AUTO, WEBP]
              placeholder: BLURRED
            )
          }
        }
      }
    }
    sanityEventTreeLighting {
      ticketOfferingsTitle
      ticketOfferingsDescription
      includedTickets {
        slugId
        categoryTitle
        order
      }
      ticketOfferings {
        title
        tickets {
          title
          url {
            url
            caption
          }
          image {
            alt
            asset {
              gatsbyImageData(
                width: 1600
                layout: CONSTRAINED
                placeholder: BLURRED
              )
            }
          }
          description {
            children {
              text
            }
          }
          slugId
        }
      }
    }
  }
`;
