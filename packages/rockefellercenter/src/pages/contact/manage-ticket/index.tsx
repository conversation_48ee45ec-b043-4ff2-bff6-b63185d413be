/** @jsxImportSource theme-ui @jsxRuntime classic */
import React from 'react';
import invariant from 'invariant';
import { FormProvider, useForm } from 'react-hook-form';
import {
  Date,
  Text,
  RequiredLabel,
  Input,
  Flex,
  Box,
  SubmitButton,
  Form,
  Radio,
  Link,
  Select,
} from '@tishman/components';
import { graphql } from 'gatsby';

import { Layout } from '../../../layouts';
import ContactUsBlock from '../../../blocks/ContactUsBlock';

import type { PageConfig } from '../../../PageConfig';

export const pageConfig: PageConfig = {
  theme: 'Rock Center Black',
  logo: 'Rockefeller Center',
  pageName: 'Contact Us',
  cta: {
    to: '/buy-tickets/',
    label: 'Buy Tickets',
  },
};

export const query = graphql`
  query ManageTicketForm {
    meta: formJson(jsonId: { eq: "manage-ticket-form" }) {
      meta {
        title
        description
      }
    }
    formJson(jsonId: { eq: "manage-ticket-form" }) {
      id
      integrationId
      name
      slug
      description {
        text
      }
      thankYouMessage {
        title
        caption
        links {
          url
          label
        }
      }
    }
    sanityContactPageTemplate(slug: { current: { eq: "manage-ticket" } }) {
      ...SanityContactPageFields
    }
  }
`;

interface ManageTicketFormValues {
  fullname: string;
  email: string;
  phone: string;
  orderNumber: string;
  rescheduleDate: string;
  rescheduleTime: string;
  meridiem: string;
}

const ManageTicketForm = ({
  data,
}: {
  data: Queries.ManageTicketFormQuery & {
    sanityContactPageTemplate: Queries.SanityContactPageTemplate;
  };
}): React.JSX.Element => {
  const { formJson, sanityContactPageTemplate } = data;
  const formMethods = useForm<ManageTicketFormValues>();

  const { formState, register } = formMethods;

  invariant(formJson, 'Manage Ticket Form JSON data is required!');

  const { integrationId, ...contactUsBlockProps } = formJson;

  return (
    <Layout theme="Rock Center Black">
      <ContactUsBlock
        sanityContactPageData={sanityContactPageTemplate}
        showThankYou={formState.isSubmitSuccessful}
        {...contactUsBlockProps}
      >
        <FormProvider {...formMethods}>
          <Form id={integrationId}>
            <Text sx={{ textAlign: 'center', marginBottom: 4 }}>
              You may request a change to your reservation at least 24 hours in
              advance of your scheduled visit. Rescheduling is subject to
              availability, and additional fees may apply if the new time or
              date has a higher price. Tickets that have already been used or
              scanned cannot be rescheduled. Requested date is based on
              availability and not guaranteed. Reservation is not officially
              rescheduled until you receive an email confirmation with your
              updated reservation. All purchases made through our online
              platform are final sale and non-refundable, unless otherwise
              stated.
            </Text>
            <Box sx={{ mb: 5 }}>
              {formState.errors.fullname && (
                <Text variant="formError">
                  {formState.errors.fullname.message}
                </Text>
              )}
              <Input
                placeholder="Enter Full Name"
                text="Full Name"
                {...register('fullname', {
                  required: 'Please enter your full name',
                  validate: {
                    hasFirstAndLastName: (value) => {
                      const nameArray = value.trim().split(/\s+/);
                      return (
                        nameArray.length >= 2 ||
                        'Please enter your first and last name'
                      );
                    },
                  },
                })}
                required
                sx={{
                  'borderColor': formState.errors.fullname && '#EC104E',
                  '::placeholder': {
                    color: '#fff',
                  },
                }}
              />
              {formState.errors.email && (
                <Text variant="formError">
                  {formState.errors.email.message}
                </Text>
              )}
              <Input
                placeholder="Enter Email Address"
                text="Email"
                {...register('email', {
                  required: 'Please enter a valid email address',
                  pattern: {
                    value: /^\S+@\S+\.\S+$/,
                    message: 'Please enter a valid email address',
                  },
                })}
                required
                sx={{
                  'borderColor': formState.errors.email && '#EC104E',
                  '::placeholder': {
                    color: '#fff',
                  },
                }}
              />
              {formState.errors.phone && (
                <Text variant="formError">
                  {formState.errors.phone.message}
                </Text>
              )}
              <Input
                placeholder="************"
                text="Phone Number"
                {...register('phone', {
                  required: 'Please enter your phone number',
                  pattern: {
                    value: /^[(0-9) \-+().]*$/,
                    message: 'Please enter a valid phone number',
                  },
                })}
                required
                sx={{
                  'borderColor': formState.errors.phone && '#EC104E',
                  '::placeholder': {
                    color: '#fff',
                  },
                }}
              />
              {formState.errors.orderNumber && (
                <Text variant="formError">
                  {formState.errors.orderNumber.message}
                </Text>
              )}
              <Input
                placeholder="Enter Order Number"
                text="Order Number"
                {...register('orderNumber', {
                  required: 'Please enter your order number',
                })}
                required
                sx={{
                  'borderColor': formState.errors.orderNumber && '#EC104E',
                  '::placeholder': {
                    color: '#fff',
                  },
                }}
              />
              <Box mt={3}>
                <Text mb={2} mt={2} sx={{ fontWeight: 500 }}>
                  I want to reschedule my ticket
                </Text>
              </Box>
              <Flex sx={{ flexDirection: 'column' }}>
                <Flex sx={{ gap: 3, mb: 3 }}>
                  <Box sx={{ flex: 1 }}>
                    {formState.errors.rescheduleDate && (
                      <Text variant="formError">
                        {formState.errors.rescheduleDate.message}
                      </Text>
                    )}
                    <Date
                      style={{
                        height: '50px',
                      }}
                      text="Reschedule Date"
                      {...register('rescheduleDate', {
                        required: 'Please select a date',
                      })}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    {formState.errors.rescheduleTime && (
                      <Text variant="formError">
                        {formState.errors.rescheduleTime.message}
                      </Text>
                    )}
                    <Flex sx={{ gap: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Input
                          placeholder="H:MM"
                          style={{
                            height: '50px',
                          }}
                          text="Reschedule Time"
                          {...register('rescheduleTime', {
                            required: 'Please enter a time',
                            pattern: {
                              value: /^(0?[1-9]|1[0-2]):[0-5][0-9]$/,
                              message:
                                'Please enter time in H:MM format (e.g., 9:30 or 09:30)',
                            },
                          })}
                        />
                      </Box>
                      <Box sx={{ width: '80px' }}>
                        <Select
                          style={{
                            height: '50px',
                          }}
                          text=" "
                          {...register('meridiem')}
                        >
                          <option value="AM">AM</option>
                          <option value="PM">PM</option>
                        </Select>
                      </Box>
                    </Flex>
                  </Box>
                </Flex>
              </Flex>
            </Box>
            <Flex
              sx={{
                justifyContent: 'space-between',
                flexDirection: ['column', null, 'row'],
              }}
            >
              <Box sx={{ mb: [4, null, 0, null] }}>
                <RequiredLabel labelText="Required*" />
                <Box mt={2}>
                  <Link
                    href="/documents/TOR_TermsAndConditions.pdf"
                    sx={{
                      textDecoration: 'underline',
                      fontSize: 'inherit',
                      fontWeight: '500',
                    }}
                    target="_blank"
                  >
                    Terms & Conditions
                  </Link>
                </Box>
              </Box>
              <SubmitButton
                disabled={formState.isSubmitting}
                text={formState.isSubmitting ? 'Submitting...' : 'Submit'}
              />
            </Flex>
          </Form>
        </FormProvider>
      </ContactUsBlock>
    </Layout>
  );
};

export default ManageTicketForm;
