/** @jsxImportSource theme-ui @jsxRuntime classic */
import React from 'react';
import { graphql } from 'gatsby';

import { SanityVenuePage } from '../../../components/SanityVenuePage';

import type { PageConfig } from '../../../PageConfig';

type TSanityVenueDetailPageTemplate = {
  data: {
    sanityVenueDetailPageTemplate: Queries.SanityVenueDetailPageTemplate;
  };
};

export const pageConfig: PageConfig = {
  theme: 'Rock Center Black',
  pageName: 'Private Events',
  cta: { to: '/contact/', label: 'Inquire' },
};

const SanityVenueDetailPageTemplate = ({
  data,
}: TSanityVenueDetailPageTemplate) => {
  return (
    <SanityVenuePage
      data={data.sanityVenueDetailPageTemplate}
      pageConfig={pageConfig}
    />
  );
};

export const query = graphql`
  query SanityVenueDetailPageTemplate($slug__current: String!) {
    meta: sanityVenueDetailPageTemplate(
      venue: { slug: { current: { eq: $slug__current } } }
    ) {
      seo {
        title: metaTitle
        description: metaDescription
        canonical: metaCanonicalUrl
        ogImage: metaImage {
          asset {
            gatsbyImageData(
              layout: FIXED
              width: 1200
              formats: [AUTO, WEBP]
              placeholder: BLURRED
            )
          }
        }
      }
    }
    sanityVenueDetailPageTemplate(
      venue: { slug: { current: { eq: $slug__current } } }
    ) {
      seo {
        metaTitle
      }
      venue {
        slug {
          current
        }
      }
      heroFeatureFlag
      venueDetailsFeatureFlag
      heroImage {
        alt
        asset {
          gatsbyImageData(layout: FULL_WIDTH)
        }
      }
      heroTextSection {
        eyebrow
        title
        paragraph
        ctaButton {
          ctaText
          ctaLink
        }
      }
      capacities {
        _key
        _type
        style
        children {
          _key
          _type
          text
          marks
        }
      }
      capacityPDFs {
        ... on SanityPdfDownload {
          _type
          pdfButtonText
          pdfFile {
            asset {
              url
            }
          }
        }

        ... on SanityUrlType {
          _type
          url
          caption
        }
      }
      catering {
        _key
        _type
        style
        children {
          _key
          _type
          text
          marks
        }
      }
      cateringPDFs {
        ... on SanityPdfDownload {
          _type
          pdfButtonText
          pdfFile {
            asset {
              url
            }
          }
        }

        ... on SanityUrlType {
          _type
          url
          caption
        }
      }
      additionalSpaces {
        _key
        _type
        style
        children {
          _key
          _type
          text
          marks
        }
      }
      subNavigationFeatureFlag
      subnavLinks {
        caption
        url
      }
      galleryFeatureFlag
      gallery {
        items {
          __typename
          ... on SanityImageType {
            asset {
              gatsbyImageData(
                layout: FULL_WIDTH
                placeholder: BLURRED
                formats: [AUTO, WEBP]
              )
              metadata {
                dimensions {
                  width
                  height
                }
              }
            }
            alt
            caption
          }
          ... on SanityAmbientVideoType {
            videoFile {
              asset {
                url
              }
            }
          }
        }
        title
      }
      faqsFeatureFlag
      faqs {
        category
        faqs {
          question
          answer {
            _key
            _type
            style
            children {
              _key
              _type
              text
              marks
            }
          }
        }
      }
      newsFeatureFlag
      news {
        id
        category
        titleAndSlug {
          title
          slug {
            current
          }
        }
        publishAt
        poster {
          alt
          asset {
            gatsbyImageData(layout: CONSTRAINED)
          }
          caption
        }
        authors
      }
      newsColorSchema
      socialMediaFeatureFlag
      feedName
      imageCalloutFeatureFlag
      imageCallout {
        title
        description
        image {
          align
          alt
          asset {
            gatsbyImageData(layout: CONSTRAINED)
          }
          caption
        }
        link {
          url
          caption
        }
      }
    }
  }
`;

export default SanityVenueDetailPageTemplate;
