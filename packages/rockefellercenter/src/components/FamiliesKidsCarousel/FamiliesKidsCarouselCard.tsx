/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Article, H } from '@hzdg/sectioning';
import BlockContent from '@sanity/block-content-to-react';
import {
  Box,
  Button,
  Flex,
  IntrinsicImage,
  Link,
  Text,
} from '@tishman/components';
import React, { Fragment } from 'react';
import { StaticImage } from 'gatsby-plugin-image';

import type { IGatsbyImageData } from 'gatsby-plugin-image';
import type { Block } from '@sanity/block-content-to-react';

type FamiliesKidsItem = {
  readonly type: 'attraction' | 'venue' | 'sweet' | 'dine' | 'shop' | 'event';
  readonly _id: string;
  readonly category: {
    readonly category: string;
    readonly subCategory: Array<string>;
  };
  readonly titleAndSlug: {
    readonly title: string;
    readonly slug: {
      readonly current: string;
    };
  };
  readonly startEndDateTime?: {
    readonly formattedStartDateTime: string;
    readonly formattedEndDateTime: string;
  };
  readonly excerpt?: string;
  readonly poster?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
  readonly kidsExcerpt?: string;
  readonly kidsPoster?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
  readonly photo?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
};

type FamiliesAndKidsAttractionsItem = {
  readonly type: 'attraction' | 'venue' | 'sweet' | 'dine' | 'shop' | 'event';
  readonly _id?: string;
  readonly category?: {
    readonly category?: string;
    readonly subCategory?: Array<string>;
  };
  readonly titleAndSlug?: {
    readonly title?: string;
    readonly slug?: {
      readonly current?: string;
    };
  };
  readonly excerpt?: string;
  readonly poster?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
  readonly kidsExcerpt?: string;
  readonly kidsPoster?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
  readonly image?: {
    readonly alt?: string | undefined;
    readonly asset?:
      | {
          readonly gatsbyImageData: IGatsbyImageData;
        }
      | undefined;
  };
  readonly heroCTA?: {
    readonly title?: string;
    readonly bodyCopy?: string | Block[];
  };
  readonly gallery: {
    readonly title?: string;
    readonly items: ReadonlyArray<
      | {
          readonly _key?: string;
          readonly _type?: string;
          readonly alt?: string;
          readonly asset?: {
            readonly gatsbyImageData: IGatsbyImageData;
          };
        }
      | Record<string, never>
    >;
  };
};

type FamiliesAndKidsVenue = {
  readonly type: 'attraction' | 'venue' | 'sweet' | 'dine' | 'shop' | 'event';
  readonly id: string;
  readonly title?: string;
  readonly kidsExcerpt?: string;
  readonly kidsPoster?: {
    readonly alt?: string;
    readonly asset?: {
      readonly gatsbyImageData: IGatsbyImageData;
    };
  };
  readonly link?: {
    readonly url?: string;
    readonly caption?: string;
  };
  readonly imageGallery?:
    | {
        readonly title?: string | undefined;
        readonly images?: ReadonlyArray<
          | {
              readonly caption?: string | undefined;
              readonly alt?: string | undefined;
              readonly asset?:
                | {
                    readonly gatsbyImageData: IGatsbyImageData;
                  }
                | undefined;
            }
          | undefined
        >;
      }
    | undefined;
};

type HardCodedAttraction = {
  title: string;
  type: 'attraction' | 'venue' | 'sweet' | 'dine' | 'shop' | 'event';
  image:
    | {
        alt?: string;
        asset?: {
          gatsbyImageData: IGatsbyImageData;
        };
      }
    | undefined;
  excerpt: string;
  kidsPoster?:
    | {
        alt?: string;
        asset?: {
          gatsbyImageData: IGatsbyImageData;
        };
      }
    | undefined;
  kidsExcerpt?: string;
  url?: string;
};

type CardItem = Partial<
  FamiliesKidsItem &
    FamiliesAndKidsAttractionsItem &
    FamiliesAndKidsVenue &
    HardCodedAttraction
> & {
  hardcoded?: boolean; // Added this line
};

export interface FamiliesKidsCardProps {
  /**
   * MaxWidth of card
   */
  maxWidth?: Array<string | number> | number | string;
  /**
   * Ratio of card
   */
  ratio?: Array<number> | number;
  /** The ticket. */
  item: CardItem;
}

const linkStyles = {
  color: 'black',
  backgroundColor: 'white',
  border: '1px solid black',
  fontWeight: 'regular',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  py: 3,
  px: 2,
};

const eventTitleStyles = {
  display: '-webkit-box',
  WebkitLineClamp: '2',
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  lineHeight: '1.4',
  paddingBottom: '2px',
};

const ticketDescriptionStyles = {
  display: '-webkit-box',
  WebkitLineClamp: '5',
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  lineHeight: '1.5',
  paddingBottom: '2px',
};

const dateStyles = {
  ...ticketDescriptionStyles,
  fontWeight: 'regular',
  my: 0,
};

const BaseCard = ({ item }: FamiliesKidsCardProps): JSX.Element => {
  return (
    <Fragment>
      <div>
        <Box
          as="div"
          sx={{
            position: 'relative',
            height: ['240px', null, null, '290px'],
            overflow: 'hidden',
          }}
        >
          <IntrinsicImage
            alt={item?.photo?.alt}
            image={
              (item.kidsPoster?.asset?.gatsbyImageData ??
                item?.poster?.asset?.gatsbyImageData) as IGatsbyImageData
            }
          />
        </Box>
      </div>
      {item.titleAndSlug && (
        <Flex
          sx={{
            flexDirection: 'column',
            height: '100%',
            flex: 1,
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <H sx={{ variant: 'text.ticketTitleBase', my: 0 }}>
              {item.titleAndSlug.title}
            </H>
            <Text sx={ticketDescriptionStyles} variant="ticketDescriptionBase">
              {item.excerpt && typeof item.excerpt === 'object' ? (
                <BlockContent blocks={item.excerpt as Block | Block[]} />
              ) : item.kidsExcerpt ? (
                item.kidsExcerpt
              ) : (
                item.excerpt
              )}
            </Text>
          </Box>
          <Link
            data-ticket-carousel-card={item.titleAndSlug.title}
            href={item.titleAndSlug.slug.current}
            sx={linkStyles}
            variant="button"
          >
            <Text as="span" variant="ticketButtonLabel">
              Learn More
            </Text>
          </Link>
        </Flex>
      )}
    </Fragment>
  );
};

const EventCard = ({ item }: FamiliesKidsCardProps): JSX.Element => {
  return (
    <Fragment>
      <div>
        <Box
          as="div"
          sx={{
            position: 'relative',
            height: ['240px', null, null, '290px'],
            overflow: 'hidden',
          }}
        >
          <IntrinsicImage
            alt={item?.photo?.alt}
            image={
              (item.kidsPoster?.asset?.gatsbyImageData ??
                item?.photo?.asset?.gatsbyImageData) as IGatsbyImageData
            }
          />
        </Box>
      </div>
      <Flex
        sx={{
          flexDirection: 'column',
          height: '100%',
          flex: 1,
        }}
      >
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Text sx={dateStyles} variant="ticketDescriptionBase">
            {item?.startEndDateTime?.formattedStartDateTime}
          </Text>
          <H
            sx={{ variant: 'text.ticketTitleBase', my: 0, ...eventTitleStyles }}
          >
            {item?.titleAndSlug?.title}
          </H>
          <Text sx={ticketDescriptionStyles} variant="ticketDescriptionBase">
            {item.excerpt && typeof item.excerpt === 'object' ? (
              <BlockContent blocks={item.excerpt as Block | Block[]} />
            ) : item.kidsExcerpt ? (
              item.kidsExcerpt
            ) : (
              item.excerpt
            )}
          </Text>
        </Box>
        <Link
          data-ticket-carousel-card={item?.titleAndSlug?.title}
          href={item?.titleAndSlug?.slug.current as string}
          sx={linkStyles}
          variant="button"
        >
          <Text as="span" variant="ticketButtonLabel">
            Learn More
          </Text>
        </Link>
      </Flex>
    </Fragment>
  );
};

const AttractionCard = ({ item }: FamiliesKidsCardProps): JSX.Element => {
  return (
    <Fragment>
      <div>
        <Box
          as="div"
          sx={{
            position: 'relative',
            height: ['240px', null, null, '290px'],
            overflow: 'hidden',
          }}
        >
          {/* This hardcoded options is only used for Bloom so remove this if you remove the card */}
          {item.hardcoded ? (
            <StaticImage
              alt="alt text"
              src="../../images/Marissa_Alper_Bloom_Family_Day_65.jpg"
            />
          ) : (
            <IntrinsicImage
              alt={item?.image?.alt}
              image={
                (item?.kidsPoster?.asset?.gatsbyImageData ??
                  item?.gallery?.items[0]?.asset
                    ?.gatsbyImageData) as IGatsbyImageData
              }
            />
          )}
        </Box>
      </div>
      <Flex
        sx={{
          flexDirection: 'column',
          height: '100%',
          flex: 1,
        }}
      >
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <H sx={{ variant: 'text.ticketTitleBase', my: 0 }}>
            {item?.gallery?.title}
          </H>
          <Text sx={ticketDescriptionStyles} variant="ticketDescriptionBase">
            {typeof item?.heroCTA?.bodyCopy === 'object' ? (
              <BlockContent
                blocks={item?.heroCTA?.bodyCopy as Block | Block[]}
              />
            ) : (
              item?.kidsExcerpt ?? item?.heroCTA?.bodyCopy ?? item?.excerpt
            )}
          </Text>
        </Box>
        {item.url ? (
          <Link
            data-ticket-carousel-card={item.title}
            href={item.url as string}
            sx={linkStyles}
            variant="button"
          >
            <Text as="span" variant="ticketButtonLabel">
              {item.url === '/attractions/the-rink-at-rockefeller-center/'
                ? 'Learn More'
                : 'Buy Tickets'}
            </Text>
          </Link>
        ) : (
          <Button disabled py="18px">
            Coming soon
          </Button>
        )}
      </Flex>
    </Fragment>
  );
};

const VenueCard = ({ item }: FamiliesKidsCardProps): JSX.Element => {
  return (
    <Fragment>
      <div>
        <Box
          as="div"
          sx={{
            position: 'relative',
            height: ['240px', null, null, '290px'],
            overflow: 'hidden',
          }}
        >
          <IntrinsicImage
            alt={item?.image?.alt}
            image={
              (item?.kidsPoster?.asset?.gatsbyImageData ??
                item?.gallery?.items[0]?.asset?.gatsbyImageData ??
                item?.imageGallery?.images![0]?.asset?.gatsbyImageData ??
                item?.poster?.asset?.gatsbyImageData) as IGatsbyImageData
            }
          />
        </Box>
      </div>
      <Flex
        sx={{
          flexDirection: 'column',
          height: '100%',
          flex: 1,
        }}
      >
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <H sx={{ variant: 'text.ticketTitleBase', my: 0 }}>
            {item?.title ?? item?.gallery?.title ?? item?.titleAndSlug?.title}
          </H>
          <Text sx={ticketDescriptionStyles} variant="ticketDescriptionBase">
            {typeof item?.heroCTA?.bodyCopy === 'object' ? (
              <BlockContent
                blocks={item?.heroCTA?.bodyCopy as Block | Block[]}
              />
            ) : (
              item?.kidsExcerpt ?? item?.heroCTA?.bodyCopy ?? item?.excerpt
            )}
          </Text>
        </Box>
        {item.url ? (
          <Link
            data-ticket-carousel-card={item.title}
            href={item.url as string}
            sx={linkStyles}
            variant="button"
          >
            <Text as="span" variant="ticketButtonLabel">
              {item.url === '/attractions/the-rink-at-rockefeller-center/'
                ? 'Learn More'
                : 'Buy Tickets'}
            </Text>
          </Link>
        ) : (
          <Button disabled py="18px">
            Coming soon
          </Button>
        )}
      </Flex>
    </Fragment>
  );
};

const renderComponentBasedOnType = (item: CardItem): JSX.Element => {
  switch (item.type) {
    case 'sweet':
    case 'shop':
    case 'dine':
      return <BaseCard item={item} />;
    case 'attraction':
      return <AttractionCard item={item} />;
    case 'venue':
      return <VenueCard item={item} />;
    case 'event':
      return <EventCard item={item} />;
    default:
      return <Fragment></Fragment>;
  }
};

export function FamiliesKidsCarouselCard({
  item,
}: FamiliesKidsCardProps): JSX.Element {
  return (
    <Flex
      as={Article}
      sx={{
        border: '1px solid',
        borderColor: 'text',
        flexDirection: 'column',
        gap: 3,
        height: '100%',
        maxWidth: ['272px', '323px'],
        minHeight: ['485px', null, '580px'],
        minWidth: '257px',
        padding: 3,
        display: 'flex',
      }}
    >
      {renderComponentBasedOnType(item)}
    </Flex>
  );
}
