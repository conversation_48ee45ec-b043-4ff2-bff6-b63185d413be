/** @jsxImportSource theme-ui @jsxRuntime classic */
import { H } from '@hzdg/sectioning';
import {
  Flex,
  Container,
  CarouselV2ContextProvider,
  CarouselV2,
  CarouselNavigationV2,
  useThemeUIValue,
  useResponsiveValue,
} from '@tishman/components';
import React from 'react';

import {
  FamiliesKidsCarouselCard,
  type FamiliesKidsCardProps,
} from './FamiliesKidsCarouselCard';

export interface FamiliesKidsCarouselProps {
  items: ReadonlyArray<Readonly<FamiliesKidsCardProps['item']>>;
  heading?: string;
  subheading?: string;
}

interface ExtendedFamiliesKidsCarousel extends FamiliesKidsCarouselProps {
  cta?: {
    label?: string;
    href?: string;
  };
}

export function FamiliesKidsCarousel(
  props: ExtendedFamiliesKidsCarousel,
): React.JSX.Element {
  const { heading, subheading, items } = props;

  const slideSpaceBetween = useThemeUIValue([3, 4], 'space') ?? 32;
  const slideMaxWidth = useResponsiveValue(['272px', '323px']);

  return (
    <Flex
      data-module="FamiliesKidsCarousel"
      sx={{
        flexDirection: 'column',
        position: 'relative',
        pt: [4, 5],
        pb: [3, 5],
      }}
    >
      <Container mb={[3, null, 5]} px={[3, null, 4]}>
        <Flex
          sx={{
            flexDirection: 'column',
            rowGap: 2,
          }}
        >
          <H
            sx={{
              variant: 'text.ticketsHeading',
            }}
          >
            {heading}
          </H>
          <H
            sx={{
              variant: 'text.smallTitle',
              fontSize: [4, '26px'],
              fontWeight: 'regular',
            }}
          >
            {subheading}
          </H>
        </Flex>
      </Container>
      <CarouselV2ContextProvider>
        <CarouselV2
          name={heading}
          slideStyle={{
            minWidth: '257px',
            maxWidth: slideMaxWidth,
            height: 'auto',

            // used before hydration
            marginRight: slideSpaceBetween,
            // `spaceBetween` starts getting used after hydration
          }}
          swiperOptions={{
            spaceBetween: slideSpaceBetween,
            style: {
              paddingLeft: slideSpaceBetween,
              paddingRight: slideSpaceBetween,
              maxWidth: '1200px',
            },
          }}
        >
          {items.map((item, index) => (
            <FamiliesKidsCarouselCard item={item} key={item._id || index} />
          ))}
        </CarouselV2>
        <Container
          sx={{
            alignSelf: 'center',
            display: 'flex',
            justifyContent: 'flex-end',
            mt: [4, 0],
            mb: [3, 0],
            position: ['static', 'absolute'],
          }}
        >
          <CarouselNavigationV2 />
        </Container>
      </CarouselV2ContextProvider>
    </Flex>
  );
}
