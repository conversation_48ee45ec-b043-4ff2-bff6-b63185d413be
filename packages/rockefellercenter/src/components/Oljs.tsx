/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Helmet } from 'react-helmet-async';
import React from 'react';

import { getLocaleLanguage, Locale, usePageContext } from '../utils';

export const Oljs = () => {
  const { locale } = usePageContext<{ locale: Locale }>();

  return (
    <Helmet htmlAttributes={{ lang: getLocaleLanguage(locale) }}>
      <script
        data-oljs="P3643-808E-77B4-49CA"
        referrerPolicy="no-referrer-when-downgrade"
        src="https://www.onelink-edge.com/moxie.min.js"
        type="text/javascript"
      ></script>
    </Helmet>
  );
};
