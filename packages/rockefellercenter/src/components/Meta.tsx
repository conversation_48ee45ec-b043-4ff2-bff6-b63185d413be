/** @jsxImportSource theme-ui @jsxRuntime classic */
import React, { memo } from 'react';
import { Helmet } from 'react-helmet-async';
import { useStaticQuery, graphql } from 'gatsby';

export interface IMeta {
  name: string;
  content: string;
}

export interface MetaProps {
  title?: string;
  seoTitle?: string;
  description?: string;
  ogImage?: string;
  lang?: string;
  meta?: IMeta[];
  keywords?: string[];
  source?: string;
  src?: string;
  canonical?: string;
  noIndex?: boolean;
  pagePath?: string;
}

const DEFAULT_META_QUERY = graphql`
  query DefaultMeta {
    site {
      siteMetadata {
        title
        description
        author
        siteUrl
      }
    }
  }
`;

export const Meta = memo(function Meta({
  title,
  seoTitle,
  description,
  ogImage = '',
  lang = 'en',
  meta = [],
  keywords = [],
  canonical,
  noIndex,
  pagePath,
}: MetaProps): JSX.Element {
  const data = useStaticQuery<Queries.DefaultMetaQuery>(DEFAULT_META_QUERY);
  const metaDescription =
    description ?? data.site?.siteMetadata?.description ?? '';
  const metaTitle = seoTitle ?? title ?? data.site?.siteMetadata?.title ?? '';

  const siteURL = process.env.SITE_URL ?? data.site?.siteMetadata?.siteUrl;

  const siteURLWithoutTrailingSlash = (
    siteURL?.endsWith('/') ? siteURL : `${siteURL}/`
  ).replace(/\/$/, '');

  const defaultCanonicalUrl = `${siteURLWithoutTrailingSlash}${pagePath}`;

  const metaCanonicalUrl = canonical ?? defaultCanonicalUrl;

  return (
    <Helmet
      htmlAttributes={{
        lang,
      }}
      link={[
        {
          rel: 'canonical',
          href: metaCanonicalUrl,
        },
      ]}
      meta={
        [
          {
            name: 'description',
            content: metaDescription,
          },
          {
            name: 'facebook-domain-verification',
            content: 'sugsazxtld8p01yaczkgrrvulf2mls',
          },
          {
            property: 'og:title',
            content: title,
          },
          {
            property: 'og:description',
            content: metaDescription,
          },
          {
            property: 'og:type',
            content: 'website',
          },
          {
            name: 'twitter:card',
            content: 'summary',
          },
          {
            name: 'twitter:creator',
            content: data.site?.siteMetadata?.author,
          },
          {
            name: 'twitter:title',
            content: title,
          },
          {
            name: 'twitter:description',
            content: metaDescription,
          },
          {
            property: 'og:image',
            content: ogImage,
          },
          {
            property: 'twitter:image',
            content: ogImage,
          },
          {
            property: 'robots',
            content: noIndex ? 'noindex' : 'max-image-preview:large',
          },
        ]
          .concat(
            keywords.length > 0
              ? {
                  name: 'keywords',
                  content: keywords.join(', '),
                }
              : [],
          )
          .concat(meta) as IMeta[]
      }
      title={metaTitle}
    />
  );
});
