import React, { useMemo } from 'react';
import { Button, Flex, Text, TishmanCSSObject } from '@tishman/components';
import { Image, ThemeUIStyleObject } from 'theme-ui';
import { navigate } from 'gatsby';

import { usePageData } from '../../utils';
import {
  flowToImageIndex,
  flowToSanityTicketMap,
} from '../../blocks/Cart/utils';
import { useLocalizedLink } from '../../utils/use-localized-link';
import { actions, useAppDispatch } from '../../store';
import { Flow, FlowEvent } from '../../services/viva/constants';
import theme from '../../../../components/src/themes/base';
import { type AddOnItem, safeDataLayerPush } from '../BuyTickets';
import chaletImage from '../../images/ticket-images/chalets.jpg';
import { newRelic } from '../../utils/new-relic';
import {
  getItemId,
  getItemName,
  getItemQuantity,
} from '../../utils/type-guards';

import type { CartContentTranslationsForFlow } from '../../data/translations/cartContent';
import type { ShopCartSelection } from '../../store/types';

type Props = {
  children?: React.ReactNode;
  translations?: CartContentTranslationsForFlow;
  flow?: Flow;
  sx?: ThemeUIStyleObject;
  dateTime?: number;
  selections: (ShopCartSelection | AddOnItem)[];
};

const buttonStyles: TishmanCSSObject<'buttons'> = {
  fontFamily: theme.fonts.button,
  fontWeight: 500,
  fontSize: 2,
  alignSelf: 'flex-start',
  backgroundColor: 'transparent',
  letterSpacing: 'unset',
  mt: '4px',
  p: 0,
  textTransform: 'capitalize',
};

export const TicketGroup = ({
  children,
  dateTime,
  translations,
  flow,
  selections,
  sx,
}: Props) => {
  const { sanityContentForCart } = usePageData<Queries.CartQuery>();
  const getLocalizedLink = useLocalizedLink();
  const dispatch = useAppDispatch();

  const image = useMemo(() => {
    if (!flow || !sanityContentForCart?.nodes) return null;

    if (flow === Flow.CHALETS) {
      return chaletImage;
    }

    const buyTicketsKey = flowToSanityTicketMap[flow];
    const attraction = sanityContentForCart.nodes.find(
      (i) => i.slug?.current === buyTicketsKey,
    );

    return attraction?.tickets?.[flowToImageIndex[flow] ?? 0]?.image;
  }, [flow, sanityContentForCart]);

  if (!translations) return null;
  // Temporarily remove cart editing from cart
  // const proceedToEdit = () => {
  //   if (!flow) return;
  //   newRelic.logCartAction(`Cart - Edit clicked in Cart`, 'Edit');
  //   navigate(
  //     `${getLocalizedLink(
  //       `/buy-tickets/${FlowEvent[flow]}`,
  //     )}?edit=true&flow=${flow}&date=${dateTime}`,
  //   );
  // };

  const isChalets = flow && [Flow.CHALETS].includes(flow) && image;

  const imageSrc = isChalets
    ? image
    : image?.asset?.gatsbyImageData.images.fallback?.src;

  return (
    <Flex
      sx={{
        ...sx,
        flexDirection: ['column', 'column', 'row'],
        alignItems: ['stretch', 'stretch', 'flex-start'],
        rowGap: [2],
        columnGap: '24px',
      }}
    >
      {(!!image?.asset || isChalets) && (
        <Image
          alt={image.alt ?? 'ticket group image'}
          src={imageSrc}
          sx={{
            aspectRatio: 'auto 661 / 443',
            maxWidth: ['none', 'none', 262],
          }}
        />
      )}
      <Flex
        sx={{
          flexDirection: 'column',
          flex: 1,
        }}
      >
        <Text
          sx={{
            fontSize: ['18px'],
            fontWeight: 500,
            mb: 3,
          }}
        >
          {translations.title}
        </Text>
        <Flex
          sx={{
            flexDirection: 'column',
          }}
        >
          <Flex
            sx={{
              flexDirection: 'column',
              rowGap: ['8px'],
            }}
          >
            {children}
            {flow && (
              <Flex
                sx={{
                  columnGap: ['10px'],
                  mt: 24,
                }}
              >
                {/* Temporarily remove cart editing from cart
                <Button
                  data-testid="edit-button-cart-ticket-group"
                  onClick={proceedToEdit}
                  sx={buttonStyles}
                  variant="underline"
                >
                  edit
                </Button> */}
                <Button
                  onClick={() => {
                    if (flow && dateTime) {
                      dispatch(
                        actions.checkout.clearDateFromCheckout({
                          flow,
                          dateTime,
                        }),
                      );

                      const removedItems = selections.map((selection) => ({
                        item_id: getItemId(selection),
                        item_name: getItemName(selection),
                        item_category: 'Tickets',
                        price: (
                          ('price' in selection ? selection.price : 0) / 100
                        ).toFixed(2),
                        quantity: getItemQuantity(selection),
                        discount:
                          'discount' in selection && selection.discount
                            ? selection.discount
                            : '0.00',

                        item_flow: flow,
                      }));

                      newRelic.logCartAction(
                        `TicketGroup - Remove clicked in Cart`,
                        'Remove',
                        {
                          removedItems,
                        },
                      );

                      safeDataLayerPush({
                        event: 'remove_from_cart',
                        remove_items: removedItems,
                      });
                    }
                  }}
                  sx={buttonStyles}
                  variant="underline"
                >
                  remove
                </Button>
              </Flex>
            )}
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};
