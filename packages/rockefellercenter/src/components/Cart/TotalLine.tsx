import React from 'react';
import { Flex, Text } from '@tishman/components';
import { ThemeUIStyleObject } from 'theme-ui';

import { formatMoney } from '../../utils';

import type { CheckoutTotals } from '../../store/types';

type Props = {
  totals: CheckoutTotals;
  sx?: ThemeUIStyleObject;
};

export const CartTotalLine = ({ totals, sx }: Props) => {
  return (
    <Flex
      sx={{
        px: [24],
        flexDirection: 'column',
        ...sx,
      }}
    >
      <Flex
        sx={{
          justifyContent: ['space-between', 'space-between', 'flex-end'],
          borderTop: '1px solid rgba(0,0,0,0.1)',
          py: [24],
          columnGap: '9px',
        }}
      >
        <Text
          sx={{
            fontWeight: [500, 500, 300],
          }}
        >
          Total
        </Text>
        {totals.total ? (
          <Text
            data-testid="total-line-amount"
            sx={{
              fontWeight: 500,
            }}
          >
            {totals.subtotal
              ? formatMoney(totals.subtotal, undefined, true)
              : formatMoney(totals.total, undefined, true)}
          </Text>
        ) : null}
      </Flex>
    </Flex>
  );
};
