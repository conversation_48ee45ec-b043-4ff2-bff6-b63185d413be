import React, { Fragment, useMemo } from 'react';
import { Flex, Text } from '@tishman/components';
import { Close } from '@tishman/icons';
import { Box } from 'theme-ui';

import { formatMoney } from '../../utils';
import {
  actions,
  useAppDispatch,
  useAppSelector,
  selectCheckoutCart,
} from '../../store';
import { Flow, StatGroup } from '../../services/viva/constants';
import {
  parseTimeslotHours,
  parseTimeslotTime,
} from '../../utils/parse-timeslot-hours';
import {
  formatTimeRangeDisplay,
  safeDataLayerPush,
  type AddOnItem,
} from '../BuyTickets';
import { parseAndFormatDate } from '../../services/date';
import { newRelic } from '../../utils/new-relic';
import {
  getItemId,
  getItemName,
  getItemQuantity,
} from '../../utils/type-guards';

import type { ShopCartSelection } from '../../store/types';
import { putInArray } from '../../services/viva/utils';

type Props = {
  selection: ShopCartSelection | AddOnItem;
  title?: string;
  flow: Flow;
  isAddOn?: boolean;
  dateTime?: number;
};

const FLOWS_WITH_NO_TIME = [Flow.TOR_EXPRESS];

export const Ticket = ({
  selection,
  title,
  flow,
  dateTime,
  isAddOn = false,
}: Props) => {
  const dispatch = useAppDispatch();
  const checkout = useAppSelector(selectCheckoutCart);
  const qty = getItemQuantity(selection);

  const selectionDate =
    'timeSlot' in selection && selection.timeSlot
      ? parseTimeslotTime(selection.timeSlot)
      : [];

  const hours = useMemo(() => {
    if ('timeSlot' in selection && selection.timeSlot) {
      return parseTimeslotHours(selection.timeSlot);
    }

    return [];
  }, [selection]);

  const remove = (_selection: typeof selection) => {
    if (isAddOn && dateTime) {
      dispatch(
        actions.checkout.removeAddOnItemFromCheckoutByFlowAndDateTime({
          flow,
          dateTime,
          statGroup: (Array.isArray(_selection.statGroup)
            ? _selection.statGroup[0]
            : _selection.statGroup) as StatGroup,
        }),
      );
    } else if (!isAddOn && 'dateTime' in _selection && _selection.dateTime) {
      dispatch(
        actions.checkout.removeItemFromCheckoutCartByTypeAndDateTime({
          flow,
          dateTime: _selection.dateTime,
          type: _selection.type,
        }),
      );
    }

    const removedItems = [
      {
        item_id: getItemId(_selection),
        item_name: getItemName(_selection),
        item_category: flow,
        price: (('price' in _selection ? _selection.price : 0) / 100).toFixed(
          2,
        ),
        quantity: getItemQuantity(_selection),
        discount:
          'discount' in _selection && _selection.discount
            ? _selection.discount
            : '0.00',
      },
    ];

    newRelic.logCartAction(`Ticket - Remove clicked in Cart`, 'Remove', {
      removedItems,
    });

    safeDataLayerPush({
      event: 'remove_from_cart',
      currency: 'USD',
      value: (('price' in _selection ? _selection.price : 0) / 100).toFixed(2),
      items: removedItems,
    });
  };

  return (
    <Flex
      sx={{
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#fafafa',
      }}
    >
      <Flex
        sx={{
          flexDirection: 'column',
          marginLeft: ['12px', 3],
          marginRight: '12px',
          py: ['8px', 3],
          flex: 1,
        }}
      >
        {/* TYPE & QUANTITY */}
        <Flex
          sx={{
            justifyContent: 'space-between',
            columnGap: 42,
          }}
        >
          {title && (
            <Flex
              sx={{
                justifyContent: 'space-between',
                flex: 1,
              }}
            >
              <Text sx={{ fontWeight: 500 }}>{title}</Text>
              {selectionDate.length === 0 && (
                <Flex>
                  <Text sx={{ mx: '12px' }}>Qty. {qty}</Text>
                  <Text>{formatMoney(selection.price, undefined, true)}</Text>
                </Flex>
              )}
            </Flex>
          )}
        </Flex>
        {/* DATE & TIME */}
        <Flex
          sx={{
            columnGap: '24px',
          }}
        >
          <Flex
            sx={{
              flexDirection: 'column',
              flex: [1, 1, '0 1 calc(100% - 40px)'],
            }}
          >
            <Flex
              sx={{
                columnGap: '24px',
              }}
            >
              <Flex
                sx={{
                  flexDirection: 'column',
                  flex: 1,
                }}
              >
                {selectionDate.map((date, index) => (
                  <Fragment key={index}>
                    {window?.location?.hostname === 'localhost' && date}
                    <Flex
                      sx={{
                        justifyContent: 'space-between',
                      }}
                    >
                      <Text>{parseAndFormatDate(date, 'MMMM d, yyyy')}</Text>

                      <Fragment>
                        <Text>Qty. {qty}</Text>
                      </Fragment>
                    </Flex>
                    <Flex
                      sx={{
                        justifyContent: 'space-between',
                      }}
                    >
                      {'timeSlot' in selection &&
                        selection.timeSlot?.[index] &&
                        !FLOWS_WITH_NO_TIME.includes(flow) && (
                          <Text
                            data-date={`${hours[index]}:${selection.timeSlot?.[index]?.startRaw.minutes} ${selection.timeSlot?.[index]?.startRaw.meridian}`}
                            data-testid="cart-tickets"
                          >
                            {formatTimeRangeDisplay(
                              putInArray(selection.timeSlot),
                              index,
                              flow === Flow.ON_THE_ROCKS,
                            )}
                          </Text>
                        )}
                      <Fragment>
                        <Text>
                          {formatMoney(selection.price, undefined, true)}
                        </Text>
                      </Fragment>
                    </Flex>
                  </Fragment>
                ))}
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
      <Flex
        sx={{
          cursor: 'pointer',
          padding: ['8px', 3],
        }}
      >
        <Close onClick={() => remove(selection)} />
      </Flex>
    </Flex>
  );
};
