import React, { useEffect, useRef, useState } from 'react';
import { navigate } from 'gatsby';
import {
  Flex,
  IntrinsicBox,
  Link,
  Text,
  Box,
  Button,
  Modal,
} from '@tishman/components';
import pluralize from 'pluralize';

import {
  useAppSelector,
  selectCheckoutCartSelectionsCount,
  selectCheckoutCartAddOnsSelectionsCount,
  useAppDispatch,
  actions,
} from '../../store';
import { formatMoney } from '../../utils';
import { Translatable, useTranslations } from '../../utils/use-translations';
import { useLocalizedLink } from '../../utils/use-localized-link';
import { useCheckBasketForSummary } from '../../utils/use-check-basket-for-summary';
import { ModalLoader } from '../BuyTickets';
import { ModalErrorCard } from '../BuyTickets/Modals/ModalErrorCard';
import { newRelic } from '../../utils/new-relic';
import { CART_LOCAL_STORAGE_KEY } from '../../services/viva/constants';
import { removeObjectForLocalStorageKey } from '../../store/utils';
import { VIVA_ERROR_CODES } from '../../services/viva/constants/errorcodes';

import { CartTotalLine } from './TotalLine';

import type { ThemeUIStyleObject } from 'theme-ui';
import type { CartTranslations } from '../../data/translations/cart';
import type { CheckoutTotals } from '../../store/types';

type Props = {
  totals: CheckoutTotals;
  sx?: ThemeUIStyleObject;
};

export const CartSummary = ({ totals, sx }: Props) => {
  const translations = useTranslations<CartTranslations>(Translatable.Cart);
  const getLocalizedLink = useLocalizedLink();
  const selectionsCount = useAppSelector(selectCheckoutCartSelectionsCount);
  const addOnsCount = useAppSelector(selectCheckoutCartAddOnsSelectionsCount);
  const totalCount = selectionsCount + addOnsCount;
  const dispatch = useAppDispatch();

  const [isLoading, setIsLoading] = useState(false);
  const [isSticky, setIsSticky] = useState(true);
  const [errorModalMessage, setErrorModalMessage] = useState('');
  const [shouldResetCart, setShouldResetCart] = useState(false);

  const buttonBoxRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const selectionsCountRef = useRef(totalCount);

  // Handle navigation away from page
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (shouldResetCart) {
        handleErrorModalClose();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [shouldResetCart]);

  // On mount, check basket with Viva
  const { checkBasket, error: checkBasketError } = useCheckBasketForSummary({
    setIsLoading,
  });

  useEffect(() => {
    checkBasket();
  }, []);

  // Update totals on selectionsCount change
  useEffect(() => {
    if (
      totalCount !== selectionsCountRef.current &&
      totalCount > 0 &&
      !checkBasketError
    ) {
      checkBasket();
      selectionsCountRef.current = totalCount;
    }
    if (checkBasketError) {
      if (
        checkBasketError.message.indexOf(
          VIVA_ERROR_CODES.GENERIC_SEAT_CAPACITY.toString(),
        ) > -1 ||
        checkBasketError.message.indexOf(
          VIVA_ERROR_CODES.SEAT_SOLD_OUT.toString(),
        ) > -1
      ) {
        setErrorModalMessage(
          'Unfortunately the tickets you have selected are no longer available.',
        );
        setShouldResetCart(true);
      } else {
        setErrorModalMessage(checkBasketError.message);
      }
    }
  }, [totalCount, checkBasketError]);

  // On scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsSticky(false);
          } else {
            setIsSticky(true);
          }
        });
      },
      {
        rootMargin: '0px',
        threshold: 1,
      },
    );

    if (buttonBoxRef.current) observer.observe(buttonBoxRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleErrorModalClose = () => {
    if (shouldResetCart) {
      removeObjectForLocalStorageKey(CART_LOCAL_STORAGE_KEY);
      dispatch(actions.wizard.reset());
      dispatch(actions.checkout.reset());
      setShouldResetCart(false);
    }
    navigate('/buy-tickets/');
  };

  if (!totals || !translations) return null;

  return (
    <Flex
      sx={{
        flexDirection: 'column',
        ...sx,
      }}
    >
      <CartTotalLine
        sx={{
          display: ['flex', 'flex', 'none'],
        }}
        totals={totals}
      />
      <Flex
        sx={{
          bg: '#F8E9DB',
          py: 56,
          px: '16px',
          width: '100%',
          maxWidth: ['none', 'none', 357],
        }}
      >
        <Flex
          sx={{
            flexDirection: 'column',
            rowGap: [5],
            flex: 1,
          }}
        >
          <Flex
            sx={{
              flexDirection: 'column',
              rowGap: [2],
            }}
          >
            <Text
              sx={{
                fontSize: [3],
                fontWeight: 500,
              }}
            >
              {translations.summary.title}
            </Text>
            <Flex
              sx={{
                justifyContent: 'space-between',
              }}
            >
              <Text
                sx={{
                  textTransform: 'capitalize',
                }}
              >
                {pluralize('Item', selectionsCount)} ({selectionsCount}{' '}
                {pluralize('Item', selectionsCount)})
              </Text>
              {totals?.total && !isLoading ? (
                <Text data-testid="subtotal-amount">
                  {totals?.inclusiveTax &&
                  parseFloat(totals.inclusiveTax) > 0 &&
                  totals.subtotal
                    ? formatMoney(totals.subtotal)
                    : formatMoney(totals.total)}
                </Text>
              ) : null}
            </Flex>
            <Flex
              sx={{
                justifyContent: 'space-between',
              }}
            >
              <Text
                sx={{
                  textTransform: 'capitalize',
                }}
              >
                {pluralize('Tax', 5)}
              </Text>
              {totals?.totalTax && !isLoading ? (
                <Text data-testid="total-tax-amount">
                  {totals?.inclusiveTax && parseFloat(totals.inclusiveTax) > 0
                    ? formatMoney(
                        parseFloat(totals.totalTax) -
                          parseFloat(totals.inclusiveTax),
                      )
                    : formatMoney(totals.totalTax)}
                </Text>
              ) : null}
            </Flex>
          </Flex>
          <Flex
            sx={{
              flexDirection: 'column',
            }}
          >
            <Flex
              sx={{
                flexDirection: 'column',
                rowGap: [2],
              }}
            >
              <Flex
                sx={{
                  justifyContent: 'space-between',
                }}
              >
                <Text sx={{ textTransform: 'capitalize' }}>
                  {translations.summary.total}
                </Text>
                {totals.totalWithTax ? (
                  <Text data-testid="total-with-tax-amount">
                    {formatMoney(totals.totalWithTax)}
                  </Text>
                ) : null}
              </Flex>
              {(parseInt(totals.totalDiscount ?? '0') ?? 0) > 0 && (
                <Flex
                  sx={{
                    justifyContent: 'space-between',
                  }}
                >
                  <Text sx={{ textTransform: 'capitalize' }}>
                    {translations.summary.promoCode}
                  </Text>
                  {totals.totalDiscount ? (
                    <Text data-testid="total-discount-amount">
                      {formatMoney(totals.totalDiscount, undefined, true)}
                    </Text>
                  ) : null}
                </Flex>
              )}
            </Flex>
            <IntrinsicBox
              sx={{
                height: '1px',
                bg: 'black',
                my: [3],
              }}
            />
            <Flex
              sx={{
                justifyContent: 'space-between',
              }}
            >
              <Text sx={{ textTransform: 'capitalize', fontWeight: 500 }}>
                Total with tax
              </Text>
              {totals.totalWithTax ? (
                <Text
                  data-testid="total-with-tax-amount"
                  sx={{
                    fontWeight: 500,
                  }}
                >
                  {formatMoney(totals.totalWithTax, undefined, true)}
                </Text>
              ) : null}
            </Flex>
          </Flex>
          <Box
            ref={buttonBoxRef}
            sx={{
              height: 58,
              zIndex: 2,
            }}
          >
            <Button
              className="view-checkout"
              data-gtm-initiator="cart"
              disabled={isLoading}
              onClick={() => {
                newRelic.logCartAction(
                  'CartSummary - (go to) Checkout',
                  'onClick',
                );
                navigate(getLocalizedLink('/checkout/'));
              }}
              ref={buttonRef}
              sx={{
                height: 58,
                py: 0,
                fontSize: 1,
                letterSpacing: '2px',
                transition: 'all 0.2s',
                position: [
                  // Mobile
                  isSticky ? 'fixed' : 'relative',
                  // Tablet
                  isSticky ? 'fixed' : 'relative',
                  // Desktop
                  'relative',
                ],
                bottom: 0,
                left: 0,
                width: '100%',
              }}
              variant="inverted"
            >
              proceed to checkout
            </Button>
          </Box>
        </Flex>
      </Flex>
      {isLoading && <ModalLoader />}
      <Modal
        id={`cart-error-modal-${new Date().getTime()}`}
        isOpen={!!errorModalMessage}
        onClose={handleErrorModalClose}
      >
        <ModalErrorCard
          closeModal={handleErrorModalClose}
          description={errorModalMessage}
          errorBlock={
            <Button
              aria-label="Return to Buy Tickets"
              sx={{
                p: 0,
                mt: 24,
                textAlign: 'left',
                maxWidth: [257, '100%'],
              }}
              variant="underline"
            >
              <Link
                onClick={(e) => {
                  e.preventDefault();
                  handleErrorModalClose();
                }}
                sx={{
                  'textDecoration': 'none',
                  'color': 'inherit',
                  'fontSize': 1,
                  'fontWeight': 500,
                  'lineHeight': 1.5,
                  ':hover': {
                    textDecoration: 'none',
                  },
                }}
                to="/buy-tickets/"
              >
                Return to Buy Tickets
              </Link>
            </Button>
          }
          title={checkBasketError?.name ?? ''}
        />
      </Modal>
    </Flex>
  );
};
