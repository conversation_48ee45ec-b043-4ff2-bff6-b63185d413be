/** @jsxImportSource theme-ui @jsxRuntime classic */
import { H } from '@hzdg/sectioning';
import {
  Box,
  Container,
  Flex,
  Grid,
  Text,
  Link,
  TishmanThemeName,
  SxStyleProp,
  AnchorSection,
  IntrinsicImage,
} from '@tishman/components';
import * as Icons from '@tishman/icons';
import React, { memo, useEffect, useState } from 'react';
import { IGatsbyImageData } from 'gatsby-plugin-image';
import { Theme, ThemeUIStyleObject } from 'theme-ui';

import { useLocalizedLink } from '../utils/use-localized-link';

/* eslint-disable react/prop-types */
interface DescriptionChildren {
  text: string;
}

export interface Description {
  children: DescriptionChildren[];
}

export interface Url {
  url: string;
  caption: string;
}

export interface TicketOfferingTicket {
  title: string;
  url: Url;
  image: {
    alt: string;
    asset: {
      gatsbyImageData: IGatsbyImageData;
    };
  };
  description: Description[];
  slugId: string;
  order: number;
}

export interface TicketOfferingProps {
  includedTicketsArray: TicketOfferingTicket[];
  title?: string;
  sx?: SxStyleProp;
  id: string;
  theme?: TishmanThemeName;
  description?: string;
  link?: string;
}

interface TicketCardProps {
  offering: TicketOfferingTicket;
  i: number;
}

const getContainerConfig = (length: number) => {
  // For layouts where we need precise control over 2-item rows filling full width,
  // we'll use flexbox instead of CSS Grid
  const needsFlexbox = length === 5 || length === 7;

  if (needsFlexbox) {
    return {
      'display': 'flex',
      'flexWrap': 'wrap',
      '& > *': {
        flexBasis:
          length === 5 ? 'calc(33.333% - 16px)' : 'calc(33.333% - 16px)',
        flexGrow: 1,
        minWidth: '280px',
      },
      // For 5 items: first 3 take 1/3 width, next 2 take 1/2 width each
      ...(length === 5 && {
        '& > *:nth-of-type(4), & > *:nth-of-type(5)': {
          flexBasis: 'calc(50% - 16px)',
        },
      }),
      // For 7 items: first 3 take 1/3, next 2 take 1/2, last 2 take 1/2
      ...(length === 7 && {
        '& > *:nth-of-type(4), & > *:nth-of-type(5)': {
          flexBasis: 'calc(50% - 16px)',
        },
        '& > *:nth-of-type(6), & > *:nth-of-type(7)': {
          flexBasis: 'calc(50% - 16px)',
        },
      }),
    };
  }

  // For simpler layouts, use CSS Grid
  switch (length) {
    case 1:
      return { gridTemplateColumns: '1fr' };
    case 2:
      return { gridTemplateColumns: ['1fr', '1fr', '1fr 1fr'] };
    case 3:
      return { gridTemplateColumns: ['1fr', '1fr', '1fr 1fr 1fr'] };
    case 4:
      return { gridTemplateColumns: ['1fr', '1fr', '1fr 1fr'] };
    case 6:
      return { gridTemplateColumns: ['1fr', '1fr', '1fr 1fr 1fr'] };
    default:
      return { gridTemplateColumns: ['1fr', '1fr', '1fr 1fr 1fr'] };
  }
};

export function TicketOfferings({
  includedTicketsArray,
  title,
  sx,
  id,
  description,
  link,
  theme = 'Rock Center Cream',
}: TicketOfferingProps): React.ReactNode {
  const getLocalizedLink = useLocalizedLink();
  const [shouldEagerLoadImages, setShouldEagerLoadImages] = useState(false);

  // Detect if page loaded with fragment targeting this section to fix Chrome image loading issue
  // Use small timeout to let AnchorSection middleware initialize first
  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.hash === `#${id}`) {
      const timer = setTimeout(() => setShouldEagerLoadImages(true), 100);
      return () => clearTimeout(timer);
    }
  }, [id]);

  if (includedTicketsArray.length === 0) {
    return null;
  }

  const TicketCard: React.FC<TicketCardProps> = memo(({ offering, i }) => (
    <Box
      bg="white"
      key={i}
      sx={{
        willChange: 'transform',
        transform: 'translateZ(0)',
      }}
    >
      <Flex
        sx={{
          flexDirection: 'column',
          height: '100%',
        }}
      >
        <Box>
          {offering.image?.asset?.gatsbyImageData && (
            <IntrinsicImage
              alt={offering.image.alt?.replace('N/A', '')}
              image={offering.image.asset.gatsbyImageData}
              loading={shouldEagerLoadImages ? 'eager' : 'lazy'}
              ratio={316 / 212}
            />
          )}
        </Box>
        <Box sx={{ flex: '1 1 auto', pt: 4, px: 3 }}>
          <H
            sx={{
              variant: 'styles.h2',
              fontFamily: 'headingSecondary',
              fontSize: 5,
              fontWeight: 'heading',
              color: 'black',
            }}
          >
            {offering.title}
          </H>
          <Box sx={{ pb: 3, pt: 4 }}>
            <Text sx={{ fontSize: 3, color: 'black', overflow: 'auto' }}>
              {offering.description[0].children.map((child, index) => (
                <React.Fragment key={index}>{child.text}</React.Fragment>
              ))}
            </Text>
          </Box>
        </Box>
        <Box sx={{ mt: 'auto', pb: [0, 4], px: [0, 3] }}>
          <Link
            sx={{
              display: 'block',
              textAlign: 'center',
              width: '100%',
              bg: 'black',
              color: 'white',
            }}
            to={getLocalizedLink(offering.url.url)}
            variant="button"
          >
            Buy Tickets
          </Link>
        </Box>
      </Flex>
    </Box>
  ));

  TicketCard.displayName = 'TicketCard';

  const MemoizedSection = memo(AnchorSection);

  return (
    <MemoizedSection id={id} theme={theme}>
      <Container paddingY={5}>
        {/* HEADER SECTION: TITLE, DESCRIPTION, AND NAV LINKS */}
        <Flex
          sx={{
            flexDirection: ['column', 'row'],
            justifyContent: 'space-between',
            mb: [2, null, 4],
            pt: [0, 5],
          }}
        >
          {title && (
            <H
              sx={{
                variant: 'styles.h1',
                fontFamily: 'headingSecondary',
              }}
            >
              {title}
            </H>
          )}
          {description && (
            <Box
              sx={{
                fontSize: [2, 3],
                letterSpacing: [2, 1],
                opacity: 0.8,
                display: ['none', null, 'block'],
                ...sx,
              }}
            >
              {description}
            </Box>
          )}
          <Flex sx={{ flexDirection: ['column', 'row'] }}>
            {link?.includes('tor=true') && (
              <Link
                sx={{ maxHeight: 45, maxWidth: 165, mt: [3, 0] }}
                to={getLocalizedLink(
                  '/attractions/top-of-the-rock-observation-deck/plan-your-visit/?tab=compare-tickets',
                )}
                variant="underline"
              >
                Compare Tickets
              </Link>
            )}
            {link && (
              <Link
                sx={{
                  maxHeight: 45,
                  maxWidth: [100, 'none'],
                  ml: [0, 5],
                  mt: [3, 0],
                }}
                to={link ?? getLocalizedLink('/buy-tickets')}
                variant="underline"
              >
                See All
                <Icons.Arrow
                  sx={{ display: ['inline-block', 'none'], ml: 2 }}
                />
              </Link>
            )}
          </Flex>
        </Flex>

        {/* TICKET OFFERINGS GRID */}
        <Grid
          gap={3}
          py={4}
          sx={
            getContainerConfig(
              includedTicketsArray.length,
            ) as ThemeUIStyleObject<Theme>
          }
        >
          {includedTicketsArray.map((offering, i) => {
            return <TicketCard i={i} key={i} offering={offering} />;
          })}
        </Grid>
      </Container>
    </MemoizedSection>
  );
}
