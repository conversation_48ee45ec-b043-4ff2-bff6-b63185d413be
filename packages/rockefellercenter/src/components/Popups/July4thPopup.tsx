/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, Button, Flex, Link, Modal, Text } from '@tishman/components';
import * as Icons from '@tishman/icons';
import React, { useState } from 'react';

import julyFourthImage from '../../images/July4Popup.jpg';

export const July4thPopup = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleModalOpen = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <Flex
      sx={{
        alignItems: 'flex-start',
        border: '2px solid #275DC5',
        borderRadius: '4px',
        display: 'flex',
        flexDirection: 'row',
        gap: '18px',
        padding: '16px 12px',
        my: 2,
        width: '100%',
        minHeight: '156px',
      }}
    >
      <Icons.Fireworks
        aria-hidden
        style={{ height: '30px', flex: '0 0 30px' }}
      />
      <Flex sx={{ flexDirection: 'column', gap: 2, maxWidth: '343px' }}>
        <Text
          sx={{
            fontStyle: 'normal',
            fontWeight: 500,
            fontSize: 2,
          }}
        >
          Celebrate July 4th at Top of the Rock! <br />
          Enjoy the fireworks from 70 floors up, plus free festive food, drinks,
          live entertainment, and fun for the whole family.
          <br />
          Entry from 6-10 PM.
        </Text>
        <Button
          aria-expanded={isModalOpen}
          aria-haspopup="dialog"
          onClick={handleModalOpen}
          sx={{
            'fontWeight': 500,
            'fontSize': 2,
            'lineHeight': '24px',
            'textDecoration': 'underline',
            'color': 'text',
            'backgroundColor': 'transparent',
            'border': 'none',
            'padding': 0,
            'margin': 0,
            'textAlign': 'left',
            'textTransform': 'none',
            '&:hover, &:not([disabled]):hover': {
              backgroundColor: 'transparent',
              border: 'none',
              textDecoration: 'underline',
            },
          }}
        >
          Learn More
        </Button>
      </Flex>

      {/* Modal */}
      <Modal
        id="july-fourth-modal"
        isOpen={isModalOpen}
        onClose={handleModalClose}
        sx={{
          width: '100%',
          height: '100%',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            p: '10px',
            backgroundColor: 'white',
            transition: 'all 0.3s ease-in-out',
            opacity: isModalOpen ? 1 : 0,
            position: 'relative',
            border: '1px solid black',
          }}
        >
          <Flex
            onClick={handleModalClose}
            sx={{
              'position': 'absolute',
              'top': '20px',
              'right': '20px',
              'cursor': 'pointer',
              'color': 'white',
              '&:hover': {
                color: 'white',
                backgroundColor: 'transparent',
              },
              '& svg path': {
                strokeWidth: 1,
              },
            }}
          >
            <Icons.Close />
          </Flex>
          <Box
            sx={{
              backgroundColor: 'white',
              border: '1px solid black',
              width: '100%',
              maxWidth: '527px',
            }}
          >
            <img
              alt="July 4th at Top of the Rock"
              src={julyFourthImage}
              style={{
                width: '100%',
                maxWidth: '527px',
                height: '260px',
                objectFit: 'cover',
              }}
            />
            <Flex sx={{ p: '16px', flexDirection: 'column', gap: '12px' }}>
              <Text
                as="h2"
                sx={{
                  fontSize: 3,
                  fontWeight: 600,
                }}
              >
                4th of the July at Top of the Rock
              </Text>
              <Text>
                Celebrate Independence Day with spectacular 360-degree views, a
                prime vantage point for the firework show, festive food and
                drinks, live-DJ entertainment, and family-friendly activities
                like face painting, glow sticks, and more! Tickets include a
                drink, hot dog (veggie option available), ice cream, and cookie,
                with special pricing for kids 12 and under. Spots are limited!
              </Text>
              <Link
                href="/buy-tickets/top-of-the-rock?date=2025-07-04&period=2"
                onClick={handleModalClose}
                sx={{
                  'display': 'inline-flex',
                  'justifyContent': ['left', 'center'],
                  'alignItems': 'center',
                  'width': ['100%', 'fit-content'],
                  'height': '44px',
                  'color': 'white',
                  'textDecoration': 'none',
                  '&:active, &:focus, &:visited, &:hover': {
                    textDecoration: 'none',
                    color: 'white',
                  },
                }}
                variant="button"
              >
                Buy Tickets
              </Link>
            </Flex>
          </Box>
        </Box>
      </Modal>
    </Flex>
  );
};
