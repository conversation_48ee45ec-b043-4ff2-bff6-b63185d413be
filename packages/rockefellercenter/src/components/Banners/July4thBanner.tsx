/** @jsxImportSource theme-ui @jsxRuntime classic */
import { useLocation } from '@reach/router';
import { Box, Flex, Link, Text } from '@tishman/components';
import * as Icons from '@tishman/icons';
import React from 'react';

export const July4thBanner = () => {
  const location = useLocation();
  const shouldShowTopBanner = location.pathname === '/buy-tickets/';

  if (!shouldShowTopBanner) return null;

  return (
    <Link
      href="/buy-tickets/top-of-the-rock?date=2025-07-04&period=2"
      sx={{
        'textDecoration': 'none',
        'color': 'inherit',
        '&:active, &:focus, &:visited, &:hover': {
          textDecoration: 'none',
          color: 'inherit',
        },
        '& *, & svg, & svg *': {
          color: 'inherit',
        },
      }}
    >
      <Flex
        sx={{
          width: '100%',
          minHeight: '66px',
          backgroundColor: 'modalBackground',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: ['column', 'row'],
          position: 'sticky',
          top: 0,
          zIndex: 100,
        }}
      >
        <Flex>
          <Icons.Fireworks sx={{ height: '30px' }} />
          <Text
            sx={{
              fontFamily: 'headingSecondary',
              fontWeight: 500,
              fontSize: 24,
              letterSpacing: '-0.02em',
              ml: '12px',
            }}
          >
            4th of July
          </Text>
          <Text
            sx={{
              mx: 2,
              fontFamily: 'body',
              fontWeight: 400,
              fontSize: 1,
              lineHeight: '15px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            on
          </Text>
          <Text
            sx={{
              fontFamily: 'headingSecondary',
              fontWeight: 500,
              fontSize: 24,
              letterSpacing: '-0.02em',
            }}
          >
            Top of the Rock
          </Text>
        </Flex>
        <Flex
          sx={{
            alignItems: 'center',
            position: ['relative', 'absolute'],
            right: [2, '24px'],
          }}
        >
          <Text
            sx={{
              fontFamily: 'headingSecondary',
              fontWeight: 500,
              fontSize: 14,
              letterSpacing: '2px',
              textTransform: 'uppercase',
              marginRight: 1,
            }}
          >
            LEARN MORE
          </Text>
          <Icons.Arrow sx={{ ml: '10px' }} />
        </Flex>
      </Flex>
    </Link>
  );
};
