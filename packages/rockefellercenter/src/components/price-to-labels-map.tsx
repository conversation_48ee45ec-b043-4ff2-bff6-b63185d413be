/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, Flex, Text } from '@tishman/components';
import * as Icons from '@tishman/icons';
import React from 'react';

import { formatMoney } from '../utils';
import {
  FLOWS_WITH_DISCOUNTED_TICKETS,
  Flow,
} from '../services/viva/constants';

import type { SxStyleProp } from '@tishman/components';
import type { Availability } from './BuyTickets/SegmentedTimePicker/Availabilities';

export const PriceToLabelsMap = ({
  timeSlot,
  sx,
  flow,
}: {
  timeSlot: Availability;
  sx?: SxStyleProp;
  flow: Flow;
}) => {
  const priceMap = new Map<number | string, string[]>();

  timeSlot.prices.forEach((price) => {
    const labels = priceMap.get(price.value) || [];
    priceMap.set(price.value, [...labels, price.label]);
  });

  const prices = Array.from(priceMap).map(([value, labels]) => ({
    label:
      labels.length > 1
        ? labels.slice(0, -1).join(', ') + ' / ' + labels.slice(-1)
        : labels[0],
    value,
  }));

  const textStyles: SxStyleProp = {
    marginLeft: [1, 2],
    fontSize: [1, 2],
    whiteSpace: 'pre-wrap',
    lineHeight: '1',
  };

  const boldTextStyles: SxStyleProp = {
    ...textStyles,
    fontWeight: 'medium',
    lineHeight: '1',
  };

  return (
    <Flex sx={{ justifyContent: 'space-between', alignItems: 'center', ...sx }}>
      <Flex
        sx={{
          minWidth: '24px',
          minHeight: '24px',
          alignItems: 'center',
        }}
      >
        {timeSlot.isFireworks ? (
          <Box
            id="fireworks-icon"
            sx={{
              display: 'inline-flex',
              width: ['18px', '24px'],
            }}
          >
            <Icons.Fireworks />
          </Box>
        ) : FLOWS_WITH_DISCOUNTED_TICKETS.includes(flow) &&
          timeSlot.isDiscounted ? (
          <Box
            sx={{
              'display': 'inline-flex',
              'color': 'OLIVE',
              'stroke': 'black',
              'svg path': { stroke: 'black' },
              'width': ['18px', '24px'],
            }}
          >
            <Icons.DiscountTag />
          </Box>
        ) : timeSlot.isSunset ? (
          <Box
            id="sunset-icon"
            sx={{
              display: 'inline-flex',
              svg: {
                path: { stroke: 'text' },
                fill: '#FAAF5E',
              },
              width: ['18px', '24px'],
            }}
          >
            <Icons.SunsetIcon />
          </Box>
        ) : null}
      </Flex>

      {prices.map((price, index) => (
        <Box data-testid="price-container" key={index}>
          <Text as="span" data-testid="price-value" sx={{ ...boldTextStyles }}>
            {formatMoney(price.value).replace('US$', '$')}
          </Text>
          <Text as="span" data-testid="price-label" sx={{ ...textStyles }}>
            {price.label}
          </Text>
        </Box>
      ))}
    </Flex>
  );
};
