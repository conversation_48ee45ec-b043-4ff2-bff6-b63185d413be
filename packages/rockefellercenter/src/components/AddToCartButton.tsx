import React, { Fragment, useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from '@tishman/components';
import { getWindow } from '@hzdg/dom-utils';
import { Flex } from 'theme-ui';
import { createPortal } from 'react-dom';

import {
  actions,
  selectAttributes,
  selectComboPerformances,
  selectCounterEndTime,
  selectFlow,
  selectHasPerformances,
  selectPerformances,
  selectShopCartAddOnSelections,
  selectShopCartDateTime,
  selectShopCartSelections,
  selectTotalQuantity,
  selectWizardCurrentStep,
  useAppDispatch,
  useAppSelector,
} from '../store';
import {
  selectCheckoutCart,
  selectCheckoutCoupon,
  selectCheckoutItemsFlat,
} from '../store/selectors/checkout';
import {
  getObjectForLocalStorageKey,
  setObjectForLocalStorageKey,
} from '../store/utils';
import {
  ADDON_EVENTS_WITH_TIME_CAPACITY,
  CHAMPAGNE_TOAST_COMP_AK,
  BosEventForAddOnStatGroup,
  CART_LOCAL_STORAGE_KEY,
  COMP_TICKETS,
  FLOWS_WITH_COMP_TICKETS,
  Flow,
  StatGroup,
  TCompAddOn,
  CompAddOns,
  CompChampagneToastDefaultFlows,
  CompPhotoPackageDefaultFlows,
} from '../services/viva/constants';
import { useGetEditQueryParams } from '../utils/use-ticket-edit-mode';
import { EMPTY_SHOP_CART } from '../store/slices';
import { useCheckBasketMutation } from '../services/viva';
import { isBosError, putInArray } from '../services/viva/utils';
import { newRelic } from '../utils/new-relic';
import { saveBuyTicketsPageSessionStorage } from '../utils/reloadBuyTicketsPageSessionStorage';

import { CartModal } from './BuyTickets/Modals/Cart/Cart';
import {
  AddOnItem,
  DataLayerContext,
  ModalLoader,
  safeDataLayerPush,
} from './BuyTickets';

import type { CheckoutType } from '../store/types';
import type { ShopCartSelection } from '../store/types/order';

type Props = {
  disabled?: boolean;
  isLoading?: boolean;
  isAddOn?: boolean;
  stepNumber: number;
};

const ButtonText = ({
  isAddOn,
  isLoading,
  editMode,
}: {
  isAddOn?: boolean;
  isLoading?: boolean;
  editMode?: boolean;
}) => {
  if (isLoading) {
    return (
      <Spinner
        sx={{
          width: '16px',
          height: '16px',
        }}
      />
    );
  } else if (editMode) {
    return <Fragment>update cart</Fragment>;
  } else if (isAddOn) {
    return <Fragment>continue</Fragment>;
  }

  return <Fragment>continue</Fragment>;
};

export const AddToCartButton = ({
  disabled: _disabled,
  isAddOn,
  isLoading = false,
  stepNumber,
}: Props) => {
  const window = getWindow();
  const { editMode, originalDateTime } = useGetEditQueryParams();
  const cart = useAppSelector(selectCheckoutCart);
  const checkoutItems = useAppSelector(selectCheckoutItemsFlat);
  const selections = useAppSelector(selectShopCartSelections);
  const addOns = useAppSelector(selectShopCartAddOnSelections);
  const _performances = useAppSelector(selectPerformances);
  const comboPerformances = useAppSelector(selectComboPerformances);
  const shopCartAddonSelections = useAppSelector(selectShopCartAddOnSelections);
  const shopCartDateTime = useAppSelector(selectShopCartDateTime);
  const counterEndTime = useAppSelector(selectCounterEndTime);
  const checkoutCoupon = useAppSelector(selectCheckoutCoupon);
  const flow = useAppSelector(selectFlow);
  const attributes = useAppSelector(selectAttributes);
  const hasPerformances = useAppSelector(selectHasPerformances);
  const currentStep = useAppSelector(selectWizardCurrentStep);
  const totalQuantity = useAppSelector(selectTotalQuantity);
  const dispatch = useAppDispatch();
  const checkBasketMutation = useCheckBasketMutation(flow);
  const performanceHash =
    useAppSelector((state) => state.order.shopCart.addOnsPerformancesHash) ??
    {};

  const disabled =
    _disabled ??
    (!hasPerformances || selections.length === 0 || currentStep !== stepNumber);

  const [showCartModal, setCartShowModal] = useState(false);
  const [showLoadingModal, setShowLoadingModal] = useState(false);

  const performances = [Flow.RC_ROCK_PASS].includes(flow)
    ? {
        ..._performances,
        [flow]: comboPerformances.map((perf) => perf.performanceAK),
      }
    : _performances;

  // Reset shopCart if moving away from page
  useEffect(() => {
    if (!window) return;

    const callback = () => {
      // The purpose of this condition is to handle page refresh scenarios where items have been added to the cart
      // and the modal is open. When the user refreshes the page, we need to enforce edit mode by setting session storage.
      if (showCartModal) {
        saveBuyTicketsPageSessionStorage(flow, shopCartDateTime.toString());
      }

      dispatch(actions.order.resetShopCart());
    };

    window.addEventListener('beforeunload', callback);

    return () => window.removeEventListener('beforeunload', callback);
  }, [window, dispatch, showCartModal]);

  // Close modal if no selections
  useEffect(() => {
    if (checkoutItems.length === 0) {
      setCartShowModal(false);
    }
  }, [checkoutItems]);

  const sendGA4Event = () => {
    const isShopCartSelection = (
      ticket: ShopCartSelection | AddOnItem,
    ): ticket is ShopCartSelection => {
      return 'count' in ticket;
    };

    const items = [...selections, ...addOns]
      .filter((ticket) => ticket)
      .map((ticket) => {
        const discountAmount =
          'discount' in ticket && ticket.discount ? ticket.discount : '0.00';
        const timeSlot =
          'timeSlot' in ticket ? ticket.timeSlot?.[0] : undefined;
        const quantity = isShopCartSelection(ticket)
          ? ticket.count
          : ticket.quantity ?? 0;
        const price = isShopCartSelection(ticket) ? ticket.price : ticket.price;
        return {
          item_id: isShopCartSelection(ticket) ? ticket.productAK : ticket.ak,
          item_name:
            'label' in ticket
              ? ticket.label
              : ticket.name ?? ticket.description,
          item_category: flow,
          price: (price / 100).toFixed(2),
          quantity,
          discount: discountAmount,
          variant: timeSlot?.isDiscounted
            ? 'evening'
            : timeSlot?.isSunset
            ? 'sunset'
            : 'standard',
        };
      })
      .filter((item) => (item?.quantity ?? 0) > 0);

    safeDataLayerPush({
      reset: function (this: DataLayerContext) {
        this.reset();
      },
    });

    safeDataLayerPush({
      event: 'add_to_cart',
      currency: 'USD',
      value: (
        [...selections, ...addOns].reduce((acc, sel) => {
          if (!sel) return acc;

          let total = 0;
          if (isShopCartSelection(sel)) {
            total = sel.price * sel.count;
          } else {
            total = sel.price * (sel.quantity ?? 1);
          }
          return acc + total;
        }, 0) / 100
      ).toFixed(2),
      items,
    });
  };

  const onClick = async () => {
    setShowLoadingModal(true);
    const _cart = cart ?? EMPTY_SHOP_CART;
    let cartSelections = [...(_cart.selections[flow] ?? [])];

    // Hash map of add on aks
    const combinedAsObject: Record<string, AddOnItem | undefined> = {};

    // Callback to merge addOns, by incrementing quantity if ak is in hash map
    const mergeItems = (item: AddOnItem) => {
      const existingItem = { ...combinedAsObject[item.ak] };
      // Only add or update items if they are a part of the current flow
      if (flow === item.flow) {
        if (existingItem && existingItem.quantity !== undefined) {
          combinedAsObject[item.ak] = {
            ...existingItem,
            ...item,
            capacityGuid: item.capacityGuid,
          } as AddOnItem;
        } else {
          combinedAsObject[item.ak] = item;
        }
      }
    };
    const currentAddOns = _cart.addOnSelections[flow]?.[shopCartDateTime] ?? [];

    // Combine all addOns between shopCart and checkout cart
    [...currentAddOns, ...shopCartAddonSelections].forEach(mergeItems);

    // Convert hash map back to array
    let cartAddOnSelections = Object.values(combinedAsObject) as AddOnItem[];

    if (editMode && originalDateTime) {
      // Replace the entire cartSelections with the new selections
      cartSelections = selections.map((sel) => ({
        ...sel,
        dateTime: shopCartDateTime,
      }));

      dispatch(
        actions.checkout.setFlowSelection({
          flow,
          data: cartSelections,
          performances,
          dateTime: shopCartDateTime,
        }),
      );
      newRelic.logCartAction(
        'AddToCartButton (Edit Mode) - onClick',
        'setFlowSelection',
        {
          flow,
          cartSelections,
          performances,
          shopCartDateTime,
        },
      );
      // Handle add-ons
      if (FLOWS_WITH_COMP_TICKETS.includes(flow)) {
        // Create a map of existing comp tickets
        const compAddOnsMap = new Map(
          currentAddOns
            .filter((addOn) => COMP_TICKETS.includes(addOn.statGroup))
            .map((addOn) => [addOn.ak, addOn]),
        );

        // Merge new add-ons with existing comp tickets
        cartAddOnSelections = shopCartAddonSelections.map((addOn) => {
          const existingCompTicket = compAddOnsMap.get(addOn.ak);
          if (existingCompTicket) {
            // Preserve the existing performance information for comp tickets
            return {
              ...addOn,
              quantity: totalQuantity as number,
              performances: existingCompTicket.performances,
            };
          }
          return addOn;
        });

        // Add any comp tickets that weren't in the new selections
        compAddOnsMap.forEach((compAddOn) => {
          if (!cartAddOnSelections.some((addOn) => addOn.ak === compAddOn.ak)) {
            cartAddOnSelections.push(compAddOn);
          }
        });
      } else {
        // Overwrite with new add-on selections
        cartAddOnSelections = shopCartAddonSelections;
      }
      // Update performances for add-ons if needed
      if (performanceHash) {
        cartAddOnSelections = cartAddOnSelections.map((sel) => ({
          ...sel,
          performances: performanceHash[sel.ak]?.ak
            ? [performanceHash[sel.ak].ak].filter(
                (p): p is string => p !== undefined,
              )
            : sel.performances,
        }));
      }
      dispatch(
        actions.checkout.setAddOns({
          addOns: cartAddOnSelections,
          dateTime: shopCartDateTime,
          originalDateTime,
          flow,
        }),
      );
      newRelic.logCartAction(
        'AddToCartButton (Edit Mode) - onClick',
        'setAddOns',
        {
          cartAddOnSelections,
          shopCartDateTime,
          originalDateTime,
          flow,
        },
      );
    } else {
      let _selections = [...selections];
      // Find existing _selections
      const cartSelectionByDateIndex = cartSelections.findIndex(
        (sel) => sel.dateTime === shopCartDateTime,
      );

      // Rink Membership and Tor Express do not need a capacity guid
      if (![Flow.RINK_MEMBERSHIP, Flow.TOR_EXPRESS].includes(flow)) {
        const basket = await checkBasketMutation.mutateAsync({
          items: _selections.map((sel) => ({
            AK: sel.productAK,
            QTY: sel.count,
            performances: performances[flow].filter(
              (p): p is string => p !== undefined,
            ),
          })),
        });

        if ('ITEMLIST' in basket) {
          _selections = selections.map((sel) => {
            const updatedSelection = putInArray(basket.ITEMLIST.ITEM).find(
              (ITEM) => ITEM.AK === sel.productAK,
            );

            return {
              ...sel,
              capacityGuid: updatedSelection?.CAPACITYGUID,
              price: Math.ceil(
                (parseFloat(updatedSelection?.PRICE?.NET || '0') /
                  parseInt(updatedSelection?.QTY || '1')) *
                  100,
              ),
              tax: Math.ceil(
                parseFloat(updatedSelection?.PRICE?.TAX || '0') * 100,
              ),
            };
          });
        }
      }

      if (cartSelectionByDateIndex > -1) {
        // Update existing selection
        const selectionByDate = { ...cartSelections[cartSelectionByDateIndex] };
        const shopCartSelectionByStatGroup = _selections.find(
          (sel) =>
            [...sel.statGroup].sort().toString() ===
            [...selectionByDate.statGroup].sort().toString(),
        );
        selectionByDate.count += shopCartSelectionByStatGroup?.count ?? 0;

        cartSelections[cartSelectionByDateIndex] = selectionByDate;
      } else {
        // Add new selection
        cartSelections = cartSelections.concat(
          _selections.map((sel) => ({
            ...sel,
            dateTime: shopCartDateTime,
          })),
        );
      }

      // Certain addOns need a capacity guid, we must call checkBasket to get it
      if (performanceHash) {
        let capGuids: Record<string, string> = {};

        const addOnsWithCapacity = cartAddOnSelections.filter((addOn) =>
          ADDON_EVENTS_WITH_TIME_CAPACITY.includes(
            BosEventForAddOnStatGroup[addOn.statGroup],
          ),
        );

        if (addOnsWithCapacity.length > 0) {
          const performances = addOnsWithCapacity
            .filter((a) => performanceHash[a.ak]?.ak)
            .map((addOn) => performanceHash[addOn.ak]?.ak);

          if (performances.length) {
            const items = addOnsWithCapacity
              .filter((sel) => {
                // No capacity guid needed for flows that are not in the following array,
                // for the Beam
                if (
                  sel.statGroup === StatGroup.THE_BEAM &&
                  ![
                    Flow.TOR_GA,
                    Flow.THE_BEAM_PASS,
                    Flow.RC_ROCK_PASS,
                    Flow.TOR_VIP_ROCKSTAR,
                    Flow.RC_ROCK_PASS_VIP_HOLIDAY,
                  ].includes(flow)
                ) {
                  return false;
                }

                return true;
              })
              .map((sel) => ({
                AK: sel.ak,
                QTY: sel.quantity ?? 0,
                performances: performances.filter(
                  (p): p is string => p !== undefined,
                ),
              }));

            if (items.length) {
              const checkBasket = await checkBasketMutation.mutateAsync({
                items,
              });

              if (!isBosError(checkBasket)) {
                const items = Array.isArray(checkBasket.ITEMLIST.ITEM)
                  ? checkBasket.ITEMLIST.ITEM
                  : [checkBasket.ITEMLIST.ITEM];

                capGuids = Object.fromEntries(
                  items.map((item) => [item.AK, item.CAPACITYGUID]),
                );
              }
            }
          }
        }

        cartAddOnSelections = cartAddOnSelections.map((sel) => ({
          ...sel,
          ...(capGuids?.[sel.ak] ? { capacityGuid: capGuids[sel.ak] } : {}),
          performances: (performanceHash[sel.ak]?.ak
            ? [performanceHash[sel.ak].ak]
            : []
          ).filter((p): p is string => p !== undefined),
        }));
      }

      dispatch(
        actions.checkout.setFlowSelection({
          flow,
          data: cartSelections,
          performances,
          dateTime: shopCartDateTime,
        }),
      );

      newRelic.logCartAction('AddToCartButton - onClick', 'setFlowSelection', {
        flow,
        cartSelections,
        performances,
        shopCartDateTime,
      });

      // If we have comp tickets in the basket the preserve them
      // otherwise, just add all addons as if they were new
      if (FLOWS_WITH_COMP_TICKETS.includes(flow)) {
        const compAddOns = currentAddOns.filter((addOn) =>
          COMP_TICKETS.includes(addOn.statGroup),
        );

        const cartAddOnSelectionsWithCompTickets = [...cartAddOnSelections];

        compAddOns.forEach((compAddOn) => {
          const existingIndex = cartAddOnSelectionsWithCompTickets.findIndex(
            (addOn) => addOn.ak === compAddOn.ak,
          );

          if (existingIndex !== -1) {
            // Replace existing add-on with the comp add-on
            cartAddOnSelectionsWithCompTickets[existingIndex] = compAddOn;
          } else {
            // Add new comp add-on if no match found
            cartAddOnSelectionsWithCompTickets.push(compAddOn);
          }
        });

        dispatch(
          actions.checkout.setAddOns({
            addOns: cartAddOnSelectionsWithCompTickets,
            dateTime: shopCartDateTime,
            flow,
          }),
        );
        newRelic.logCartAction(
          'AddToCartButton - onClick',
          'setAddOnsWithCompTickets',
          {
            cartAddOnSelectionsWithCompTickets,
            shopCartDateTime,
            flow,
          },
        );
      } else {
        dispatch(
          actions.checkout.setAddOns({
            addOns: cartAddOnSelections,
            dateTime: shopCartDateTime,
            flow,
          }),
        );
        newRelic.logCartAction('AddToCartButton - onClick', 'setAddOns', {
          cartAddOnSelections,
          shopCartDateTime,
          flow,
        });
      }

      // Finalize countdown in local storage
      const lsCart = getObjectForLocalStorageKey<CheckoutType>(
        CART_LOCAL_STORAGE_KEY,
      );

      if (lsCart) {
        setObjectForLocalStorageKey<CheckoutType>(CART_LOCAL_STORAGE_KEY, {
          ...lsCart,
          counterEndTime,
        });
      }
    }

    // Attributes, if exists
    if (attributes && Object.values(attributes).length > 0) {
      dispatch(actions.checkout.setAttributes(attributes));
    }

    setShowLoadingModal(false);

    // Show modal
    setCartShowModal(true);

    sendGA4Event();
  };

  const onModalClose = () => {
    setCartShowModal(false);
  };

  if (!window || !document) return null;

  return (
    <Flex
      sx={{
        justifyContent: 'flex-end',
      }}
    >
      <Button
        data-testid="add-to-cart-button"
        disabled={disabled || isLoading}
        id="add-to-cart-button"
        onClick={onClick}
        sx={{
          maxWidth: '500px',
          width: '100%',
          cursor: disabled || isLoading ? 'not-allowed' : 'pointer',
        }}
        variant="inverted"
      >
        <ButtonText
          editMode={editMode}
          isAddOn={isAddOn}
          isLoading={isLoading}
        />
      </Button>
      {createPortal(
        <Modal id="add-to-cart-modal" isOpen={showCartModal}>
          <CartModal closeModal={onModalClose} />
        </Modal>,
        document.body,
      )}
      {showLoadingModal && <ModalLoader />}
    </Flex>
  );
};
