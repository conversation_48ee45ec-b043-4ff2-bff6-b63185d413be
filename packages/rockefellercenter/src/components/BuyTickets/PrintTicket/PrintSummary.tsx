/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  Flex,
  Box,
  Text,
  getThemeByName,
  ThemeProvider,
  useThemeUI,
} from '@tishman/components';
import * as Icons from '@tishman/icons';
import QRCode from 'react-qr-code';
import React, { Fragment, useMemo } from 'react';

import {
  formatDateTime,
  formatTimeRangeDisplay,
  formatTimeToNearestHalfHour,
} from '../utils';
import { putInArray } from '../../../services/viva/utils';
import { selectShopCartDateTime, useAppSelector } from '../../../store';
import {
  ADDONS_THAT_DONT_SHOW_DATE,
  ADDONS_THAT_DONT_SHOW_TIME,
  BEAM_AKS,
  BEAM_HIDDEN_EVENTS,
  BosEvent,
  ON_THE_ROCKS_PRODUCT_AK,
  SKYLIFT_AKS,
  SKYLIFT_HIDDEN_EVENTS,
} from '../../../services/viva/constants';

import type {
  ITEM,
  OrderResponse,
} from '../../../services/viva/types/response/order';
import type { TicketPropsTicket } from './SingleTicket';

interface SummaryProps {
  ticket: TicketPropsTicket;
  order: OrderResponse;
}

const shouldRenderAddOn = (item: ITEM, hiddenEvents: string[]) => {
  const productEventAk = item?.ITEMPERFORMANCELIST?.ITEMPERFORMANCE?.EVENTAK;

  if (!productEventAk) {
    return true;
  }

  return !hiddenEvents.includes(productEventAk as BosEvent);
};

const shouldRenderBeam = (item: ITEM) => {
  return shouldRenderAddOn(item, BEAM_HIDDEN_EVENTS);
};

const shouldRenderSkylft = (item: ITEM) => {
  return shouldRenderAddOn(item, SKYLIFT_HIDDEN_EVENTS);
};

const checkIfShouldShowTime = (
  ticket: TicketPropsTicket,
  order: OrderResponse,
) => {
  const isBeam = BEAM_AKS.includes(ticket.item.PRODUCT.AK);
  const isSkylift = SKYLIFT_AKS.includes(ticket.item.PRODUCT.AK);

  if (!isBeam && !isSkylift) {
    return !ADDONS_THAT_DONT_SHOW_TIME.includes(ticket.item?.PRODUCT.AK);
  }

  if (isBeam) {
    return shouldRenderBeam(putInArray(order.ITEMLIST.ITEM)?.[0]);
  }

  if (isSkylift) {
    return shouldRenderSkylft(putInArray(order.ITEMLIST.ITEM)?.[0]);
  }

  return true;
};

export const PrintSummary = ({
  order,
  ticket,
}: SummaryProps): React.JSX.Element => {
  const dateTime = useAppSelector(selectShopCartDateTime);
  const themeUI = useThemeUI();

  const ticketDateTime = useMemo(() => {
    const item = ticket.item;

    const shouldShowTime = checkIfShouldShowTime(ticket, order);

    const isCityPass = item?.PRODUCT?.AK === 'TOR.EVN1.MCC737';
    const isOnTheRocks = item?.PRODUCT?.AK === ON_THE_ROCKS_PRODUCT_AK;

    if (isCityPass) {
      const { date, time } = formatDateTime(dateTime.toString());

      return [
        { date, time: shouldShowTime ? time : null, ak: item?.PRODUCT.AK },
      ];
    }

    const performanceTimes = putInArray(
      item?.ITEMPERFORMANCELIST?.ITEMPERFORMANCE,
    )
      .filter(Boolean) // remove processing fee kind of items don't that have ITEMPERFORMANCE
      .map((performance) => {
        const { date, time } = formatDateTime(performance.DATETIME);
        return {
          date: ADDONS_THAT_DONT_SHOW_DATE.includes(item?.PRODUCT.AK)
            ? null
            : date,
          time: shouldShowTime
            ? isOnTheRocks
              ? `${time} - ${formatTimeToNearestHalfHour(
                  performance.ENDDATETIME,
                )}`
              : time
            : null,
          ak: item?.PRODUCT,
        };
      });

    return performanceTimes;
  }, [dateTime, order, ticket]);

  const getTicketName = (name: string) => {
    if (name === 'Season Member') return 'Season Pass';
    if (name === 'Bar Cafe Item') return 'Champagne Toast';

    return name;
  };

  const ticketName = getTicketName(ticket.item.PRODUCT.NAME);

  return (
    <Box
      sx={{
        'borderRight': ['none', null, 'dashed 1px #333'],
        'padding': [2, 4, 6],
        '@media print': {
          border: 'none',
          paddingTop: 2,
        },
      }}
    >
      <Box sx={{ width: '100%' }}>
        <Text
          sx={{
            paddingBottom: 3,
            borderBottom: 'solid 1px #777',
            marginBottom: 3,
          }}
          variant="mediumTitle"
        >
          Your Ticket Summary
        </Text>
        <Flex
          sx={{
            'width': '100%',
            'justifyContent': 'space-between',
            '@media print': {
              justifyContent: 'space-around',
            },
          }}
        >
          <Box data-testid="print-summary-ticket">
            <Box
              sx={{
                height: 40,
              }}
            >
              <ThemeProvider theme={getThemeByName('Rock Center Lavender')}>
                <Icons.TicketIcon
                  style={{
                    height: 30,
                    width: 30,
                    color: themeUI.theme.colors.background,
                  }}
                />
              </ThemeProvider>
            </Box>
            <Text sx={{ fontWeight: 500, marginBottom: 2 }}>Ticket Type</Text>
            <Text>{`1 ${ticketName}`}</Text>
          </Box>
          {ticketDateTime?.[0]?.date && (
            <Box>
              <Box sx={{ height: 40 }}>
                <ThemeProvider theme={getThemeByName('Top of the Rock Olive')}>
                  <Icons.CalendarIcon
                    style={{ fill: themeUI.theme.colors.background }}
                  />
                </ThemeProvider>
              </Box>
              <Text sx={{ fontWeight: 500, marginBottom: 2 }}>Entry Date</Text>
              <Text>{ticketDateTime?.[0]?.date}</Text>
            </Box>
          )}
          {!ticket.dateOnly && ticketDateTime?.[0]?.time && (
            <Box>
              <Box sx={{ height: 40 }}>
                <ThemeProvider theme={getThemeByName('Top of the Rock Yellow')}>
                  <Icons.Clock
                    style={{ fill: themeUI.theme.colors.background }}
                  />
                </ThemeProvider>
              </Box>
              <Text sx={{ fontWeight: 500, marginBottom: 2 }}>Entry Time*</Text>
              {ticketDateTime.map((tdt, idx) => (
                <Text key={`${tdt.date}-${tdt.time}-${idx}`}>{tdt?.time}</Text>
              ))}
            </Box>
          )}
        </Flex>
        <Flex
          sx={{
            'mt': 5,
            'justifyContent': 'center',
            'alignItems': 'center',
            'flexDirection': 'column',
            '@media print': {
              mx: 'auto',
            },
          }}
        >
          {ticket?.USAGERECAP?.MEDIACODE ? (
            <Fragment>
              <Box
                sx={{
                  'maxWidth': '100%',
                  '@media print': {
                    display: 'none',
                  },
                }}
              >
                <QRCode value={ticket.USAGERECAP.MEDIACODE} />
              </Box>
              <Box
                sx={{
                  'display': 'none',
                  '@media print': {
                    mt: '5mm',
                    mb: 0,
                    display: 'block',
                  },
                }}
              >
                <QRCode size={150} value={ticket.USAGERECAP.MEDIACODE} />
              </Box>
              <Text
                sx={{
                  textAlign: 'center',
                  fontWeight: 500,
                  marginTop: 3,
                  position: 'relative',
                }}
              >
                {ticket.USAGERECAP.MEDIACODE}
              </Text>
            </Fragment>
          ) : (
            <Text sx={{ textAlign: 'center', color: 'black' }}>
              No QR code available for this item.
            </Text>
          )}
        </Flex>
      </Box>
    </Box>
  );
};
