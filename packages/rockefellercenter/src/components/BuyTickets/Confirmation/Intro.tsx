/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, Text, Flex, ExternalLink, Link } from '@tishman/components';
import { H } from '@hzdg/sectioning';
import React, { Fragment, useContext } from 'react';
import { getWindow } from '@hzdg/dom-utils';

import { useOrderQuery } from '../../../services/viva';
import { Flow } from '../../../services/viva/constants';
import { ConfirmationPageContext } from '../../../utils/confirmation-page-context-provider';
import { useLocalizedLink } from '../../../utils/use-localized-link';

import { ShareTrip } from './ShareTrip';

interface IntroProps {
  description?: string;
  title?: string;
  knowBeforeYouGo?: readonly (string | undefined)[];
}

export const Intro = ({
  description,
  title,
  knowBeforeYouGo,
}: IntroProps): JSX.Element | null => {
  const window = getWindow();
  const getLocalizedLink = useLocalizedLink();
  const { checkout, flow } = useContext(ConfirmationPageContext);

  const printTicketsLink = getLocalizedLink(
    `/buy-tickets/tickets?${new URLSearchParams([
      ['SaleId', `${checkout.payment?.saleAK}`],
      ['Flow', window?.btoa(flow) ?? ''],
    ])}`,
  );

  const orderQuery = useOrderQuery({
    orderAK: checkout.payment.saleAK,
    flow,
  });

  if (!checkout || !checkout.payment) return null;

  return orderQuery.isSuccess && orderQuery.data ? (
    <Box>
      <H
        sx={{
          maxWidth: [198, 385],
          mb: 3,
          variant: 'text.buyTicketsConfirmationTitle',
        }}
      >
        {title}
      </H>
      {orderQuery.data.SALE.RESERVATIONAK && (
        <Text
          sx={{
            variant: 'text.ticketPrice',
            pt: [0, 2],
            mb: [2, 3],
          }}
        >
          {`Confirmation #${orderQuery.data.SALE.RESERVATIONAK}`}
        </Text>
      )}
      <Text sx={{ variant: 'text.smallP', maxWidth: 380, mb: 2 }}>
        {description}
      </Text>
      {!checkout.isRefund && (
        <Flex sx={{ pt: 3, flexWrap: 'wrap' }}>
          {/* {flow === Flow.CITY_PASS_REDEMPTION ? (
            <Link
              data-testid="print-tickets-button"
              href={printTicketsLink}
              sx={{
                variant: 'links.button',
                width: ['100%', 'initial'],
                textAlign: ['center', 'left'],
                mr: [0, 4],
                height: '100%',
              }}
            >
              Print Ticket
            </Link>
          ) : (
            <ExternalLink
              data-testid="print-tickets-button"
              href={printTicketsLink}
              sx={{
                variant: 'links.button',
                width: ['100%', 'initial'],
                textAlign: ['center', 'left'],
                mr: [0, 4],
                height: '100%',
              }}
            >
              Print Ticket
            </ExternalLink>
          )} */}
          <ShareTrip />
        </Flex>
      )}
      {Boolean(knowBeforeYouGo) && (
        <Fragment>
          <Text sx={{ mt: 24 }} variant="smallTitle">
            Know Before You Go:
          </Text>
          <Box as="ul" sx={{ listStyle: 'disc', ml: 16, mt: 12 }}>
            {(knowBeforeYouGo ?? []).map((copy, i) => (
              <li key={i}>{copy}</li>
            ))}
          </Box>
        </Fragment>
      )}
    </Box>
  ) : null;
};
