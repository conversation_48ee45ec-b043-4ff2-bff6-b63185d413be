/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Text, Flex } from '@tishman/components';
import React, { useEffect, useMemo, useState } from 'react';

import {
  actions,
  selectCheckoutCart,
  selectCheckoutCartAddonSelections,
  useAppDispatch,
  useAppSelector,
} from '../../../../store';
import { ModalContainer } from '../ModalContainer';
import {
  Translatable,
  useTranslations,
} from '../../../../utils/use-translations';
import { CheckoutCountdown } from '../../CheckoutCountdown';
import { COMP_TICKETS, Flow } from '../../../../services/viva/constants';
import { safeDataLayerPush } from '../../utils';

import { TicketGroup } from './TicketGroup';
import { Ticket } from './Ticket';
import { CartFooter } from './Footer';

import type { ShopCartSelection } from '../../../../store/types';
import type { CartContentTranslationsForLocale } from '../../../../data/translations/cartContent';

interface BuyTicketsModal {
  closeModal?: () => void;
}

export const CartModal = ({ closeModal }: BuyTicketsModal) => {
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const translations = useTranslations<CartContentTranslationsForLocale>(
    Translatable.CartContent,
  );
  const shopCart = useAppSelector(selectCheckoutCart);
  const addOnSelections = useAppSelector(selectCheckoutCartAddonSelections);
  const dispatch = useAppDispatch();

  // If it's not the initial load, push the dataLayer event
  useEffect(() => {
    if (!isInitialLoad) return;

    safeDataLayerPush({
      event: 'modal_view_cart',
    });

    setIsInitialLoad(false);
  }, [isInitialLoad, setIsInitialLoad]);

  // Gather all selections by flow
  const shopCartSelections = useMemo(() => {
    if (!shopCart?.selections) return {};
    const _selections: { [key in Flow]?: ShopCartSelection[] } = {};

    for (const key in shopCart.selections) {
      const selection = shopCart.selections[key];
      // Only add selections that have a length
      if (selection?.length) {
        _selections[key as Flow] = selection;
      }
    }

    return _selections;
  }, [shopCart]);

  const handleCloseModel = () => {
    closeModal?.();

    dispatch(actions.checkout.reset());
  };

  if (!translations) return null;

  return (
    <ModalContainer
      closeModal={handleCloseModel}
      scrollable={false}
      style={{
        maxHeight: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Flex
        id="cart-modal"
        sx={{
          flex: ['0 1 calc(100dvh - 230px)'],
          rowGap: ['24px'],
          flexDirection: 'column',
          px: ['24px'],
          py: ['64px'],
          overflow: 'scroll',
          transition: 'flex 0.3s',
        }}
      >
        {/* TITLE & TIMER */}
        <Flex
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: '2px',
          }}
        >
          <Text
            sx={{
              fontSize: ['28px', '32px'],
              fontWeight: 500,
            }}
          >
            {translations.title}
          </Text>
          <CheckoutCountdown showDescription={false} />
        </Flex>
        {/* CART ITEMS */}
        {Object.keys(shopCartSelections).map((_key, i) => {
          const key = _key as Flow;
          return (
            <TicketGroup key={i} translations={translations?.byFlow[key]}>
              {shopCartSelections[key]?.map((selection, i) => (
                <Ticket
                  flow={key}
                  key={`key-${i}`}
                  selection={selection}
                  translations={translations?.byFlow[key]}
                />
              ))}
              {addOnSelections[key] &&
                Object.keys(addOnSelections[key]).map((dateTime) => {
                  const selections = addOnSelections[key]?.[dateTime];

                  if (!selections.length) return null;

                  return selections
                    .filter((sel) => !COMP_TICKETS.includes(sel.statGroup))
                    .map((selection, i) => (
                      <Ticket
                        dateTime={Number(dateTime)}
                        flow={key}
                        isAddOn
                        key={`addon-${i}`}
                        selection={selection}
                        translations={translations?.addOns}
                      />
                    ));
                })}
            </TicketGroup>
          );
        })}
      </Flex>
      <CartFooter />
    </ModalContainer>
  );
};
