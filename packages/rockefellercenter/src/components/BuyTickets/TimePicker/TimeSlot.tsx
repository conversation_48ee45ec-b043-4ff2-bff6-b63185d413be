/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Button, Text } from '@tishman/components';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';

import {
  selectFirstTimeSlot,
  selectFlow,
  selectTotalQuantity,
  useAppSelector,
  useAppDispatch,
} from '../../../store';
import { Flow, checkAdaptivePricing } from '../../../services/viva/constants';
import { PriceToLabelsMap } from '../../price-to-labels-map';
import { useScrollToTimeSlot } from '../../../utils/use-scroll-to-time-slot';
import { useGetAvailableTimeSlot } from '../../../utils/use-get-available-time-slot';
import { BuyTicketsLayoutContext } from '../../../layouts/BuyTicketsLayout';
import { useSelectTimeSlot } from '../../../utils/use-select-time-slot';
import { newRelic } from '../../../utils/new-relic';

import type { Availability } from '../SegmentedTimePicker/Availabilities';
import { formatTimeRangeDisplay, formatTimeToNearestHalfHour } from '..';
import { putInArray } from '../../../services/viva/utils';

type Props = {
  data: Availability;
  setLocalPerformanceAK: React.Dispatch<React.SetStateAction<string>>;
};

export const TimeSlot = ({ data: timeSlot, setLocalPerformanceAK }: Props) => {
  const dispatch = useAppDispatch();
  const selectedTimeSlot = useAppSelector(selectFirstTimeSlot);
  const flow = useAppSelector(selectFlow);
  const checkoutTotalQuantity = useAppSelector(selectTotalQuantity);
  const { scrollToSlot } = useScrollToTimeSlot();
  const { getTimePeriodByTimeSlot } = useGetAvailableTimeSlot();
  const { setSelectedTimePeriod, isTimeSlotsAnimating } = useContext(
    BuyTicketsLayoutContext,
  );

  const hasAdaptivePricing = checkAdaptivePricing(flow);
  const timeSlotRef = useRef<HTMLButtonElement | null>(null);
  const selectedTimeSlotAK = useRef<string | null>(null);
  const selectTimeSlot = useSelectTimeSlot(setLocalPerformanceAK);

  // Trying to avoid re-renders and multiple new IntersectionObserver calls on
  // isTimeSlotsAnimating by abstracting this callback
  const observerCallback = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      if (isTimeSlotsAnimating) return;

      if (entries[0].isIntersecting && !isTimeSlotsAnimating) {
        const timePeriod = getTimePeriodByTimeSlot(timeSlot.startRaw.hours);
        setSelectedTimePeriod(timePeriod);
      }
    },
    [isTimeSlotsAnimating],
  );

  // On initial render, watch for time slot to be visible in viewport
  // If true, update the tab menu if necessary
  useEffect(() => {
    if (!timeSlotRef.current) return;
    const parentEl = timeSlotRef.current.parentElement;

    const observer = new IntersectionObserver(observerCallback, {
      root: parentEl,
      rootMargin: '0px 0px -20% 0px',
    });

    observer.observe(timeSlotRef.current);

    return () => {
      observer.disconnect();
    };
  }, [observerCallback]);

  // Update the selected time slot and scroll to it if necessary
  useEffect(() => {
    if (
      !selectedTimeSlot ||
      !timeSlot.ak ||
      selectedTimeSlot.ak !== timeSlot.ak
    )
      return;

    if (
      selectedTimeSlotAK.current !== selectedTimeSlot.ak &&
      timeSlotRef.current
    ) {
      selectedTimeSlotAK.current = timeSlot.ak;

      scrollToSlot(timeSlotRef.current, true);
    }
  }, [scrollToSlot, selectedTimeSlot, timeSlot.ak]);

  // Hours value calculation
  const hours = useMemo(() => {
    return timeSlot.startRaw.hours % 12 === 0
      ? 12
      : timeSlot.startRaw.hours % 12;
  }, [timeSlot.startRaw.hours]);

  const isSelected = selectedTimeSlot?.ak === timeSlot.ak;
  const timeSlotAriaLabel = `${isSelected ? 'Selected' : 'Select'}  ${hours}:${
    timeSlot.startRaw.minutes
  } ${timeSlot.startRaw.meridian}`;

  // Price table for variable pricing
  const adaptedPricing = useMemo(() => {
    if (!hasAdaptivePricing) return null;
    return <PriceToLabelsMap flow={flow} timeSlot={timeSlot} />;
  }, [hasAdaptivePricing, timeSlot]);

  const timeSlotTestId = `time-slot-${hours}-${
    timeSlot.startRaw.minutes
  }-${timeSlot.startRaw.meridian.toLowerCase()}`;
  return (
    <Button
      aria-label={timeSlotAriaLabel}
      aria-selected={isSelected}
      data-testid={timeSlotTestId}
      disabled={Number(timeSlot.availability) < checkoutTotalQuantity}
      id={timeSlot.ak}
      key={timeSlot.timeRaw}
      onClick={() => {
        selectTimeSlot(timeSlot);
        newRelic.logCartAction('TimeSlot', 'onClick', {
          selectTimeSlot: timeSlot,
        });
      }}
      ref={timeSlotRef}
      role="option"
      sx={{
        'alignItems': 'center',
        'borderBottom': '1px solid black',
        'borderLeft': '1px solid black',
        'borderRight': '1px solid black',
        'cursor': 'pointer',
        'display': 'grid',
        'gridTemplateColumns': hasAdaptivePricing ? '1fr 4fr' : '1fr',
        'fontWeight': 'medium',
        'gap': '12px',
        'paddingX': hasAdaptivePricing ? [2, 4] : [2, 3],
        'paddingY': 2,
        'width': '100%',
        'transition':
          'background-color 0.15s ease-in-out, color 0.15s ease-in-out',
        // Sold out style
        ...(Number(timeSlot.availability) >= checkoutTotalQuantity
          ? {}
          : {
              opacity: '0.65',
              backgroundColor: 'rgb(0 0 0 / 15%)',
              pointerEvents: 'none',
            }),
        '&[aria-selected="true"], &:hover': {
          '#fireworks-icon': {
            svg: {
              '& path:first-of-type': {
                fill: '#FFD759',
              },
              '& path[stroke-width="1.385"]': {
                stroke: '#FFD759',
              },
            },
          },
          '#sunset-icon': {
            svg: {
              path: { stroke: '#FAAF5E' },
            },
          },
        },
        '@media (hover: hover) and (pointer: fine)': {
          '&:active, &:hover': {
            span: {
              color: 'background',
            },
            svg: {
              color: 'background',
            },
            div: {
              color: 'background',
            },
            backgroundColor: 'text',
          },
        },
        'span': {
          textWrap: 'nowrap',
        },
        ...(selectedTimeSlot?.ak === timeSlot.ak
          ? {
              span: {
                color: 'background',
                textWrap: 'nowrap',
              },
              backgroundColor: 'text',
              svg: {
                color: 'background',
              },
              div: {
                color: 'background',
              },
            }
          : {}),
      }}
      variant="tab"
    >
      {/* TIME */}
      <Text
        as="span"
        sx={{
          fontSize: [1, 2],
          fontWeight: 'medium',
          ml: [1, 0],
          textAlign: hasAdaptivePricing ? 'left' : 'center',
          lineHeight: '1',
        }}
      >
        {formatTimeRangeDisplay(
          putInArray(timeSlot),
          0,
          flow === Flow.ON_THE_ROCKS,
        )}
      </Text>
      {/* PRICE */}
      {Number(timeSlot.availability) >= checkoutTotalQuantity ? (
        adaptedPricing
      ) : (
        <Text
          sx={{
            position: 'absolute',
            right: [2, 3],
            fontStyle: 'italic',
            fontSize: 2,
          }}
        >
          Sold Out
        </Text>
      )}
    </Button>
  );
};
