/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Flex, Text } from '@tishman/components';
import React, { useCallback, useContext, useEffect, useMemo } from 'react';

import {
  Locale,
  formatMoney,
  formatPriceRange,
  getLocalStoragePreferredLocale,
} from '../../../utils';
import {
  actions,
  selectFlow,
  selectHasChaperone,
  selectHasChild,
  selectHasToddler,
  selectShopCartSelections,
  selectTotalQuantity,
  useAppDispatch,
  useAppSelector,
  selectFirstTimeSlot,
} from '../../../store';
import { QuantityPicker } from '../QuantityPicker';
import {
  Flow,
  StatGroup,
  TicketLimit,
  AllInPassPriceRanges,
  TorGaPriceRanges,
  RinkPriceRanges,
  RinkPremiumPriceRanges,
  TheBeamPackagePriceRanges,
  RinkChaletPriceRanges,
  FLOW_PRODUCT_TITLES_BY_AK,
} from '../../../services/viva/constants';
import { BuyTicketsLayoutContext } from '../../../layouts/BuyTicketsLayout';
import { newRelic } from '../../../utils/new-relic';
import { safeDataLayerPush } from '../utils';

import type { TicketLineProps } from './NewTicketLineTypes';

const getTicketLimit = (flow: Flow | null): number => {
  if (flow === null) return 0;
  return TicketLimit[flow as keyof typeof TicketLimit] || 0;
};

export const NewTicketLine = ({
  hideLabel,
  label,
  helpLabel,
  tax,
  price,
  pricetableAK,
  priceRange,
  statGroup,
  productAK,
  type,
}: TicketLineProps): React.JSX.Element => {
  const dispatch = useAppDispatch();
  const { isLoading } = useContext(BuyTicketsLayoutContext);
  const flow = useAppSelector(selectFlow);
  const isRinkMembershipFlow = flow === Flow.RINK_MEMBERSHIP;
  const selectedTimeSlot = useAppSelector(selectFirstTimeSlot);

  // ticket limits may vary for every attraction. Please check if this needs to be set in the backend.
  const shopCartSelections = useAppSelector(selectShopCartSelections);
  const totalQuantity = useAppSelector(selectTotalQuantity);

  const ticketLimit = getTicketLimit(flow);

  const quantityMaxedOut = useMemo(
    () => totalQuantity === ticketLimit,
    [ticketLimit, totalQuantity],
  );

  const hasChaperone = useAppSelector(selectHasChaperone);
  const hasChild = useAppSelector(selectHasChild);
  const hasToddler = useAppSelector(selectHasToddler);
  const hasChildOrToddler = hasChild || hasToddler;

  const selectedTicket = useMemo(
    () =>
      shopCartSelections.find(
        (selectedTicket) => selectedTicket.type === type,
      ) ?? { count: 0, productAK, label, statGroup, type },
    [shopCartSelections, productAK, label, statGroup, type],
  );

  const isChaperoneTicket = selectedTicket.statGroup.includes(
    StatGroup.RINK_CHAPERONE,
  );

  /* Handlers */
  const handleIncrease = useCallback(() => {
    if (quantityMaxedOut && selectedTicket.label !== 'Chaperone') {
      const error = new Error(
        `Unfortunately, you cannot buy more than ${ticketLimit} tickets in a single transaction. Please call our Group Sales department at ************ or 877.NYC.ROCK (************) to purchase tickets for groups of ${
          ticketLimit + 1
        } or more.`,
      );
      error.name = "Sorry, you've exceed the ticket limit";
      newRelic.logCartAction(
        `NewTicketLine - handleIncrease Error: ${error.name}`,
        'handleIncrease',
        { ticket: selectedTicket.label, count: selectedTicket.count },
      );
      // handleError(error);
      return;
    }
    // Only one chaperone per checkout
    const count =
      selectedTicket.label === 'Chaperone' && selectedTicket.count >= 1
        ? 1
        : selectedTicket.count + 1;

    newRelic.logCartAction(
      'NewTicketLine - Increase quantity',
      'handleIncrease',
      { ticket: selectedTicket.label, count },
    );

    // Add GA4 event for quantity change
    safeDataLayerPush({
      event: 'select_item',
      currency: 'USD',
      value: (price / 100).toFixed(2),
      items: [
        {
          item_id: productAK,
          item_name: label,
          item_category: 'Tickets',
          price: (price / 100).toFixed(2),
          quantity: count,
          discount: '0.00',
          variant: selectedTimeSlot?.isSunset
            ? 'sunset'
            : selectedTimeSlot?.isDiscounted
            ? 'evening'
            : undefined,
        },
      ],
    });

    if (productAK) {
      dispatch(
        actions.order[
          isRinkMembershipFlow
            ? 'setShopCartSelectionsByTicketTypeByPush'
            : 'setShopCartSelections'
        ]({
          count,
          label,
          price,
          priceRange: type === 'chaperone' ? { high: 0, low: 0 } : priceRange,
          pricetableAK,
          productAK,
          statGroup,
          tax,
          type,
        }),
      );
    }
  }, [
    dispatch,
    // handleError,
    label,
    price,
    pricetableAK,
    priceRange,
    productAK,
    quantityMaxedOut,
    selectedTicket.count,
    selectedTicket.label,
    statGroup,
    tax,
    ticketLimit,
    type,
    isRinkMembershipFlow,
    selectedTimeSlot,
  ]);

  const handleDecrease = useCallback(() => {
    if (selectedTicket.count > 0) {
      newRelic.logCartAction(
        'NewTicketLine - Decrease quantity',
        'handleDecrease',
        { ticket: selectedTicket.label, count: selectedTicket.count - 1 },
      );
    }

    const count = selectedTicket.count === 0 ? 0 : selectedTicket.count - 1;

    if (productAK) {
      dispatch(
        actions.order.setShopCartSelections({
          statGroup,
          count,
          label,
          tax,
          price,
          pricetableAK,
          priceRange,
          productAK,
          type,
        }),
      );
    }
  }, [
    dispatch,
    label,
    price,
    pricetableAK,
    priceRange,
    productAK,
    selectedTicket.count,
    statGroup,
    tax,
    type,
  ]);

  useEffect(() => {
    if (hasChaperone && !hasChildOrToddler) {
      dispatch(
        actions.order.setShopCartSelections({
          statGroup: [StatGroup.RINK_CHAPERONE],
          label: '',
          price: 0,
          tax: 0,
          priceRange: { high: 0, low: 0 },
          pricetableAK: '',
          productAK: '',
          count: 0,
          type: 'chaperone',
        }),
      );
    }
  }, [dispatch, hasChaperone, hasChildOrToddler, selectedTicket]);

  const formattedLabel = useMemo(() => {
    switch (flow) {
      case Flow.RINK_MEMBERSHIP:
        // TODO: Translate
        return 'Season Pass';
      case Flow.CITY_PASS:
        return label.replace(/.*(Adult|Child).*/i, '$1');
      case Flow.ALL_IN_PASS:
      case Flow.ON_THE_ROCKS:
        return FLOW_PRODUCT_TITLES_BY_AK[flow]?.[productAK] ?? label;
      default:
        return label;
    }
  }, [label, flow]);

  const getPriceText = () => {
    if (flow === Flow.RINK) {
      return formatPriceRange(
        RinkPriceRanges[type].high,
        RinkPriceRanges[type].low,
      );
    }
    if (flow === Flow.RINK_VIP) {
      return formatPriceRange(
        RinkPremiumPriceRanges.high,
        RinkPremiumPriceRanges.low,
      );
    }
    if (flow === Flow.CHALETS) {
      return formatPriceRange(
        RinkChaletPriceRanges.high,
        RinkChaletPriceRanges.low,
      );
    }
    if (flow === Flow.TOR_EXPRESS) {
      return formatPriceRange(
        RinkChaletPriceRanges.high,
        RinkChaletPriceRanges.low,
      );
    }
    if (isRinkMembershipFlow) return formatMoney(price);
    if (isChaperoneTicket) return formatMoney(0);
    if (flow === Flow.TOR_GA) {
      return formatPriceRange(
        TorGaPriceRanges[type].high,
        TorGaPriceRanges[type].low,
      );
    }
    if (flow === Flow.THE_BEAM_PASS) {
      return formatPriceRange(
        TheBeamPackagePriceRanges.high,
        TheBeamPackagePriceRanges.low,
      );
    }
    if (flow === Flow.ALL_IN_PASS) {
      return formatPriceRange(
        AllInPassPriceRanges[type].high,
        AllInPassPriceRanges[type].low,
      );
    }

    return formatPriceRange(priceRange.high ?? 0, priceRange.low ?? 0);
  };

  const locale = getLocalStoragePreferredLocale();

  const requiresLocaleStyle = locale && [Locale.DE_DE].includes(locale);

  return (
    <Flex
      data-testid={`ticket-line-${type}`}
      sx={{
        'flexDirection': 'row',
        'justifyContent': 'space-between',
        'columnGap': ['10px', '58px'],
        'alignItems': ['center', 'baseline'],
        '&:not(:last-child)': {
          mb: [4, 3],
        },
      }}
    >
      {!hideLabel && (
        <Flex sx={{ flexDirection: 'column', mb: [0] }}>
          <Flex
            sx={{
              alignItems: 'baseline',
              flexWrap: 'wrap',
              flexDirection: [isRinkMembershipFlow ? 'column' : 'row', 'row'],
            }}
          >
            <Text
              sx={{
                minWidth: 'min-content',
                marginRight: '0.5em',
                fontSize: requiresLocaleStyle ? [4, 6] : [5, 6],
              }}
              variant="ticketsHeading"
            >
              {formattedLabel}
            </Text>
            <Text
              sx={{
                variant: 'text.ticketsPriceMedium',
                fontWeight: 500,
              }}
            >
              {getPriceText()}
            </Text>
          </Flex>
          {helpLabel && flow !== Flow.CITY_PASS && <Text>{helpLabel}</Text>}
        </Flex>
      )}
      <QuantityPicker
        addButtonBorderRadiusVariant={hideLabel ? 'big' : 'small'}
        count={
          isRinkMembershipFlow
            ? shopCartSelections.length
            : selectedTicket.count
        }
        disabled={quantityMaxedOut || isLoading}
        isSingleChecked={hasChaperone && hasChildOrToddler}
        isSingleDisabled={!hasChildOrToddler}
        isSingleTicket={isChaperoneTicket}
        onDecrement={handleDecrease}
        onIncrement={handleIncrease}
        wrapperStyles={{
          alignSelf: [
            isRinkMembershipFlow ? 'flex-start' : 'center',
            'flex-start',
          ],
          marginTop: [0, 2],
        }}
      />
    </Flex>
  );
};
