import React, { useCallback, useMemo, useState } from 'react';

import { PHOTO_PKG_AK, StatGroup } from '../../../services/viva/constants';
import {
  actions,
  selectFlow,
  selectShopCart,
  useAppDispatch,
  useAppSelector,
} from '../../../store';
import { newRelic } from '../../../utils/new-relic';

import type { AddOnItem } from '../AddOns';
import type { IAddOnLineItem } from './types';

export const useAddOnLineItem = (props: IAddOnLineItem) => {
  const [checked, setChecked] = useState(true);
  const flow = useAppSelector(selectFlow);
  const availablePerformancesHash =
    useAppSelector((state) => state.order.shopCart.addOnsPerformancesHash) ??
    {};

  const {
    ak,
    description: propDescription,
    price,
    marketingText,
    name: propName,
    ga4ItemName,
    canIncrement = true,
    statGroup,
    image: propImage,
  } = props;

  // Use the provided props or fallback to defaults
  const description = propDescription || '';
  const name = propName || '';
  const image = propImage || null;

  const dispatch = useAppDispatch();
  let maxQuantity = props.maxQuantity || 10;
  const isPhotoPass = ak === PHOTO_PKG_AK;

  // Only 1 Photo Pass can be added per order
  if (isPhotoPass) {
    maxQuantity = 1;
  }

  const shopCart = useAppSelector(selectShopCart);

  const shopCartAddOnItem = useMemo(() => {
    return shopCart.addOnSelections.find((i) => i.ak === ak);
  }, [ak, shopCart.addOnSelections]);

  const availableCapacity =
    availablePerformancesHash[shopCartAddOnItem?.ak ?? '']?.available;

  // Initial quantity of the add-on item,
  // which is either the provided initial quantity for skate rental or 0 if not specified
  const initialQuantity =
    statGroup === StatGroup.SKATE_RENTAL &&
    availableCapacity > 0 &&
    availableCapacity >= (props.initialQuantity ?? 0)
      ? props.initialQuantity
      : 0;

  // How many items can be added at once,
  // depending on whether it's allowed or the maximum limit is reached
  const quantityDelta = canIncrement ? 1 : maxQuantity;

  // Decrement the quantity of an add-on item
  const handleDecrement = useCallback(() => {
    if (
      canIncrement &&
      shopCartAddOnItem &&
      Number(shopCartAddOnItem.quantity) > 0
    ) {
      const updatedQuantity =
        Number(shopCartAddOnItem.quantity) - quantityDelta;

      const addOnItemDTO: AddOnItem = {
        ...shopCartAddOnItem,
        quantity: updatedQuantity,
      };

      dispatch(actions.order.setShopCartAddOnSelections([addOnItemDTO]));

      newRelic.logCartAction(
        'AddOnLineItem - quantity decrement',
        'handleDecrement',
        {
          addOnItemDTO,
        },
      );
    } else {
      setChecked(false);
      dispatch(actions.order.decrementUpsellCount(quantityDelta));
      newRelic.logCartAction(
        'AddOnLineItem - upsell decrement',
        'handleDecrement',
        {
          quantityDelta,
        },
      );
    }
  }, [canIncrement, dispatch, quantityDelta, shopCartAddOnItem]);

  // Increment the quantity of an add-on item
  const handleIncrement = useCallback(() => {
    // Check if increment is allowed and the add-on item exists
    if (
      canIncrement &&
      shopCartAddOnItem &&
      Number(shopCartAddOnItem.quantity) < maxQuantity &&
      Number(shopCartAddOnItem.quantity) <
        availablePerformancesHash[shopCartAddOnItem.ak]?.available
    ) {
      // Calculate the updated quantity
      const updatedQuantity =
        Number(shopCartAddOnItem.quantity) + quantityDelta;

      // Create a new add-on item object with updated quantity
      const addOnItemDTO: AddOnItem = {
        ...shopCartAddOnItem,
        isAddingOn: true,
        quantity: updatedQuantity,
        flow,
      };

      // Update the shop cart with the new add-on item
      dispatch(actions.order.setShopCartAddOnSelections([addOnItemDTO]));
      newRelic.logCartAction(
        'AddOnLineItem - quantity increment',
        'handleIncrement',
        {
          addOnItemDTO,
        },
      );
    } else {
      // If increment is not allowed, set checked state to true and increment upsell count
      setChecked(true);
      dispatch(actions.order.incrementUpsellCount(quantityDelta));
      newRelic.logCartAction(
        'AddOnLineItem - upsell increment',
        'handleIncrement',
        {
          quantityDelta,
        },
      );
    }
  }, [canIncrement, dispatch, maxQuantity, quantityDelta, shopCartAddOnItem]);

  const onCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.target.checked ? handleIncrement() : handleDecrement();
  };

  return { handleDecrement, handleIncrement, checked, onCheckboxChange };
};
