/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  Flex,
  Text,
  Checkbox,
  Box,
  useBreakpointIndex,
} from '@tishman/components';
import React, { Fragment } from 'react';
import { StaticImage } from 'gatsby-plugin-image';

import {
  selectFlow,
  selectShopCartAddOnSelections,
  useAppSelector,
} from '../../../store';
import { QuantityPicker } from '../QuantityPicker';
import { getRelevantStatGroup } from '../../../utils/compare-statgroups';
import {
  AddOnItemByStatGroup,
  Flow,
  StatGroup,
} from '../../../services/viva/constants';
import { formatMoney } from '../../../utils';
import addOnMarketingText from '../../../data/translations/addOns';
import ExpandableContent from '../../ExpandableContent';

import {
  descriptionTextStyles,
  itemQuantityContainerStyles,
  checkboxContainerStyles,
  checkboxStyles,
  priceUpsellCountStyles,
} from './styles';
import { useAddOnLineItem } from './useAddOnLineItem';
import { Tag } from './Tag';

import type { IAddOnLineItem } from './types';

const FallbackImage = ({ statGroup }: { statGroup: StatGroup }) => {
  if (statGroup === StatGroup.PHOTO_PACKAGE) {
    return (
      <StaticImage alt="Photo Pass" src="../../../images/photo-pass.jpg" />
    );
  } else if (statGroup === StatGroup.THE_BEAM) {
    return <StaticImage alt="The Beam" src="../../../images/the-beam.jpg" />;
  } else if (statGroup === StatGroup.CHAMPAGNE_TOAST) {
    return (
      <StaticImage
        alt="The NYC Skyline"
        src="../../../images/top-rock-at-night.jpg"
      />
    );
  }

  return null;
};

export const AddOnLineItem = (props: IAddOnLineItem) => {
  const {
    additionalInfo,
    statGroup,
    price,
    canIncrement = true,
    image,
    name,
    description,
    soldOut = false,
    ticketSpecial,
  } = props;
  const flow = useAppSelector(selectFlow);
  const isMobile = useBreakpointIndex() < 2;

  // Use the passed in description if available, otherwise fallback to marketing copy
  const marketingCopy =
    description ||
    (addOnMarketingText.byFlow[flow]?.[statGroup]?.copy ??
      addOnMarketingText.byStatGroup[statGroup]?.copy ??
      '');

  const addOnItems = useAppSelector(selectShopCartAddOnSelections);

  const currentAddOn = addOnItems.find((addOn) => addOn.ak === props.ak);
  const quantity = currentAddOn ? currentAddOn.quantity : 0;

  const { handleIncrement, handleDecrement, checked, onCheckboxChange } =
    useAddOnLineItem(props);

  const isBeamAddOn = getRelevantStatGroup(statGroup) === StatGroup.THE_BEAM;

  // Use the passed in name if available, otherwise fallback to the default title
  const displayTitle = name || AddOnItemByStatGroup[statGroup]?.title;

  return (
    <Flex sx={{ py: 3, mb: 4, flexDirection: ['column', 'row'] }}>
      <Flex
        sx={{
          flex: 1,
          flexWrap: ['wrap', 'wrap', 'nowrap'],
        }}
      >
        {/* TICKET DETAILS */}
        <Flex
          data-stat-group={statGroup}
          data-testid="add-on-line-item-ticket-details"
          sx={{
            borderTop: ['1px solid #D8D8D8', 'none'],
            borderRight: ['1px solid #D8D8D8', 'none'],
            borderLeft: ['1px solid #D8D8D8', 'none'],
            borderRadius: '4px 0 0 0',
            columnGap: 24,
            flexDirection: ['column', 'row'],
            flex: ['1 0 100%', '1 0 100%', '1 1 auto'],
            minWidth: [null, null, null, '760px', '1040px'],
            p: [3, 0],
            rowGap: 3,
          }}
        >
          {/* IMAGE */}
          {/* Some flows do not show an image */}
          {![Flow.RC_TOUR].includes(flow) && (
            <Flex
              sx={{
                columnGap: [3, 0],
                flex: ['0 0 74px', '0 0 267px'],
                flexDirection: ['column', 'row'],
              }}
            >
              <Box
                className="item image"
                sx={{
                  maxHeight: ['100%', '181px'],
                  width: ['100%', '267px'],
                }}
              >
                {image ? (
                  <img
                    alt={displayTitle}
                    src={image}
                    style={{
                      height: '100%',
                      objectFit: 'cover',
                      width: '100%',
                    }}
                  />
                ) : (
                  <FallbackImage statGroup={statGroup} />
                )}
              </Box>
              {/* TITLE, DESCRIPTION, & TAGS */}
              <Flex
                className="item title"
                sx={{
                  display: ['flex', 'none'],
                  pr: [3, 0],
                  py: [3, 0],
                  rowGap: 2,
                  flexDirection: ['row', 'column'],
                  justifyContent: ['space-between', 'flex-start'],
                }}
              >
                <Text sx={descriptionTextStyles}>{displayTitle}</Text>
                {ticketSpecial?.tag && <Tag>{ticketSpecial.tag}</Tag>}
              </Flex>
            </Flex>
          )}
          <Flex
            sx={{
              flexDirection: 'column',
              rowGap: 3,
            }}
          >
            {/* TITLE */}
            <Flex
              className="item title"
              sx={{
                display: [
                  ![Flow.RC_TOUR].includes(flow) ? 'none' : 'flex',
                  'flex',
                ],
                columnGap: 3,
                flexDirection: ['column', null, null, 'row'],
                alignItems: ['flex-start', null, null, 'center'],
              }}
            >
              {/* DESCRIPTION */}
              <Text sx={descriptionTextStyles}>{displayTitle}</Text>
              {/* DESKTOP ONLY PRICE */}
              {!isMobile && (
                <div className="item price">
                  {/* for photo pass, we're hardcoding the discount price to show user */}
                  <Text sx={priceUpsellCountStyles}>
                    {formatMoney(price * 100, undefined)}{' '}
                    {ticketSpecial?.originalPrice && (
                      <Fragment>
                        <s>{ticketSpecial.originalPrice}</s>{' '}
                      </Fragment>
                    )}
                  </Text>
                </div>
              )}
              {/* TAGS */}
              {ticketSpecial?.tag && <Tag>{ticketSpecial.tag}</Tag>}
            </Flex>
            {/* DESCRIPTION */}
            <div className="item description">
              {isMobile ? (
                <ExpandableContent text={marketingCopy} />
              ) : (
                <Text sx={{ whiteSpace: 'pre-line' }}>{marketingCopy}</Text>
              )}
            </div>
            {/* Additional Info */}
            {additionalInfo?.additionalLabel && (
              <ExpandableContent
                label={additionalInfo.additionalLabel}
                showPreview={false}
                text={additionalInfo.additionalText || ''}
              />
            )}
          </Flex>
        </Flex>
        {/* QUANTITY & MOBILE PRICE */}
        <Flex
          sx={{
            border: ['1px solid #D8D8D8', 'none'],
            borderRadius: '0 0 4px 4px',
            justifyContent: 'space-between',
            p: [3, 0],
            width: '100%',
          }}
        >
          {/* MOBILE ONLY PRICE */}
          {isMobile && (
            <Flex
              className="item price"
              sx={{ alignItems: 'baseline', flex: 3 }}
            >
              {/* for photo pass, we're hardcoding the discount price to show user */}
              <Text sx={{ fontSize: 6 }}>
                {formatMoney(price * 100, undefined)}{' '}
                {ticketSpecial?.originalPrice && (
                  <Fragment>
                    <s>{ticketSpecial.originalPrice}</s>{' '}
                  </Fragment>
                )}
              </Text>

              <Text sx={{ fontSize: 5, ml: 2 }}>each</Text>
            </Flex>
          )}
          {/* QUANTITY PICKER */}
          <Box
            as="div"
            className="item quantity"
            data-testid={
              isBeamAddOn
                ? 'beam-addon-quantity-picker'
                : `${statGroup}-addon-quantity-picker`
            }
            sx={itemQuantityContainerStyles}
          >
            {' '}
            {/*
              Sold Out only show if:
                1. The add-on isn't in ADDON_EVENTS_WITH_TIME_CAPACITY or ADDON_EVENTS_WITH_CAPACITY AND
                2. GetDaysAvailabilities returns an empty array (empty DAYLIST)
              If this happens then it is a sign to ask RC to update the performances availability in BOS
            */}
            {soldOut ? (
              <Text sx={{ fontSize: 4, ml: 2 }}>Sold Out</Text>
            ) : canIncrement ? (
              <QuantityPicker
                count={Number(quantity)}
                onDecrement={handleDecrement}
                onIncrement={handleIncrement}
              />
            ) : (
              <Flex sx={checkboxContainerStyles}>
                <Checkbox
                  checked={checked}
                  onChange={onCheckboxChange}
                  sx={checkboxStyles}
                  text=""
                />
              </Flex>
            )}
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};
