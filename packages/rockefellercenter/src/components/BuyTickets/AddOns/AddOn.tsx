/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Flex, Text, Box, SxStyleProp } from '@tishman/components';
import React from 'react';
import { IGatsbyImageData } from 'gatsby-plugin-image';

import defaultImage from '../../../images/top-rock-at-night.jpg';
import photoPassImage from '../../../images/photo-pass.jpg';
import bunnyPhotoImage from '../../../images/bunny-photo.jpg';
import beamImage from '../../../images/the-beam.jpg';
import jupiterImage from '../../../images/restaurant-jupiter.jpg';
import fiveAcresImage from '../../../images/five-acres.jpg';
import beanieImage from '../../../images/beanie.jpg';
import skateRentalImage from '../../../images/skate-rental.jpg';
import treePhotoImage from '../../../images/tree-photo.jpg';
import santaPhotoImage from '../../../images/santa-photo.jpg';
import skyliftImage from '../../../images/skylift.jpg';
import champagneImage from '../../../images/champagne.jpg';
import ornamentImage from '../../../images/ornament.jpg';
import {
  useAppSelector,
  selectTotalQuantity,
  selectNumberOfTicketsWithoutChildrenAndToddlers,
  selectHasMembership,
} from '../../../store';
import { AddOnLineItem } from '../AddOnLineItem';
import { StatGroup, TicketSpecial } from '../../../services/viva/constants';
import { AddOnMembershipLineItem } from '../AddOnMembershipLineItem';

import { styles } from './AddOns.styles';

import type { ThemeUIStyleObject } from 'theme-ui';
import type { AddOnItem } from './types';

type TProps = {
  addOn: AddOnItem;
  available?: number;
  marketingText?: string;
  style?: SxStyleProp;
  maxQuantity?: number;
  additionalInfo?: {
    additionalLabel: string;
    additionalText: string | React.JSX.Element;
  };
  ticketSpecial?: TicketSpecial;
  image?: IGatsbyImageData;
  name?: string;
};

const IMAGES: { [key in StatGroup]?: string } = {
  [StatGroup.PHOTO_PACKAGE]: photoPassImage,
  [StatGroup.THE_BEAM]: beamImage,
  [StatGroup.RESTAURANT_JUPITER]: jupiterImage,
  [StatGroup.FIVE_ACRES]: fiveAcresImage,
  [StatGroup.TREE_PHOTO]: treePhotoImage,
  [StatGroup.ORNAMENT]: ornamentImage,
  [StatGroup.BEANIE]: beanieImage,
  [StatGroup.SKATE_RENTAL]: skateRentalImage,
  [StatGroup.SANTA_PHOTO]: santaPhotoImage,
  [StatGroup.BUNNY_PHOTO]: bunnyPhotoImage,
  [StatGroup.SKYLIFT]: skyliftImage,
  [StatGroup.CHAMPAGNE_TOAST]: champagneImage,
};

export const AddOn = ({
  addOn,
  available,
  maxQuantity,
  marketingText = '',
  style,
  additionalInfo,
  ticketSpecial,
  image,
  name,
}: TProps) => {
  const totalQuantity = useAppSelector(selectTotalQuantity);
  const isMembership = useAppSelector(selectHasMembership);
  const numberOfTicketsWithoutChildrenAndToddlers = useAppSelector(
    selectNumberOfTicketsWithoutChildrenAndToddlers,
  );

  const initialQuantity =
    addOn.statGroup === StatGroup.CHAMPAGNE_TOAST
      ? numberOfTicketsWithoutChildrenAndToddlers
      : totalQuantity;

  return (
    <Flex sx={{ flexDirection: 'column', ...style }}>
      {isMembership ? (
        <Flex sx={styles.membershipContainer as ThemeUIStyleObject}>
          <Text py={3} sx={styles.membershipText}>
            Add rental skates or an overnight locker to your membership.
          </Text>
          <Box sx={styles.membershipBox}>
            <AddOnMembershipLineItem
              ak="1"
              canIncrement={false}
              description="Season Skate Rental"
              initialQuantity={totalQuantity}
              marketingText=""
              maxQuantity={totalQuantity}
              price={15}
              statGroup={addOn.statGroup}
            />
            <AddOnMembershipLineItem
              ak="2"
              canIncrement={false}
              description="Season Locker Rental"
              initialQuantity={totalQuantity}
              marketingText=""
              maxQuantity={totalQuantity}
              price={15}
              statGroup={addOn.statGroup}
            />
          </Box>
        </Flex>
      ) : (
        addOn &&
        addOn.statGroup && (
          <AddOnLineItem
            additionalInfo={additionalInfo}
            ak={addOn.ak}
            description={marketingText}
            ga4ItemName={addOn.name}
            image={image || IMAGES[addOn.statGroup] || defaultImage}
            initialQuantity={initialQuantity}
            key={addOn.ak}
            marketingText={marketingText}
            maxQuantity={maxQuantity ?? totalQuantity}
            name={name || addOn.name}
            price={addOn.price}
            soldOut={available === 0}
            statGroup={addOn.statGroup}
            ticketSpecial={ticketSpecial}
          />
        )
      )}
    </Flex>
  );
};
