import { useEffect, useRef } from 'react';

import { Flow } from '../../services/viva/constants';

// import { Locale } from '../../utils';

import { Availability } from './SegmentedTimePicker/Availabilities';

import type { ITicketOrPackageType, IODTTicketOrPackageType } from '.';

/** Takes the current list of selected ticket types and the
 * list of ODT ticket types, looks up the price and tax, calculates
 * and returns the total.
 */
export function calculatePrice(
  ticketList: ITicketOrPackageType[],
  ticketTypes: IODTTicketOrPackageType[],
): number {
  return ticketList.reduce((total, ticket) => {
    const apiTicket = ticketTypes.find((item) => item.TypeId === ticket.TypeId);

    if (!apiTicket) {
      throw new Error(
        'Could not find matching ticket types in ODT TicketType response',
      );
    }

    return (
      Math.round(
        (ticket.Count * apiTicket.PriceExcludingTax + total + Number.EPSILON) *
          100,
      ) / 100
    );
  }, 0);
}

export const decimalPrice = (value: number): number =>
  Math.round((value + Number.EPSILON) * 100) / 100;

/** Returns the correct ticket title to display for the TicketStep summary
 * Takes the current flow and the ticketCount
 */

export const getTicketDisplayTitle = (
  flow: Flow | null,
  ticketCount: number,
  label: string,
): string => {
  let formattedLabel: string;

  if (label === 'VIP') {
    formattedLabel = 'Premium Skate';
  } else {
    formattedLabel = label
      .replace('New York ', '')
      .replace('City PASS', '')
      .replace('RC Tour Kids and Family', 'Rockefeller Center Tour Jr.')
      .replace('VIP Experience', 'VIP Pass')
      .replace('VIP Rockstar', 'VIP Rock Pass');
  }

  switch (flow) {
    case Flow.C3:
      return `${formattedLabel} C3 Pass`;
    case Flow.CITY_PASS:
    case Flow.CITY_PASS_UPGRADE:
      return `${formattedLabel} CityPASS`;
    case Flow.TOR_EXPRESS:
      return `${formattedLabel}${ticketCount > 1 ? 'es' : ''}`;
    case Flow.RINK_MEMBERSHIP:
      return formattedLabel;
    default:
      return `${formattedLabel} Ticket${ticketCount > 1 ? 's' : ''}`;
  }
};

export const generateDataLayerItemId = (productAK: string) =>
  'SKU_' + productAK.replace(/^[PT]/, '');

// export { useIsMounted } from '../../utils';

export const isUserAgentMobile = (): boolean => {
  if (typeof window !== 'undefined') {
    return /Mobile|iP(hone|od|ad)|Android|BlackBerry|IEMobile/.test(
      window.navigator.userAgent || window.navigator.vendor,
    );
  }
  return false;
};

function parseDateTimeAsIs(dateString: string) {
  const date = new Date(dateString);

  // Get UTC date components
  const year = date.getUTCFullYear();
  const month = date.getUTCMonth();
  const day = date.getUTCDate();
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();

  // Array of month names
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  // Function to get ordinal suffix
  function getOrdinalSuffix(n: number): string {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return s[(v - 20) % 10] || s[v] || s[0];
  }

  // Format the date
  const formattedDate = `${monthNames[month]} ${day}${getOrdinalSuffix(
    day,
  )}, ${year}`;

  // Format the abbreviated date
  const abvDate = `${month + 1}/${day}/${year.toString().slice(-2)}`;

  // Format the time
  let period = 'AM';
  let hour = hours;
  if (hour >= 12) {
    period = 'PM';
    if (hour > 12) hour -= 12;
  }
  if (hour === 0) hour = 12;
  const formattedTime = `${hour}:${minutes
    .toString()
    .padStart(2, '0')} ${period}`;

  return {
    abvDate,
    date: formattedDate,
    time: formattedTime,
  };
}

export const formatDateTime = (dateTimeISOString?: string) => {
  if (!dateTimeISOString)
    return {
      date: null,
      time: null,
      abvDate: null,
    };

  // Format that local time
  const {
    date: formattedDate,
    time: formattedTime,
    abvDate,
  } = parseDateTimeAsIs(dateTimeISOString);

  // Return formatted date and time as an object
  return { date: formattedDate, time: formattedTime, abvDate: abvDate };
};

export const formatTimeToNearestHalfHour = (dateTimeStr: string): string => {
  // Extract hours and minutes from the time string (e.g., "2025-06-18T18:29:00.000Z")
  const timePart =
    dateTimeStr.indexOf('T') > -1 ? dateTimeStr.split('T')[1] : dateTimeStr;
  const [hours, minutes] = timePart.split(':').map(Number);

  // Round up to nearest half hour
  const roundedMinutes = Math.ceil(minutes / 30) * 30;
  let roundedHours = hours;

  if (roundedMinutes === 60) {
    roundedHours = (hours + 1) % 24;
  }

  // Convert to 12-hour format
  const period = roundedHours >= 12 ? 'PM' : 'AM';
  const displayHours = roundedHours > 12 ? roundedHours - 12 : roundedHours;

  return `${displayHours}:${roundedMinutes === 0 ? '00' : '30'} ${period}`;
};

export const formatTimeRangeDisplay = (
  timeSlot: Availability[] | undefined,
  index: number,
  isOnTheRocks: boolean,
) => {
  const startTime = timeSlot?.[index]?.startRaw;
  const endTime = timeSlot?.[index]?.endRaw;

  if (!startTime) return '';

  // Convert start time to 12-hour format
  const startHours = startTime.hours;
  const period = startHours >= 12 ? 'PM' : 'AM';
  const displayHours =
    startHours > 12 ? startHours - 12 : startHours === 0 ? 12 : startHours;
  const formattedStartTime = `${displayHours}:${startTime.minutes} ${period}`;

  if (!isOnTheRocks || !endTime) {
    return formattedStartTime;
  }

  const formattedEndTime = formatTimeToNearestHalfHour(
    `${endTime.hours}:${endTime.minutes}`,
  );

  return `${formattedStartTime} - ${formattedEndTime}`;
};

// Add this helper function outside the component
export const useCombinedRefs = (...refs: any[]): React.RefObject<any> => {
  const targetRef = useRef();

  useEffect(() => {
    refs.forEach((ref) => {
      if (!ref) return;
      if (typeof ref === 'function') {
        ref(targetRef.current);
      } else {
        ref.current = targetRef.current;
      }
    });
  }, [refs]);

  return targetRef;
};

const isProduction = process.env.GTM_ENV === 'production';

export function safeDataLayerPush(data: Record<string, unknown>) {
  if (window.dataLayer) {
    window.dataLayer.push(data);
  } else {
    console.log('DataLayer push (non-production):', data);
  }
}
