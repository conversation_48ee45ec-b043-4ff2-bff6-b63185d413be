/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Section, Container, Box } from '@tishman/components';
import React, { useEffect, useMemo } from 'react';
import { getWindow } from '@hzdg/dom-utils';

import {
  actions,
  selectFlow,
  useAppDispatch,
  useAppSelector,
} from '../../store';
import { fetchNYDate } from '../../services/date';

import { ProgressTracker } from '.';

interface WizardProps {
  children: React.ReactNode;
  totalSteps: number;
  width?: 'default' | 'wide' | 'small';
}

/** Wizard contains the progress tracker and all of the steps
 * for a given flow. It determines the total number of steps for a flow
 * and updates the progress tracker
 */
export const Wizard = ({
  children,
  totalSteps,
  width = 'default',
}: WizardProps): React.JSX.Element => {
  const dispatch = useAppDispatch();
  const flow = useAppSelector(selectFlow);

  useEffect(() => {
    const initializeWizard = async () => {
      dispatch(actions.wizard.initialize(flow));
      const dateInNYCToday = await fetchNYDate();
      const { day, month, year } = dateInNYCToday;

      dispatch(actions.wizard.setDateInNYCToday(dateInNYCToday));

      const window = getWindow();
      const dateParam =
        window?.location.search &&
        new URLSearchParams(window.location.search).get('date');
      const hasDateParam = Boolean(dateParam);

      if (!hasDateParam) {
        dispatch(
          actions.wizard.setSelectedDatePicker({ day, month, year, time: 0 }),
        );
      }
    };

    initializeWizard();
  }, [dispatch, flow]);

  const maxWidth = useMemo(() => {
    if (width === 'wide') return '1300px';
    else if (width === 'small') return '1200px';
    else return 'container';
  }, [width]);

  return (
    <Section
      sx={{
        width: '100%',
        bg: 'white',
        pt: [5, '104px'],
        pb: 6,
        pl: ['none', '50px'],
        display: 'flex',
        flex: 1,
      }}
      theme="Rock Center"
    >
      {totalSteps > 1 && (
        <Box
          sx={{
            width: '50px',
            left: 0,
            display: ['none', 'block'],
            position: 'fixed',
            top: '50%',
            transform: 'translateY(calc(-50% + 104px))',
          }}
        >
          <ProgressTracker steps={totalSteps} />
        </Box>
      )}
      <Container sx={{ maxWidth }}>{children}</Container>
    </Section>
  );
};
