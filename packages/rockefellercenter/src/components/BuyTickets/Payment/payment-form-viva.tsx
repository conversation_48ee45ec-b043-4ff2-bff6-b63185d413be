/** @jsxImportSource theme-ui @jsxRuntime classic */
import * as Sentry from '@sentry/gatsby';
import { Turnstile } from '@marsidev/react-turnstile';
import {
  Box,
  Checkbox,
  Flex,
  Grid,
  Input,
  Link,
  Modal,
  RequiredLabel,
  Select,
  SubmitButton,
  Text,
} from '@tishman/components';
import * as Icons from '@tishman/icons';
import validate from 'card-validator';
import { format, getYear, parse } from 'date-fns';
import { navigate } from 'gatsby';
import React, {
  useEffect,
  useState,
  useRef,
  Fragment,
  useCallback,
  useMemo,
} from 'react';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { useForm } from 'react-hook-form';
import kountSDK from '@kount/kount-web-client-sdk';
import { ErrorBoundary } from 'react-error-boundary';

import { KOUNT_MID, KOUNT_ENV } from '../../../environment';
import {
  useCheckOutMutation,
  useCloseOrderMutation,
  useDataProviderQuery,
  useGetDayPerformancesMutation,
  usePaymentMutation,
  useSaveAccountMutation,
  useSaveRinkMembershipAccountMutation,
  useSearchAccountMutation,
  useVoidTransactionMutation,
} from '../../../services/viva';
import {
  AVS_COUNTRIES,
  BosEvent,
  CART_LOCAL_STORAGE_KEY,
  CheckoutErrors,
  DataProviderCode,
  Flow,
  FlowPrettyPrint,
  ProductToPackageCode,
  COMP_TICKETS,
  COUPON_FOR_OTR_ZIP_CODE_EXEMPTION,
} from '../../../services/viva/constants';
import {
  actions,
  useAppDispatch,
  useAppSelector,
  selectCheckoutCartItems,
  selectCheckoutHasRinkSelections,
  selectCheckout,
  selectCheckoutFlow,
  selectCheckoutFlowForPayment,
  selectCitypassItems,
  selectFlow,
  selectPayment,
  selectCheckoutSelectionsFlat,
  selectCheckoutCartAddonSelectionsFlat,
  selectCheckoutCart,
  selectCheckoutCoupon,
  selectShopCartSelections,
  selectEvent,
} from '../../../store';
import { BuyTicketsModal, useBuyTicketsModal } from '../Modals';
import {
  isBosError,
  isI4GoError,
  isShift4Error,
  putInArray,
  transactionResponseHandler,
} from '../../../services/viva/utils';
import { i4Go } from '../../../services/shift4';
import { CardType, SaleRequest } from '../../../services/viva/types/payments';
import { Translatable, useTranslations } from '../../../utils/use-translations';
import { removeObjectForLocalStorageKey } from '../../../store/utils';
import { useGeolocationQuery } from '../../../utils';
import { ModalErrorCard } from '../Modals/ModalErrorCard';
import { parseFloatToDollars } from '../../../utils/parse-float-to-dollars';
import { useLocalizedLink } from '../../../utils/use-localized-link';
import { useMailchimpEcommMutation } from '../../../services/gatsby';
import { useVerifyTurnstileTokenMutation } from '../../../services/cloudflare';
import { isEmailBlocked } from '../../../services/viva/rules';
import { KountClient } from '../../../utils/kount/kount-client';
import { KOUNT } from '../../../utils/kount/constants';
import { ShortenedCountryCodeDictionary } from '../../../services/viva/constants/country';
import { StateNameDictionary } from '../../../services/viva/constants/state';
import { safeDataLayerPush } from '../utils';
import { newRelic } from '../../../utils/new-relic';
import {
  isLocalCoupon,
  isLocalZipCode,
} from '../../../utils/coupons-by-zipcode';
import { FormErrorText } from '../../FormErrorText';
import {
  getItemId,
  getItemName,
  getItemQuantity,
} from '../../../utils/type-guards';

import type { DataLayerContext } from '../types';
import type { TurnstileInstance } from '@marsidev/react-turnstile';
import type { DataProviderItem } from '../../../services/viva/types';
import type {
  CheckoutOrderAccountFields,
  CheckoutOrderFormFields,
} from '../../../store/types';
import type { CheckoutLoaderHandlers } from './payment-component-viva';
import type {
  CityPassCloseOrderResponse,
  SanitizedPaymentResult,
  Shift4SaleResponse,
} from '../../../services/viva/types/response';
import type { RinkMember } from '../../../blocks/BuyTicketsRinkMembershipAccountStepBlock';
import type { CheckoutTranslationsForLocale } from '../../../data/translations/checkout';
import type {
  CityPassVoucher,
  ShopCartSelection,
} from '../../../store/types/order';
import type { AddOnItem } from '../AddOns';
import type { TProduct } from '../../../utils/kount/mode-q-params';

const CreditCardIcon = ({ number = '' }: { number: string }): JSX.Element => {
  const validation = validate.number(number);

  switch (validation.card?.type) {
    case 'american-express':
      return <Icons.AmexCard />;
    case 'diners-club':
      return <Icons.DinersCard />;
    case 'discover':
      return <Icons.DiscoverCard />;
    case 'mastercard':
      return <Icons.MasterCard />;
    case 'visa':
      return <Icons.VisaCard />;
    default:
      return <Icons.UnknownCard />;
  }
};

type PaymentFormVivaProps = CheckoutLoaderHandlers;

type StateItem = {
  CODE: string;
  DESCRIPTION: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const removePIIFromError = (error: any): any => {
  if (!error) return error;

  // Create a deep copy to avoid mutating the original error
  const sanitizedError = JSON.parse(JSON.stringify(error));

  // Remove PII from first path
  if (sanitizedError?.raw?.config?.data?.parameters?.fields) {
    delete sanitizedError.raw.config.data.parameters.fields;
  }

  // Remove PII from second path
  if (
    sanitizedError?.raw?.request?.__sentry_xhr_v3__?.body?.parameters?.fields
  ) {
    delete sanitizedError.raw.request.__sentry_xhr_v3__.body.parameters.fields;
  }

  return sanitizedError;
};

const sanitizePaymentData = (payment: Shift4SaleResponse) => {
  if (!payment) return payment;

  // Create a deep copy to avoid mutating the original payment
  const sanitizedPayment = JSON.parse(JSON.stringify(payment));

  if (Array.isArray(sanitizedPayment.result)) {
    sanitizedPayment.result = sanitizedPayment.result.map(
      (item: SanitizedPaymentResult) => {
        // Remove customer PII
        if (item.customer) {
          delete item.customer;
        }

        // Remove or mask card details
        if (item.card) {
          item.card = {
            type: item.card.type,
            entryMode: item.card.entryMode,
            present: item.card.present,
          };
        }

        // Remove sensitive receipt data
        if (item.receipt) {
          item.receipt = item.receipt.filter(
            (r: { key: string; value: string }) =>
              !['MaskedPAN', 'CardEntryMode'].includes(r.key),
          );
        }

        // Remove sensitive transaction data
        if (item.transaction) {
          delete item.transaction.purchaseCard;
          delete item.transaction.universalToken;
        }

        return item;
      },
    );
  }

  return sanitizedPayment;
};

export const PaymentFormViva = ({
  setShowLoadingModal,
  setIsCheckoutSubmitting,
}: PaymentFormVivaProps): JSX.Element => {
  const translations = useTranslations<CheckoutTranslationsForLocale>(
    Translatable.Checkout,
  );
  const modal = useBuyTicketsModal();
  const shopCartSelections = useAppSelector(selectShopCartSelections);

  const handleError = useCallback(
    (error: Error, errorBlock?: JSX.Element) => {
      modal.setValue(error);
      setShowLoadingModal?.(false);
      setIsCheckoutSubmitting?.(false);
      if (errorBlock) {
        setCheckoutErrorBlock(errorBlock);
      }
      setCheckoutError(error);
      modal.setIsOpen(true); // Explicitly set modal to open
    },
    [modal, setShowLoadingModal, setIsCheckoutSubmitting],
  );

  const dispatch = useAppDispatch();

  const bosEvent = useAppSelector(selectEvent);
  const checkout = useAppSelector(selectCheckout);
  const flow = useAppSelector(selectCheckoutFlow);
  const shopCartFlow = useAppSelector(selectFlow);
  const cart = useAppSelector(selectCheckoutCart);
  const paymentFlow = useAppSelector(selectCheckoutFlowForPayment);
  const geoLocationQuery = useGeolocationQuery();
  const isRinkRelated = useAppSelector(selectCheckoutHasRinkSelections);
  const accountAKs =
    useAppSelector((state) => state.checkout.payment.accountAKs) ?? [];
  const checkoutPayment = useAppSelector(selectPayment);

  const isCityPASSRedemption = shopCartFlow === Flow.CITY_PASS_REDEMPTION;
  const getLocalizedLink = useLocalizedLink();

  const checkoutItems = useAppSelector(selectCheckoutCartItems);
  const cityPassItems = useAppSelector(selectCitypassItems);
  const checkoutSelectionsFlat = useAppSelector(selectCheckoutSelectionsFlat);
  const checkoutCartAddonSelectionsFlat = useAppSelector(
    selectCheckoutCartAddonSelectionsFlat,
  );
  const checkoutCouponCode = useAppSelector(selectCheckoutCoupon);

  const [checkoutError, setCheckoutError] = useState<Error | null>(null);
  const [checkoutErrorBlock, setCheckoutErrorBlock] = useState<
    JSX.Element | undefined
  >();
  const [turnstileToken, setTurnstileToken] = useState<string>('');
  const [turnstileValid, setTurnstileValid] = useState<boolean>(false);

  const turnstileRef = useRef<TurnstileInstance>(null);

  const countriesQuery = useDataProviderQuery({
    dataProviderCode: DataProviderCode.Country,
  });
  const statesQuery = useDataProviderQuery({
    dataProviderCode: DataProviderCode.USState,
  });

  const paymentMutation = usePaymentMutation();
  const voidTransaction = useVoidTransactionMutation();
  const checkOutMutation = useCheckOutMutation();
  const closeOrderMutation = useCloseOrderMutation();
  const accountMutation = useSaveAccountMutation();
  const rinkMemberAccountMutation = useSaveRinkMembershipAccountMutation();
  const searchAccountMutation = useSearchAccountMutation();
  const mailchimpMutation = useMailchimpEcommMutation();
  const verifyTurnstileTokenMutation = useVerifyTurnstileTokenMutation();

  const { formState, getValues, handleSubmit, register, setValue, watch } =
    useForm<CheckoutOrderFormFields>({
      mode: 'onSubmit',
    });

  const [correlationId] = useState(() => crypto.randomUUID());

  /**
 * KountConfig can also include callbacks called at specific stages.
 * Implementation example: Add the callbacks to the interface and then define them

  interface KountConfig {
    ...,
    callbacks?: {
      'collect-begin': (params: any) => void;
      'collect-end': (params: any) => void;
    };
  }

  kountConfig.callbacks = {
    'collect-begin': function (params) {
      // Any code you'd like to execute on collection start,
      //     such as quietly adding the session ID to your login form!
    },
    'collect-end': function (params) {
      // Any code you'd like to execute when we're done collecting data
    },
  };

 */

  interface KountConfig {
    clientID: string;
    environment: string;
    isSinglePageApp: boolean;
    isDebugEnabled: boolean;
  }

  const kountClient = useMemo(() => new KountClient(), []);

  const kountConfig = useMemo<KountConfig>(
    () => ({
      clientID: KOUNT_MID,
      environment: KOUNT_ENV,
      isSinglePageApp: false,
      isDebugEnabled: false,
    }),
    [],
  );
  useEffect(() => {
    const initKountSDK = async () => {
      try {
        const kSDK = await kountSDK(kountConfig, kountClient.getSessionId());

        if (kSDK) {
          kSDK.AttachToForm('payment-form');
          console.log('Anti-fraud SDK activated!');

          if (kountConfig.isDebugEnabled) {
            console.log('Kount Session ID:', kountClient.getSessionId());
            console.log('Kount Client ID:', KOUNT_MID);
            console.log('Kount Environment:', KOUNT_ENV);
          }
        }
      } catch (error) {
        console.error('Failed to initialize Kount SDK:', error);
      }
    };

    initKountSDK();
  }, [kountConfig, kountClient]);

  // Using a copy of the values for our form fields circumvents a readonly error on getValue fields.
  const values: CheckoutOrderFormFields = watch();
  const temp = JSON.stringify(values);
  const copiedValues = JSON.parse(temp) as CheckoutOrderFormFields;

  const country = watch('address.country');
  const zipCode = watch('address.zip');

  useEffect(() => {
    if (country !== 'USA' && country !== 'US')
      setValue('address.state', 'None');
  }, [country, setValue]);

  // This updates checkout.account.address.zip for isLocalZipCode validation in order-summary-viva
  useEffect(() => {
    dispatch(actions.checkout.setAccountZipCode(zipCode));
  }, [zipCode]);

  // Begin Rink Skates Performance override
  // Make sure the rink skates have the correct performance and same date as the tickets
  const dayPerformanceMutation = useGetDayPerformancesMutation();

  const { selections: { RINK: rinkSelections = [] } = { RINK: [] } } = cart;

  type FormFieldsAddress = {
    country: string;
    street: string;
    city: string;
    state: string;
    zip: string;
  };

  type FormFields = {
    firstName: string;
    lastName: string;
    email: string;
    mobileNumber: string;
    address: FormFieldsAddress;
    optInMail: boolean;
  };

  const validateAccountFormFields = (account: FormFields): string[] => {
    const invalidKeys: string[] = [];

    // Check top-level string fields
    Object.entries(account).forEach(([key, value]) => {
      if (key === 'address') {
        // Check nested address fields
        Object.entries(account.address).forEach(
          ([addressKey, addressValue]) => {
            if (!addressValue || addressValue.trim() === '') {
              invalidKeys.push(`address.${addressKey}`);
            }
          },
        );
      } else if (key === 'optInMail') {
        // Skip boolean field
        return;
      } else if (!value || (typeof value === 'string' && value.trim() === '')) {
        invalidKeys.push(key);
      }
    });

    return invalidKeys;
  };

  const timeRaw = rinkSelections[0]?.timeSlot?.[0]?.timeRaw;
  const year = timeRaw ? parseInt(timeRaw.split('-')[0]) : 0;
  const month = timeRaw ? parseInt(timeRaw.split('-')[1]) : 0;
  const day = timeRaw ? parseInt(timeRaw.split('-')[2]) : 0;
  const getDayPerformances = useCallback(async () => {
    try {
      const perfRes = await dayPerformanceMutation.mutateAsync({
        bosEvent: BosEvent.SKATE_RENTAL,
        day,
        year,
        month,
      });
      return perfRes;
    } catch (error) {
      console.error('Error fetching day performances:', error);
      handleError(error as Error);
      throw error;
    }
  }, [day]);

  useEffect(() => {
    if (flow === Flow.RINK) {
      getDayPerformances().then((data) => {
        if (data?.[0]?.AK) {
          dispatch(
            actions.checkout.updateSkateRentalPerformances({
              performanceAK: data[0].AK,
              flow: Flow.RINK,
            }),
          );
        }
      });
    }
  }, []);
  // End Rink Skates Performance override

  useEffect(() => {
    if (turnstileToken) {
      verifyTurnstileTokenMutation.mutateAsync(turnstileToken, {
        onSuccess: (response) => {
          if (process.env.NODE_ENV !== 'production') {
            // This is for use on preview branches, always return true.
            setTurnstileValid(true);
          } else {
            setTurnstileValid(response.success);
            if (!response.success) {
              let errorMessage = `Siteverify failed ${response[
                'error-codes'
              ].join(', ')}\n\n`;
              errorMessage += response.messages.length
                ? response.messages.join(' - ')
                : '';

              const error = new Error(errorMessage);
              error.name = CheckoutErrors.CheckoutError;

              modal.setValue(error);
              setShowLoadingModal?.(false);
              setIsCheckoutSubmitting?.(false);

              Sentry.setTag('checkout_errors', 'turnstile_error');
              Sentry.setTag('correlation_id', correlationId);
              Sentry.captureException(error);
              newRelic.logCartAction(
                `PaymentFormViva - Turnstile Error: ${errorMessage}`,
                'verifyTurnstileTokenMutation',
                { turnstileToken, correlationId },
              );
            }
          }
        },
        onError: (err) => {
          const error = new Error('Siteverify failed');
          error.name = CheckoutErrors.CheckoutError;

          modal.setValue(error);
          setShowLoadingModal?.(false);
          setIsCheckoutSubmitting?.(false);

          Sentry.setTag('checkout_errors', 'turnstile_onError');
          Sentry.setTag('correlation_id', correlationId);
          Sentry.captureException(err);
          setCheckoutError(error);
          newRelic.logCartAction(
            'PaymentFormViva - Turnstile Error: Siteverify failed',
            'verifyTurnstileTokenMutation',
            { turnstileToken, correlationId },
          );
        },
      });
    }
  }, [turnstileToken]);

  const onSubmit = async () => {
    if (
      bosEvent === BosEvent.ON_THE_ROCKS &&
      !isLocalZipCode(zipCode) &&
      checkoutCouponCode !== COUPON_FOR_OTR_ZIP_CODE_EXEMPTION
    ) {
      const error = new Error(
        `Reason: On the Rocks is a local promotion and is not available for this zip code`,
      );
      error.name = CheckoutErrors.CheckoutError;
      handleError(error);
      return;
    }

    newRelic.logCartAction(
      'PaymentFormViva - Checkout Form Submission',
      'onSubmit',
    );

    // Log initial comp addon state
    const initialCompAddons = Object.values(checkout.cart.addOnSelections)
      .flatMap((flowAddons) => Object.values(flowAddons))
      .flat()
      .filter((addon) => COMP_TICKETS.includes(addon.statGroup))
      .map((addon) => ({
        ak: addon.ak,
        name: addon.name ?? '',
        statGroup: addon.statGroup,
        quantity: addon.quantity,
        dateTime: addon.performances?.[0], // Use first performance as dateTime
      }));

    newRelic.logCartAction(
      'PaymentFormViva - Initial Comp Addons State',
      'onSubmit',
      { initialCompAddons },
    );

    const { creditCard, ...formFields } = copiedValues;
    const {
      mobileNumber,
      email,
      creditCard: { number },
    } = copiedValues;

    const trimmedValues = {
      number: number.trim(),
      email: email.trim(),
      mobileNumber: mobileNumber!.trim(),
    };

    formFields.mobileNumber = trimmedValues.mobileNumber;
    formFields.email = trimmedValues.email;
    creditCard.number = trimmedValues.number;

    if (
      checkoutCouponCode &&
      !isLocalZipCode(zipCode) &&
      isLocalCoupon(checkoutCouponCode)
    ) {
      const error = new Error(
        `Reason: Your zip code no longer matches the requirements for the applied promotion`,
      );
      error.name = CheckoutErrors.CheckoutError;
      handleError(error);

      Sentry.setTag('coupon_zip_mismatch', zipCode);
      Sentry.captureException(error);

      newRelic.logCartAction(
        'PaymentFormViva - Local Coupon Zip Code Mismatch',
        'isLocalZipRequiredForCoupon',
        { zipCode, checkoutCouponCode },
      );

      return;
    }

    try {
      // call this as soon as the user clicks the submit button
      setShowLoadingModal(true);
      setIsCheckoutSubmitting(true);

      // Immediately check if the user's email is on the domain blocklist
      if (isEmailBlocked(formFields.email)) {
        const error = new Error(
          `Reason: There was an issue processing your order. Please check your information and try again.`,
        );
        error.name = CheckoutErrors.CheckoutError;

        Sentry.setTag('email_blocked', formFields.email);
        Sentry.setTag('correlation_id', correlationId);
        Sentry.captureException(error);

        newRelic.logCartAction(
          'PaymentFormViva - Email on Blocklist Error',
          'isEmailBlocked',
          { correlationId },
        );

        handleError(error);
        throw error;
      }

      let _checkoutItems = [
        ...(isCityPASSRedemption ? cityPassItems : checkoutItems),
      ];

      const accountFormFieldIssues = validateAccountFormFields(
        formFields as FormFields,
      );

      const setAccountFieldsAsync = createAsyncThunk(
        'checkout/setAccountFieldsAsync',
        async (formFields: CheckoutOrderAccountFields, { dispatch }) => {
          dispatch(actions.checkout.setAccountFields(formFields));
          newRelic.logCartAction(
            'PaymentFormViva - Set Account fields',
            'setAccountFields',
            { accountFormFieldIssues },
          );
          return formFields;
        },
      );

      // Wait for the state update to complete
      await dispatch(setAccountFieldsAsync(formFields));

      let account: {
        ACCOUNTAK: string;
      } | null = null;

      const mutateAccountData: {
        flow: Flow.RINK | Flow.TOR_GA;
        accountAK?: string;
      } = {
        flow,
      };

      /** Due to the account retrieval limitations in BOS
       * the feature of searching for and using existing accounts
       * must be disabled for the moment. Right now the Rink API workstation
       * and the RC API workstation are not able to use (retrieve) each other's
       * user accounts.
       */
      // SEARCH ACCOUNT - only if not already saved
      // const searchAccount = await searchAccountMutation.mutateAsync({
      //   email: formFields.email,
      // });

      // if (searchAccount.status.code === 200 && searchAccount.ACCOUNTAK) {
      //   // account found, use this accountAK
      //   account = { ACCOUNTAK: searchAccount.ACCOUNTAK };
      //   console.info('Account found!');
      //   mutateAccountData = {
      //     ...mutateAccountData,
      //     accountAK: account.ACCOUNTAK,
      //   };
      // } else {
      //   console.info('Account NOT found!');
      // }

      let accountDataForNewRelic = {};
      // SAVE ACCOUNT - If we have an account AK then it is passed in and the existing account gets updated,
      // if not then accountAK is null and the soap xml parser will omit the accountAK thus creating a new account.
      if (
        shopCartFlow !== Flow.CITY_PASS_REDEMPTION &&
        accountAKs.length === 0
      ) {
        try {
          const accountResponse = await accountMutation
            .mutateAsync(mutateAccountData)
            .catch((err: unknown) => {
              // Type guard to check if err is an Error object with additional properties
              interface ErrorWithResponse extends Error {
                response?: {
                  data?: unknown;
                  status?: number;
                };
              }

              const errorData = {
                name: err instanceof Error ? err.name : 'Unknown Error',
                message: err instanceof Error ? err.message : 'Unknown Error',
                response: (err as ErrorWithResponse)?.response?.data,
                status: (err as ErrorWithResponse)?.response?.status,
                raw: err, // Include the raw error object for debugging
              };

              // Sanitize PII before logging
              const sanitizedErrorData = removePIIFromError(errorData);

              newRelic.logCartAction(
                `PaymentFormViva - accountResponse catch`,
                'accountMutation',
                {
                  catchError: sanitizedErrorData,
                  accountFormFieldIssues,
                },
              );

              // Re-throw the original error with the full error data
              throw errorData;
            });

          accountDataForNewRelic = {
            accountResponseError: {
              ERROR: accountResponse.ERROR || '',
              ERRORLISTOBJTYPE: accountResponse.ERRORLISTOBJTYPE || [],
            },
            accountFormFieldIssues,
            correlationId,
          };

          // Sanitize before logging
          const sanitizedAccountData = removePIIFromError(
            accountDataForNewRelic,
          );

          newRelic.logCartAction(
            `PaymentFormViva - accountResponse`,
            'accountMutation',
            { accountDataForNewRelic: sanitizedAccountData },
          );
          if (isBosError(accountResponse)) {
            const errorMessage = `Reason: ${accountResponse.ERROR.TEXT}. SaveAccount (CODE: ${accountResponse.ERROR.CODE})`;
            const error = new Error(errorMessage);
            error.name = CheckoutErrors.AccountError;

            Sentry.setTag('checkout_errors', 'save_account_BOS_error');
            Sentry.setTag('correlation_id', correlationId);
            Sentry.captureException(error);

            newRelic.logCartAction(
              `PaymentFormViva - Save Account BOS Error: ${errorMessage}`,
              'accountMutation',
              { accountResponse, accountFormFieldIssues, correlationId },
            );

            handleError(error);
            throw error;
          }

          if (accountResponse?.ACCOUNTAK) {
            account = { ACCOUNTAK: accountResponse.ACCOUNTAK };
            dispatch(
              actions.checkout.setAccountAKs([accountResponse.ACCOUNTAK]),
            );
          }
        } catch (error) {
          if (error instanceof Error) {
            handleError(error);
          } else {
            const genericError = new Error(
              'An unexpected error occurred while saving the account',
            );
            genericError.name = CheckoutErrors.AccountError;
            handleError(genericError);
          }

          newRelic.logCartAction(
            'PaymentFormViva - Save Account Unexpected Error',
            'accountMutation',
            {
              error,
              accountDataForNewRelic,
              accountFormFieldIssues,
              saveAccountError: true,
            },
          );

          throw error;
        }
      }
      // If rink membership, create an account for each member
      if ((checkout.cart.selections[Flow.RINK_MEMBERSHIP]?.length ?? 0) > 0) {
        if (!checkout.attributes?.members) {
          const errorMessage = 'No members found for rink membership';
          const error = new Error(errorMessage);
          error.name = CheckoutErrors.CheckoutError;

          Sentry.setTag('checkout_errors', 'rink_membership_error');
          Sentry.setTag('correlation_id', correlationId);
          Sentry.captureException(error);

          newRelic.logCartAction(
            `PaymentFormViva - Membership Account Error: ${errorMessage}`,
            'setAccountFields',
            {
              cartSelections: checkout.cart.selections[Flow.RINK_MEMBERSHIP],
              correlationId,
            },
          );

          handleError(error);
          throw error;
        }

        let memberAccounts = accountAKs ?? [];

        if ((accountAKs?.length ?? 0) < 2) {
          const members = (checkout.attributes?.members ?? []) as RinkMember[];
          const memberAccountsPromises = await Promise.all(
            members.map((member) =>
              rinkMemberAccountMutation.mutateAsync({
                member,
              }),
            ),
          );

          memberAccounts = memberAccountsPromises.map(
            (acc) => acc.ACCOUNTAK ?? '',
          );
        }

        // Map created accounts to members
        _checkoutItems = _checkoutItems
          .filter((checkoutItem) => !!checkoutItem)
          .map((checkoutItem, idx) => {
            return {
              ...checkoutItem!,
              ...(memberAccounts?.[idx]
                ? { accountAK: memberAccounts[idx] }
                : {}),
            };
          });
      }

      const _accountAK = accountAKs?.length
        ? accountAKs[0]
        : account?.ACCOUNTAK;

      let saleAK = checkoutPayment.saleAK;
      let totalTax = checkoutPayment.totalTax;
      let totalWithTax = checkoutPayment.totalWithTax;
      let reservationCode = checkoutPayment.reservationCode;

      // CHECKOUT, PAYMENT, CLOSE ORDER
      if (!saleAK) {
        newRelic.logCartAction(
          'PaymentFormViva - Begin Checkout Requests',
          'checkout',
          { saleAK },
        );
        const coupon = checkout.cart.couponCode;
        const checkoutMutation = await checkOutMutation.mutateAsync({
          flow: isCityPASSRedemption ? Flow.TOR_GA : flow,
          ...(_accountAK ? { accountAK: _accountAK } : {}),
          items: _checkoutItems,
          ...(coupon ? { coupon } : {}),
        });

        if (isBosError(checkoutMutation)) {
          const errorMessage = `Reason: ${checkoutMutation.ERROR.TEXT}. CheckOut (CODE: ${checkoutMutation.ERROR.CODE})`;
          const error = new Error(errorMessage);
          error.name = CheckoutErrors.CheckoutError;

          Sentry.setTag('checkout_errors', 'checkout_mutation_error');
          Sentry.setTag('correlation_id', correlationId);
          Sentry.captureException(error);

          newRelic.logCartAction(
            `PaymentFormViva - Checkout BOS Error: ${errorMessage}`,
            'checkoutMutation',
            { checkoutMutation, correlationId },
          );

          handleError(error);
          throw error;
        } else {
          saleAK = checkoutMutation.SALE.AK;
          totalTax = checkoutMutation.SALE.TOTAL.TAX;
          totalWithTax = checkoutMutation.SALE.TOTAL.GROSS;
          reservationCode = checkoutMutation.SALE.RESERVATIONCODE;

          dispatch(
            actions.checkout.setPayment({
              saleAK,
              reservationCode,
            }),
          );

          newRelic.logCartAction(
            'PaymentFormViva - Checkout setPayment',
            'checkoutMutation',
            {
              saleAK,
              reservationCode,
            },
          );
        }
      }

      const transactionInvoice = reservationCode?.slice(-10) ?? '';

      if (isCityPASSRedemption && saleAK) {
        // Close Order
        newRelic.logCartAction(
          `PaymentFormViva - Begin Close Order`,
          'closeOrderMutation',
          {
            saleAK,
          },
        );
        await closeOrderMutation.mutateAsync(
          {
            flow: Flow.CITY_PASS_REDEMPTION,
            saleAK,
          },
          {
            onSuccess: async (closeOrder) => {
              if (isBosError(closeOrder)) {
                try {
                  await voidTransaction.mutateAsync({
                    invoice: transactionInvoice,
                    flow,
                  });

                  try {
                    // Only attempt to send Mode U request if we have a transaction ID
                    if (kountClient.getTransactionId()) {
                      await kountClient.sendModeURequest(
                        KOUNT.AUTHORIZATION_STATUS.D,
                      );
                    } else {
                      console.log(
                        'Skipping Kount Mode U request - no transaction ID available',
                      );
                    }
                  } catch (kountModeUError) {
                    // Log but don't fail the flow
                    console.error(
                      'Non-fatal error updating Kount status:',
                      kountModeUError,
                    );
                  }
                } catch (voidError) {
                  // Log void failure for investigation
                  Sentry.setTag(
                    'checkout_errors',
                    'void_transaction_failed_bos_error',
                  );
                  Sentry.setTag('correlation_id', correlationId);
                  Sentry.captureException(voidError);

                  newRelic.logCartAction(
                    'PaymentFormViva - Void Transaction Failed After BOS Error',
                    'voidTransaction',
                    { invoice: transactionInvoice, flow, error: voidError },
                  );
                }
                const errorMessage = `Reason: ${closeOrder.ERROR.TEXT}. CloseOrder (CODE: ${closeOrder.ERROR.CODE})`;
                const error = new Error(errorMessage);
                error.name = CheckoutErrors.CloseOrderError;

                Sentry.setTag('checkout_errors', 'close_order_BOS_error');
                Sentry.setTag('correlation_id', correlationId);
                Sentry.captureException(error);

                newRelic.logCartAction(
                  `PaymentFormViva - Close Order BOS Error: ${errorMessage}`,
                  'closeOrderMutation',
                  {
                    flow,
                    saleAK,
                    amount: totalWithTax,
                    closeOrder,
                    correlationId,
                  },
                );
                newRelic.logCartAction(
                  `PaymentFormViva - Close Order Error: ${errorMessage}`,
                  'voidTransaction',
                  { invoice: transactionInvoice, flow, saleAK },
                );

                handleError(error);
                throw error;
              }
              dispatch(
                actions.checkout.setTransactionAK(closeOrder.TRANSACTION.AK),
              );

              newRelic.logCartAction(
                `PaymentFormViva - Close Order success`,
                'closeOrderMutation',
                {
                  saleAK: closeOrder.SALE.AK,
                  transactionAK: closeOrder.TRANSACTION.AK,
                },
              );
            },
          },
        );
      }

      if (saleAK && reservationCode) {
        dispatch(
          actions.checkout.setSaleAndReservation({ saleAK, reservationCode }),
        );
      }
      if (!isCityPASSRedemption) {
        const cardType = (ccn: string) => {
          const cardTypeValidation = validate.number(ccn);
          switch (cardTypeValidation.card?.type) {
            case 'american-express':
              return CardType.AX;
            case 'discover':
              return CardType.NS;
            case 'mastercard':
              return CardType.MC;
            case 'visa':
              return CardType.VS;
            default:
              return '';
          }
        };

        const _kountProducts = checkoutSelectionsFlat.reduce(
          (products, selection) => {
            if (selection) {
              products.push({
                price: selection.price,
                quantity: selection.count,
                type: FlowPrettyPrint[flow],
                item: selection.productAK,
                description: (selection.helpLabel ?? '').slice(0, 48),
              });
            }
            return products;
          },
          [] as TProduct[],
        );

        const kountAddonProducts = checkoutCartAddonSelectionsFlat.reduce(
          (addons, selection) => {
            if (selection) {
              addons.push({
                price: selection.price,
                quantity: selection.quantity ?? 1,
                type: FlowPrettyPrint[flow],
                item: selection.ak,
                description: (
                  selection.name ??
                  selection.description ??
                  ''
                ).slice(0, 48),
              });
            }
            return addons;
          },
          [] as TProduct[],
        );

        const kountProducts = _kountProducts.concat(kountAddonProducts);

        const startDate = checkoutSelectionsFlat[0]?.dateTime
          ? new Date(checkoutSelectionsFlat[0].dateTime)
          : new Date();

        const formattedDate = `${startDate.getFullYear()}-${String(
          startDate.getMonth() + 1,
        ).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;

        // Calculate cardBin, cardLast4, subtotal, and taxAmount for Kount
        const cardBin = creditCard.number ? creditCard.number.slice(0, 6) : '';
        const cardLast4 = creditCard.number ? creditCard.number.slice(-4) : '';
        const subtotal = kountProducts
          .reduce((sum, p) => sum + p.price * (p.quantity || 1), 0)
          .toString();
        const taxAmount = checkout.cart.totalTax
          ? (parseFloat(checkout.cart.totalTax) * 100).toString()
          : '0';
        const outOfStateTaxAmount = '0'; // Update if you have this value
        const kountRequestParams = {
          cardNumber: creditCard.number,
          cardBin,
          cardLast4,
          subtotal,
          taxAmount,
          outOfStateTaxAmount,
          firstName: formFields.firstName,
          lastName: formFields.lastName,
          email: formFields.email,
          total: (
            parseFloat(
              checkout.cart.totalWithTax ?? checkout.cart.total ?? '0',
            ) * 100
          ).toString(),
          products: kountProducts,
          remoteAddress: geoLocationQuery.data?.IPv4 ?? '127.0.0.1',
          name: `${formFields.firstName} ${formFields.lastName}`,
          billingAddress: formFields.address.street,
          billingCity: formFields.address.city,
          billingState: formFields.address?.state
            ? StateNameDictionary[formFields.address.state]
            : 'None',
          billingPostalCode: formFields.address.zip,
          billingCountryCode:
            ShortenedCountryCodeDictionary[formFields.address.country],
          phone: formFields.mobileNumber ?? '',
          orderNumber: saleAK,
          customerID: account?.ACCOUNTAK ?? '',
          ticketStartDate: formattedDate,
        };

        const kountResponse = await kountClient
          .sendModeQRequest(kountRequestParams)
          .catch((error) => {
            console.error('Kount Error', error);
            newRelic.logCartAction(
              'PaymentFormViva - Kount Error',
              'kountResponse',
              { error, correlationId },
            );
            // Instead of throwing the error, return a default response that will allow processing to continue
            // This is a graceful fallback when Kount is unavailable
            return {
              AUTO: 'A', // Default to Approve when the service is unavailable
              TRAN: `DEMO-${Date.now()}`,
              SCOR: 100, // High score to allow processing
              OMNISCORE: 1.0, // High Omniscore to allow processing
              RULES_TRIGGERED: [],
              WARNING_CODES: [],
            };
          });

        if (!kountResponse) {
          throw new Error('Kount response missing');
        }

        newRelic.logCartAction(
          'PaymentFormViva - Kount General Response',
          'kountResponse',
          { kountResponse, correlationId },
        );

        // Explicitly store the transaction ID from Kount in the client
        // This ensures it's available for the Mode U request later
        if (kountResponse.TRAN) {
          // We need to manually set the transaction ID in kountClient
          // since some flows might not store it automatically
          kountClient.setTransactionId(kountResponse.TRAN);
        }

        // If Kount returns 'Review' status, log it but continue with the transaction
        if (kountResponse.AUTO === 'R') {
          newRelic.logCartAction(
            'PaymentFormViva - Kount Review Status - Proceeding',
            'kountResponse',
            { kountResponse, correlationId },
          );
        }

        // TOKENIZE CARD
        const supportsAVS =
          AVS_COUNTRIES.includes(formFields.address.country) &&
          !formFields.address.country.includes('(UK)');
        const addressInfo = {
          i4go_postalcode: formFields.address.zip,
          i4go_streetaddress: formFields.address.street,
        };

        newRelic.logCartAction(
          'PaymentFormViva - Begin Card Tokenization',
          'i4Go',
          { correlationId },
        );

        const securedCHD = await i4Go({
          i4go_clientIP: geoLocationQuery.data?.IPv4 || '127.0.0.1',
          i4go_cardtype: cardType(creditCard.number),
          i4go_cardnumber: creditCard.number,
          i4go_expirationmonth: creditCard.month,
          i4go_expirationyear: creditCard.year,
          i4go_cvv2code: creditCard.cvv.trim(),
          i4go_cardholdername: `${formFields.firstName} ${formFields.lastName}`,
          ...(supportsAVS ? addressInfo : {}),
          flow: paymentFlow,
          correlationId,
        });

        if (isI4GoError(securedCHD)) {
          const {
            i4go_responsetext: longText,
            i4go_responsecode: primaryCode,
            i4go_response: shortText,
          } = securedCHD;

          const error = new Error(
            `Reason: ${shortText} - ${longText}  (CODE: ${primaryCode})`,
          );
          error.name = CheckoutErrors.PaymentError;

          Sentry.setTag('checkout_errors', 'payment_error');
          Sentry.setTag('correlation_id', correlationId);
          Sentry.captureException(error);

          newRelic.logCartAction(
            'PaymentFormViva - i4Go Tokenization Error',
            'i4Go',
            { securedCHD, correlationId },
          );

          handleError(error);
          throw error;
        }

        newRelic.logCartAction(
          'PaymentFormViva - Card Tokenization Success',
          'i4Go',
          { correlationId },
        );

        if (
          !saleAK ||
          !reservationCode ||
          !totalTax ||
          !totalWithTax ||
          !checkout.cart.total ||
          checkout.cart.total === '0'
        ) {
          throw new Error(
            'There was an error proccessing your request. Please try again, or contact us for assistance.',
          );
        }

        interface TimeSlot {
          ak: string;
          timeRaw: string;
          start: number;
          startRaw: {
            hours: number;
            minutes: string;
            meridian: string;
          };
          isPopular: boolean;
          availability: string;
          isBundle: boolean;
          priceTableAK: string;
        }

        interface CartItem {
          count?: number;
          label?: string;
          timeSlot?: TimeSlot[];
          [key: string]: unknown;
        }

        const desiredShift4Values = ['count', 'label', 'timeSlot'];

        const shift4ProductData = [
          ...Object.values(checkout.cart.selections),
          ...Object.values(checkout.cart.addOnSelections)
            .map((i) => Object.values(i))
            .flat(),
        ]
          .filter((i) => (i?.length ?? 0) > 0)
          .flat()
          .map((item) => {
            const filteredItem: Record<string, unknown> = {};
            // First cast to unknown then to CartItem for type safety
            const cartItem = item as unknown as CartItem;

            if (cartItem) {
              desiredShift4Values.forEach((prop) => {
                if (prop in cartItem) {
                  if (prop === 'timeSlot' && Array.isArray(cartItem.timeSlot)) {
                    // Transform timeSlot array to just include ak and timeRaw values
                    filteredItem[prop] = {
                      aks: cartItem.timeSlot.map((slot) => slot.ak),
                      times: cartItem.timeSlot.map((slot) =>
                        slot.timeRaw.replace('T', ' ').replace('.000Z', ''),
                      ),
                    };
                  } else {
                    filteredItem[prop] = cartItem[prop];
                  }
                }
              });
            }
            return filteredItem;
          });

        const shift4ProductDataCleaned = {
          saleAK,
          totalWithTax,
          accountAK: account?.ACCOUNTAK || accountAKs[0] || '', // Use direct account value first, fall back to Redux state
          ipaddress: geoLocationQuery.data?.IPv4,
          reservationCode,
          ...shift4ProductData,
          kountResponse,
        };

        const transactionInvoice = reservationCode?.slice(-10) ?? '';
        const sale: SaleRequest = {
          invoice: transactionInvoice,
          amount: {
            tax: parseFloatToDollars(checkout.cart.totalTax ?? '0'),
            total: parseFloatToDollars(checkout.cart.total ?? '0'),
          },
          card: {
            expirationDate: parseInt(`${creditCard.month}${creditCard.year}`),
            present: 'N',
            token: {
              value:
                'i4go_uniqueid' in securedCHD ? securedCHD.i4go_uniqueid : '', //securedCHD.i4go_utoken,
            },
          },
          transaction: {
            source: '2',
            invoice: transactionInvoice,
            notes: JSON.stringify(shift4ProductDataCleaned).slice(
              0,
              2095, // 2096 character limit, Shift4 will probably throw an error if we exceed this.
            ),
            purchaseCard: {
              customerReference: formFields.email,
              ...(supportsAVS
                ? { destinationPostalCode: formFields.address.zip }
                : {}),
              productDescriptors: ['Ticket'],
            },
          },
          flow: paymentFlow,
        };

        // PROCESS PAYMENT
        let payment = await paymentMutation.mutateAsync(sale);
        const paymentResponse = transactionResponseHandler(payment);

        if (!paymentResponse.isSuccess || isShift4Error(payment)) {
          await kountClient.sendModeURequest(KOUNT.AUTHORIZATION_STATUS.D);

          voidTransaction.mutate({
            invoice: transactionInvoice,
            flow: paymentFlow,
          });
          const { longText, primaryCode, shortText } = paymentResponse;
          const errorMessage = `Sorry, we couldn't process your payment: ${shortText} - ${longText}  (CODE: ${primaryCode})`;
          const error = new Error(errorMessage);
          error.name = CheckoutErrors.PaymentError;

          newRelic.logCartAction(
            'PaymentFormViva - Payment Processing Error',
            'paymentMutation',
            { payment, paymentResponse },
          );

          handleError(error);
          throw error;
        } else {
          dispatch(
            actions.checkout.setPayment({
              payment,
              invoice: transactionInvoice,
              saleAK,
            }),
          );

          payment = payment.result[0].customer
            ? payment
            : {
                result: [
                  {
                    ...payment.result[0],
                    customer: {
                      firstName: formFields.firstName,
                      lastName: formFields.lastName,
                      addressLine1: formFields.address.street,
                      postalCode: formFields.address.zip,
                    },
                  },
                ],
              };

          newRelic.logCartAction(
            `PaymentFormViva - Payment Shift4 Success`,
            'paymentMutation',
            {
              payment: sanitizePaymentData(payment),
              invoice: transactionInvoice,
              saleAK,
            },
          );

          await closeOrderMutation.mutateAsync(
            {
              flow,
              saleAK,
              amount: totalWithTax,
              payment,
            },
            {
              onError: async (error, variables, context) => {
                try {
                  await voidTransaction.mutateAsync({
                    invoice: transactionInvoice,
                    flow,
                  });
                } catch (voidError) {
                  // Log void failure for investigation
                  Sentry.setTag('checkout_errors', 'void_transaction_failed');
                  Sentry.setTag('correlation_id', correlationId);
                  Sentry.captureException(voidError);

                  newRelic.logCartAction(
                    'PaymentFormViva - Void Transaction Failed',
                    'voidTransaction',
                    { invoice: transactionInvoice, flow, error: voidError },
                  );
                }
                const errorMessage = `Mutation Error. Transaction Invoice Voided: ${transactionInvoice}`;
                const _error = new Error(errorMessage);
                _error.name = CheckoutErrors.CloseOrderError;

                Sentry.setTag('checkout_errors', 'close_order_onError');
                Sentry.setTag('correlation_id', correlationId);
                Sentry.captureException(error);

                newRelic.logCartAction(
                  `PaymentFormViva - Close Order Error: ${errorMessage}`,
                  'closeOrderMutation',
                  {
                    flow,
                    saleAK,
                    amount: totalWithTax,
                    payment,
                    correlationId,
                  },
                );
                newRelic.logCartAction(
                  `PaymentFormViva - Close Order Error: ${errorMessage}`,
                  'voidTransaction',
                  { invoice: transactionInvoice, flow, saleAK },
                );

                throw _error;
              },
              onSuccess: async (closeOrder) => {
                if (isBosError(closeOrder)) {
                  try {
                    await voidTransaction.mutateAsync({
                      invoice: transactionInvoice,
                      flow,
                    });

                    try {
                      // Only attempt to send Mode U request if we have a transaction ID
                      if (kountClient.getTransactionId()) {
                        await kountClient.sendModeURequest(
                          KOUNT.AUTHORIZATION_STATUS.D,
                        );
                      } else {
                        console.log(
                          'Skipping Kount Mode U request - no transaction ID available',
                        );
                      }
                    } catch (kountModeUError) {
                      // Log but don't fail the flow
                      console.error(
                        'Non-fatal error updating Kount status:',
                        kountModeUError,
                      );
                    }
                  } catch (voidError) {
                    // Log void failure for investigation
                    Sentry.setTag(
                      'checkout_errors',
                      'void_transaction_failed_bos_error',
                    );
                    Sentry.setTag('correlation_id', correlationId);
                    Sentry.captureException(voidError);

                    newRelic.logCartAction(
                      'PaymentFormViva - Void Transaction Failed After BOS Error',
                      'voidTransaction',
                      { invoice: transactionInvoice, flow, error: voidError },
                    );
                  }
                  const errorMessage = `Reason: ${closeOrder.ERROR.TEXT}. CloseOrder (CODE: ${closeOrder.ERROR.CODE})`;
                  const error = new Error(errorMessage);
                  error.name = CheckoutErrors.CloseOrderError;

                  Sentry.setTag('checkout_errors', 'close_order_BOS_error');
                  Sentry.setTag('correlation_id', correlationId);
                  Sentry.captureException(error);

                  newRelic.logCartAction(
                    `PaymentFormViva - Close Order BOS Error: ${errorMessage}`,
                    'closeOrderMutation',
                    { closeOrder, saleAK, correlationId },
                  );

                  handleError(error);
                  throw error;
                }

                // Successful CloseOrder
                try {
                  // Only attempt to send the Mode U request if the kountClient has a transaction ID
                  if (kountClient.getTransactionId()) {
                    await kountClient.sendModeURequest(
                      KOUNT.AUTHORIZATION_STATUS.A,
                    );
                    console.log('Successfully sent Kount Mode U request');
                  } else {
                    console.log(
                      'Skipping Kount Mode U request - no transaction ID available',
                    );
                  }
                } catch (kountModeUError) {
                  // Log but don't fail the checkout if Kount update fails
                  console.error(
                    'Non-fatal error updating Kount status:',
                    kountModeUError,
                  );
                  newRelic.logCartAction(
                    'PaymentFormViva - Non-fatal Kount Mode U Error',
                    'kountModeU',
                    { error: kountModeUError, correlationId },
                  );
                }

                const selections = Object.keys(checkout.cart.selections).filter(
                  (key) => checkout.cart.selections[key]!.length > 0,
                );

                const ticketSelectionsDTO = selections.map((_selection) => {
                  const selection = _selection as Flow;
                  return {
                    selection,
                    data: [
                      ...(checkout.cart.selections[selection] ?? []),
                      ...Object.values(
                        checkout.cart.addOnSelections[selection] ?? {},
                      ).flat(),
                    ].map((item) => item as ShopCartSelection),
                  };
                });

                safeDataLayerPush({
                  reset: function (this: DataLayerContext) {
                    this.reset();
                  },
                });

                // Track add_payment_info event when payment form is submitted
                safeDataLayerPush({
                  event: 'add_payment_info',
                  currency: 'USD',
                  value: totalWithTax,
                  payment_type: 'credit_card',
                  items: ticketSelectionsDTO.flatMap((ticket) => {
                    return ticket?.data?.map((item) => {
                      const timeSlot =
                        'timeSlot' in item ? item.timeSlot?.[0] : undefined;
                      return {
                        item_id: getItemId(item),
                        item_name: getItemName(item),
                        item_category: ticket.selection,
                        price: (item.price / 100).toFixed(2),
                        quantity: getItemQuantity(item),
                        variant: timeSlot?.isDiscounted
                          ? 'evening'
                          : timeSlot?.isSunset
                          ? 'sunset'
                          : 'standard',
                      };
                    });
                  }),
                });

                // GA4 Event to capture ecommerce transaction/purchase data
                safeDataLayerPush({
                  event: 'purchase',
                  affiliation: location.hostname,
                  currency: 'USD',
                  payment_email: formFields.email,
                  tax: totalTax,
                  transaction_id: closeOrder.TRANSACTION.AK,
                  travel_date: closeOrder.SALE.DATE ?? new Date().toISOString(),
                  value: totalWithTax,
                  items: ticketSelectionsDTO.flatMap((ticket) => {
                    return ticket?.data?.map((item) => {
                      const timeSlot =
                        'timeSlot' in item ? item.timeSlot?.[0] : undefined;
                      const discountAmount =
                        'discount' in item && item.discount
                          ? item.discount
                          : '0.00';
                      return {
                        coupon: checkoutCouponCode,
                        item_category: ticket.selection,
                        item_name: getItemName(item),
                        item_id: getItemId(item),
                        price: (item.price / 100).toFixed(2),
                        quantity: getItemQuantity(item),
                        discount: discountAmount,
                        variant: timeSlot?.isDiscounted
                          ? 'evening'
                          : timeSlot?.isSunset
                          ? 'sunset'
                          : 'standard',
                      };
                    });
                  }),
                });

                if (
                  (checkout.cart.selections[Flow.CITY_PASS]?.length ?? 0) > 0
                ) {
                  const order = closeOrder as CityPassCloseOrderResponse;
                  const encodedTickets = putInArray(
                    order.ENCODEDTICKETLIST.ENCODEDTICKET,
                  );
                  const citypassVoucherCodes: CityPassVoucher[] =
                    encodedTickets.map((ticket) => ({
                      voucherCode: putInArray(
                        ticket.MEDIAIDENTIFIERLIST.MEDIAIDENTIFIER,
                      )[0].IDENTIFIER,
                      productCode: ProductToPackageCode[ticket.PRODUCT.CODE],
                    }));
                  dispatch(
                    actions.order.setCityPassBooklets(citypassVoucherCodes),
                  );
                  // Ready to ReadExternalPAckageByCode and launch into redeem.
                }

                dispatch(
                  actions.checkout.setTransactionAK(closeOrder.TRANSACTION.AK),
                );
                newRelic.logCartAction(
                  'PaymentFormViva - Close Order Success:',
                  'closeOrderMutation',
                  { saleAK: closeOrder.SALE.AK, closeOrder },
                );
              },
            },
          );
        }
      }

      removeObjectForLocalStorageKey(CART_LOCAL_STORAGE_KEY);

      dispatch(actions.wizard.clearCounter());

      const link = getLocalizedLink('/buy-tickets/confirmation/');
      navigate(link);
    } catch (error) {
      Sentry.setTag('checkout_errors', 'checkout_page');
      Sentry.setTag('correlation_id', correlationId);
      Sentry.captureException(error);

      if (error instanceof Error) {
        modal.setValue(error);
      } else {
        const genericError = new Error('An unexpected error occurred');
        genericError.name = CheckoutErrors.CheckoutError;
        modal.setValue(genericError);
      }

      const accountFormFieldIssues = validateAccountFormFields(
        formFields as FormFields,
      );

      // Log final comp addon state on error
      const finalCompAddons = Object.values(checkout.cart.addOnSelections)
        .flatMap((flowAddons) => Object.values(flowAddons))
        .flat()
        .filter((addon) => COMP_TICKETS.includes(addon.statGroup))
        .map((addon) => ({
          ak: addon.ak,
          name: addon.name ?? '',
          statGroup: addon.statGroup,
          quantity: addon.quantity,
          dateTime: addon.performances?.[0], // Use first performance as dateTime
        }));

      newRelic.logCartAction(
        'PaymentFormViva - Checkout Error',
        'checkoutTryCatch',
        {
          error,
          accountFormFieldIssues,
          correlationId,
          initialCompAddons,
          finalCompAddons,
          compAddonsLost: initialCompAddons.length > finalCompAddons.length,
          compAddonsDiff: initialCompAddons.filter(
            (initial) =>
              !finalCompAddons.some(
                (final) =>
                  final.ak === initial.ak &&
                  final.quantity === initial.quantity,
              ),
          ),
        },
      );

      setCheckoutError(error as Error);
      setShowLoadingModal(false);
      setIsCheckoutSubmitting(false);

      return;
    }

    if (copiedValues.optInMail) {
      try {
        mailchimpMutation.mutate({ email: copiedValues.email });
      } catch (e) {
        console.error(e);
      }
    }
  };

  const confirmButtonText = formState.isSubmitting
    ? translations?.submitting
    : isCityPASSRedemption
    ? translations?.redeem
    : translations?.confirm;

  const paymentForm = useMemo(
    () => (
      <form id="payment-form" onSubmit={handleSubmit(onSubmit)}>
        {!isCityPASSRedemption && (
          <Box sx={{ mb: 5 }}>
            <Input
              {...register('firstName', {
                required: 'Please enter your first name',
                minLength: {
                  value: 2,
                  message: 'First name must be at least 2 characters',
                },
                maxLength: {
                  value: 50,
                  message: 'First name cannot exceed 50 characters',
                },
              })}
              aria-describedby="first-name-error"
              aria-invalid={!!formState.errors.firstName}
              placeholder="Enter First Name"
              required
              sx={{
                'borderColor': formState.errors.firstName && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
              }}
              text="First Name"
            />
            {formState.errors.firstName && (
              <FormErrorText id="first-name-error">
                {formState.errors.firstName.message}
              </FormErrorText>
            )}

            <Input
              {...register('lastName', {
                required: 'Please enter your last name',
                minLength: {
                  value: 2,
                  message: 'Last name must be at least 2 characters',
                },
                maxLength: {
                  value: 50,
                  message: 'Last name cannot exceed 50 characters',
                },
              })}
              aria-describedby="last-name-error"
              aria-invalid={!!formState.errors.lastName}
              placeholder="Enter Last Name"
              required
              sx={{
                'borderColor': formState.errors.lastName && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
              }}
              text="Last Name"
            />
            {formState.errors.lastName && (
              <FormErrorText id="last-name-error">
                {formState.errors.lastName.message}
              </FormErrorText>
            )}

            <Input
              {...register('email', {
                required: 'Please enter a valid email address',
                pattern: {
                  value: /^\s*\S+@\S+\.\S+\s*$/,
                  message: 'Please enter a valid email address',
                },
              })}
              aria-describedby="email-error"
              aria-invalid={!!formState.errors.email}
              placeholder="Enter email address"
              required
              sx={{
                'borderColor': formState.errors.email && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
              }}
              text="Email"
            />
            {formState.errors.email && (
              <FormErrorText id="email-error">
                {formState.errors.email.message}
              </FormErrorText>
            )}

            <Input
              {...register('mobileNumber', {
                required: 'Please enter a valid mobile number',
                pattern: {
                  value:
                    /^\s*[+]?[0-9]{1,3}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,4}\s*$/,
                  message: 'Please enter a valid Mobile Number',
                },
                maxLength: {
                  value: 15,
                  message: 'Phone number cannot exceed 15 characters',
                },
              })}
              aria-describedby="mobile-number-error"
              aria-invalid={!!formState.errors.mobileNumber}
              maxLength={15}
              placeholder="Enter Mobile Number"
              required
              sx={{
                'borderColor': formState.errors.mobileNumber && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
                'width': '100%',
              }}
              text="Mobile Number"
            />
            {formState.errors.mobileNumber && (
              <FormErrorText id="mobile-number-error">
                {formState.errors.mobileNumber.message}
              </FormErrorText>
            )}

            {translations && isRinkRelated && (
              <Box sx={{ my: 1, paddingBottom: 3 }}>
                <Checkbox
                  {...register('optInSMS')}
                  icon={
                    <Icons.Snowflake
                      style={{
                        transform: 'translateY(15%)',
                        marginRight: '8px',
                      }}
                    />
                  }
                  sx={{
                    color: 'GRAY',
                  }}
                  text={translations?.optInSMS}
                />
              </Box>
            )}

            <Box sx={{ position: 'relative' }}>
              <Input
                {...register('creditCard.number', {
                  required: 'Please enter a valid credit card',
                  validate: (value) =>
                    validate.number(value.trim()).isValid ||
                    'Please enter a valid credit card',
                })}
                aria-describedby="credit-card-number-error"
                aria-invalid={!!formState.errors.creditCard?.number}
                placeholder="1234 1234 1234 1234"
                required
                sx={{
                  'borderColor':
                    formState.errors.creditCard?.number && '#EC104E',
                  '::placeholder': {
                    color: '#fff',
                  },
                }}
                text="Credit Card Number"
              />
              {formState.errors.creditCard?.number && (
                <FormErrorText id="credit-card-number-error">
                  {formState.errors.creditCard?.number.message}
                </FormErrorText>
              )}
              <Box sx={{ position: 'absolute', right: '0.5em', top: '0.75em' }}>
                <CreditCardIcon number={watch('creditCard.number')} />
              </Box>
            </Box>

            <Grid columns={3}>
              <Box>
                <Select
                  {...register('creditCard.month', {
                    deps: ['creditCard.year'],
                    required: 'Please select expiry month',
                    validate: (value) =>
                      parse(
                        `${getValues('creditCard.year')}-${value}`,
                        'yy-MM',
                        new Date(),
                      ) > new Date() || 'Please select a valid month',
                  })}
                  aria-describedby="credit-card-month-error"
                  aria-invalid={!!formState.errors.creditCard?.month}
                  required
                  sx={{
                    'borderColor':
                      formState.errors.creditCard?.month && '#EC104E',
                    '::placeholder': {
                      color: '#fff',
                    },
                  }}
                  text="Expiration Month"
                >
                  <option>–</option>
                  {Array.from({ length: 12 }).map((_, i) => (
                    <option key={`month-${i}`}>
                      {format(new Date(1970, i), 'MM')}
                    </option>
                  ))}
                </Select>
                {formState.errors.creditCard?.month && (
                  <FormErrorText id="credit-card-month-error">
                    {formState.errors.creditCard?.month.message}
                  </FormErrorText>
                )}
              </Box>
              <Box>
                <Select
                  {...register('creditCard.year', {
                    deps: ['creditCard.month'],
                    required: 'Please select expiry year',
                    validate: (value) =>
                      parse(
                        `${value}-${getValues('creditCard.month')}`,
                        'yy-MM',
                        new Date(),
                      ) > new Date() || 'Please select a valid year',
                  })}
                  aria-describedby="credit-card-year-error"
                  aria-invalid={!!formState.errors.creditCard?.year}
                  required
                  sx={{
                    'borderColor':
                      formState.errors.creditCard?.year && '#EC104E',
                    '::placeholder': {
                      color: '#fff',
                    },
                  }}
                  text="Expiration Year"
                >
                  <option>–</option>
                  {Array.from({ length: 10 }).map((_, i) => (
                    <option key={`year-${i}`}>
                      {format(new Date(i + getYear(new Date()), 0), 'yy')}
                    </option>
                  ))}
                </Select>
                {formState.errors.creditCard?.year && (
                  <FormErrorText id="credit-card-year-error">
                    {formState.errors.creditCard?.year.message}
                  </FormErrorText>
                )}
              </Box>
              <Box>
                <Input
                  {...register('creditCard.cvv', {
                    required: 'Required',
                    validate: (value) => {
                      const isValidLength =
                        validate.number(getValues('creditCard.number')).card
                          ?.type === 'american-express'
                          ? 4
                          : 3;

                      return (
                        value.trim().length === isValidLength ||
                        'Please enter security code'
                      );
                    },
                  })}
                  aria-describedby="credit-card-security-code-error"
                  aria-invalid={!!formState.errors.creditCard?.cvv}
                  placeholder="CVV"
                  required
                  sx={{
                    'borderColor':
                      formState.errors.creditCard?.cvv && '#EC104E',
                    '::placeholder': {
                      color: '#fff',
                    },
                  }}
                  text="Security Code"
                />
                {formState.errors.creditCard?.cvv && (
                  <FormErrorText id="credit-card-security-code-error">
                    {formState.errors.creditCard?.cvv.message}
                  </FormErrorText>
                )}
              </Box>
            </Grid>
            <Select
              {...register('address.country', {
                required: 'Please select country',
              })}
              aria-describedby="address-country-error"
              aria-invalid={!!formState.errors.address?.country}
              required
              sx={{
                'borderColor': formState.errors.address?.country && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
              }}
              text="Country"
            >
              <option value="USA">United States</option>
              {countriesQuery.isSuccess &&
                countriesQuery.data &&
                countriesQuery.data
                  .filter((country: DataProviderItem) => country.CODE !== 'USA')
                  .map((country: DataProviderItem) => (
                    <option key={country.CODE} value={country.CODE}>
                      {country.DESCRIPTION}
                    </option>
                  ))}
            </Select>
            {formState.errors.address?.country && (
              <FormErrorText id="address-country-error">
                {formState.errors.address?.country.message}
              </FormErrorText>
            )}

            <Input
              {...register('address.street', {
                required: 'Please enter address',
                minLength: {
                  value: 5,
                  message: 'Address must be at least 5 characters',
                },
                maxLength: {
                  value: 70,
                  message: 'Address cannot exceed 70 characters',
                },
              })}
              aria-describedby="billing-address-error"
              aria-invalid={!!formState.errors.address?.street}
              maxLength={70}
              placeholder="Enter Billing Address"
              required
              sx={{
                'borderColor': formState.errors.address?.street && '#EC104E',
                '::placeholder': {
                  color: '#fff',
                },
              }}
              text="Billing Address"
            />
            {formState.errors.address?.street && (
              <FormErrorText id="billing-address-error">
                {formState.errors.address?.street.message}
              </FormErrorText>
            )}
            <Grid
              columns={
                watch('address.country', 'USA') === 'USA'
                  ? [1, null, '2fr 2.2fr 0.8fr']
                  : [1, null, '3fr 2fr']
              }
            >
              <Box>
                <Input
                  {...register('address.city', {
                    required: 'Please enter city',
                    minLength: {
                      value: 2,
                      message: 'City name must be at least 2 characters',
                    },
                    maxLength: {
                      value: 50,
                      message: 'City name cannot exceed 50 characters',
                    },
                  })}
                  aria-describedby="billing-address-city-error"
                  aria-invalid={!!formState.errors.address?.city}
                  placeholder="Enter City"
                  required
                  sx={{
                    'borderColor': formState.errors.address?.city && '#EC104E',
                    '::placeholder': {
                      color: '#fff',
                    },
                  }}
                  text="City"
                />
                {formState.errors.address?.city && (
                  <FormErrorText id="billing-address-city-error">
                    {formState.errors.address?.city.message}
                  </FormErrorText>
                )}
              </Box>
              {watch('address.country', 'USA') === 'USA' && (
                <Box sx={{ margin: '0 0 32px 0' }}>
                  <Select
                    {...register('address.state', {
                      deps: ['address.country'],
                      required: 'Please select state',
                      validate: (value) =>
                        ((getValues('address.country') ?? 'USA') === 'USA' &&
                          value !== 'None') ||
                        'Please select state',
                    })}
                    aria-describedby="billing-address-state-error"
                    aria-invalid={!!formState.errors.address?.state}
                    required
                    sx={{
                      'borderColor':
                        formState.errors.address?.state && '#EC104E',
                      '::placeholder': {
                        color: '#fff',
                      },
                    }}
                    text="State"
                  >
                    <option value="None">–</option>
                    {statesQuery.isSuccess &&
                      statesQuery.data &&
                      statesQuery.data
                        .sort((a: StateItem, b: StateItem) =>
                          a.DESCRIPTION.localeCompare(b.DESCRIPTION),
                        )
                        .map((state: DataProviderItem) => (
                          <option key={state.CODE} value={state.CODE}>
                            {state.DESCRIPTION}
                          </option>
                        ))}
                  </Select>
                  {formState.errors.address?.state && (
                    <FormErrorText id="billing-address-state-error">
                      {formState.errors.address?.state.message}
                    </FormErrorText>
                  )}
                </Box>
              )}
              <Box>
                <Input
                  {...register('address.zip', {
                    required: 'Please enter zip code',
                  })}
                  aria-describedby="billing-address-zip-code-error"
                  aria-invalid={!!formState.errors.address?.zip}
                  placeholder="Enter ZIP Code"
                  required
                  sx={{
                    'borderColor': formState.errors.address?.zip && '#EC104E',
                    '::placeholder': {
                      color: '#fff',
                    },
                  }}
                  text="ZIP Code"
                />
                {formState.errors.address?.zip && (
                  <FormErrorText id="billing-address-zip-code-error">
                    {formState.errors.address?.zip.message}
                  </FormErrorText>
                )}
              </Box>
            </Grid>
            <Fragment>
              <Flex
                sx={{
                  my: 1,
                  padding: 3,
                  paddingBottom: 0,
                  marginLeft: '-16px',
                  justifyContent: 'flex-start',
                  width: '100%',
                }}
              >
                <Box data-testid="tos-checkbox">
                  <Checkbox
                    {...register('tos', {
                      required: isRinkRelated
                        ? 'Please accept The Rink Terms & Conditions to continue'
                        : 'Please accept the Terms & Conditions to continue',
                    })}
                    required
                    sx={{
                      color: 'text',
                    }}
                    text="I accept and agree to the "
                  />
                </Box>
                <Box>
                  <Link
                    href={
                      isRinkRelated
                        ? '/documents/TR23-003-TheRinkWaiver-08292023.pdf'
                        : '/documents/RC_TermsAndConditions.pdf'
                    }
                    sx={{
                      textDecoration: 'underline',
                      fontSize: 'inherit',
                      fontWeight: '500',
                    }}
                    target="_blank"
                  >
                    {isRinkRelated
                      ? 'Rink Terms & Conditions'
                      : 'Terms & Conditions'}
                  </Link>
                </Box>
              </Flex>
              <Flex>
                <Box>
                  {formState.errors.tos && (
                    <FormErrorText id="tos-error">
                      {formState.errors.tos.message}
                    </FormErrorText>
                  )}
                </Box>
              </Flex>
              {isRinkRelated && (
                <Box
                  data-testid="nonrefundable-checkbox"
                  sx={{
                    my: 1,
                    padding: 3,
                    marginLeft: '-16px',
                  }}
                >
                  <Checkbox
                    {...register('nonrefundable', {
                      required:
                        'Please accept the non-refundable sales disclaimer',
                    })}
                    required
                    sx={{
                      color: 'text',
                    }}
                    text="I understand that all ticket sales are final and non-refundable."
                  />
                  <Box>
                    {formState.errors.nonrefundable && (
                      <FormErrorText id="nonrefundable-error">
                        {formState.errors.nonrefundable.message}
                      </FormErrorText>
                    )}
                  </Box>
                </Box>
              )}
            </Fragment>
            <Box sx={{ my: 1, py: 3 }}>
              <Checkbox
                {...register('optInMail')}
                sx={{
                  color: 'GRAY',
                }}
                text={translations?.newsLetterLabel ?? ''}
              />
              <Text>{translations?.newsLetterDescription ?? ''}</Text>
            </Box>
          </Box>
        )}
        <Flex sx={{ justifyContent: 'flex-end', mb: 4 }}>
          <Turnstile
            onError={() => {
              setTurnstileValid(false);
              const error = new Error('Siteverify Error');
              error.name = CheckoutErrors.CheckoutError;
              Sentry.captureException(error);
              setCheckoutError(error);
            }}
            onExpire={() => turnstileRef.current?.reset()}
            onSuccess={setTurnstileToken}
            options={{
              appearance: 'interaction-only',
              refreshExpired: 'manual',
              theme: 'light',
            }}
            ref={turnstileRef}
            siteKey={
              process.env.CF_TURNSTILE_SITEKEY || '1x00000000000000000000AA'
            }
          />
        </Flex>
        <Flex sx={{ flexDirection: 'column', gap: 3 }}>
          <Flex
            sx={{
              justifyContent: 'space-between',
              flexDirection: ['column', null, null, 'row'],
              alignItems: ['stretch', null, null, 'flex-start'],
            }}
          >
            {!isCityPASSRedemption && (
              <Box sx={{ mb: [4, null] }}>
                <RequiredLabel labelText="Required*" />
              </Box>
            )}
            <SubmitButton
              disabled={
                formState.isSubmitting ||
                closeOrderMutation.isSuccess ||
                !turnstileValid
              }
              text={confirmButtonText ?? 'Confirm'}
            />
          </Flex>
        </Flex>
      </form>
    ),
    [handleSubmit, onSubmit],
  );

  return (
    <ErrorBoundary
      fallbackRender={({ error, resetErrorBoundary }) => (
        <Fragment>
          {paymentForm}
          <BuyTicketsModal
            data={{ page: { checkoutModalCard: true } }}
            error={error}
            errorBlock={checkoutErrorBlock}
            isOpen={true}
            onClose={() => {
              resetErrorBoundary();
              setCheckoutError(null);
              setCheckoutErrorBlock(undefined);
            }}
            shopCartSelections={shopCartSelections}
          />
        </Fragment>
      )}
      onReset={() => {
        modal.setIsOpen(false);
        modal.setValue(undefined);
        setCheckoutError(null);
        setCheckoutErrorBlock(undefined);
      }}
    >
      <Fragment>
        {paymentForm}
        <BuyTicketsModal
          data={{ page: { checkoutModalCard: true } }}
          error={modal.value || checkoutError || undefined}
          errorBlock={checkoutErrorBlock}
          isOpen={modal.isOpen || !!checkoutError}
          onClose={() => {
            modal.setIsOpen(false);
            setCheckoutError(null);
            setCheckoutErrorBlock(undefined);
          }}
          onCloseFinished={() => {
            modal.setValue(undefined);
            setCheckoutError(null);
            setCheckoutErrorBlock(undefined);
          }}
          shopCartSelections={shopCartSelections}
        />
      </Fragment>
    </ErrorBoundary>
  );
};
