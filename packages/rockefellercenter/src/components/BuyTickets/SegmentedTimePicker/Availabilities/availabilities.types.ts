import type { PerformanceResponseObject } from '../../../../services/viva/types';

export interface Price {
  label: string;
  value: number | string;
}

type Attraction = 'tor' | 'the-rink';
export interface BundleAvailability {
  start: Record<Attraction, number>;
  prices: Record<Attraction, Price[]>;
  availability: Record<Attraction, number | string>;
  isBundle: boolean;
}

export interface Product {
  ak: string;
  price: string;
}
export interface Availability {
  ak: string;
  start: number;
  startRaw: PerformanceResponseObject['RAW_TIME'];
  endRaw: PerformanceResponseObject['RAW_TIME'];
  isPopular: boolean;
  availability: number | string;
  prices: Price[];
  products: Product[];
  isBundle: boolean;
  isDiscounted?: boolean;
  isSunset?: boolean;
  isFireworks?: boolean;
  discountedProducts?: Product[];
  priceTableAK: string;
  timeRaw: string;
}

export interface AvailabilitySet {
  availabilities: Availability[] | BundleAvailability[];
  isBundle: boolean;
}
export interface AvailabilitiesProps {
  availabilities: AvailabilitySet;
  isToday: boolean;
}

export interface AvailabilitiesListProps {
  availabilitiesList: Availability[] | BundleAvailability[];
  label: string;
  isBundle: boolean;
}
