import {
  format,
  getHours,
  addMinutes,
  addHours,
  startOfDay,
  formatISO9075,
} from 'date-fns';

import {
  DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK,
  FLOW_PRODUCT_TITLES_BY_AK,
  Flow,
} from '../../../../services/viva/constants';
import { putInArray } from '../../../../services/viva/utils';
import { useFindAllProductByStatGroup } from '../../../../services/viva';

import type { BOSProduct } from '../../../../services/viva/types';
import type {
  Availability,
  AvailabilitySet,
  BundleAvailability,
  Price,
  Product,
} from './availabilities.types';
import type { PerformanceResponseObject } from '../../../../services/viva/types/flows';

const MORNING_FLOOR = 7;
const AFTERNOON_FLOOR = 12;
const EVENING_FLOOR = 17;
const CLOSING_TIME = 22;

const theRink = 'the-rink';
const tor = 'tor';

export type SegmentKey = 'MORNING' | 'AFTERNOON' | 'EVENING';

const generateRandomAvailability = () => Math.floor(Math.random() * 100 + 1);

export const formatAvailabilityTime = (timestamp: number) =>
  format(timestamp, 'h:mm a');

const getSegmentKey = (timestamp: number): SegmentKey | null => {
  const hours: number = getHours(timestamp);

  if (hours >= MORNING_FLOOR && hours < AFTERNOON_FLOOR) return 'MORNING';
  else if (hours >= AFTERNOON_FLOOR && hours < EVENING_FLOOR)
    return 'AFTERNOON';
  else if (hours >= EVENING_FLOOR && hours < CLOSING_TIME) return 'EVENING';

  return null;
};

type SegmentedAvailabilities = Record<
  SegmentKey,
  Availability[] | BundleAvailability[]
>;

export const getSegmentedAvailabilities = (
  availabilities: Availability[],
): SegmentedAvailabilities => {
  const emptySegments: SegmentedAvailabilities = {
    MORNING: [],
    AFTERNOON: [],
    EVENING: [],
  };

  try {
    const av = availabilities.reduce((acc, availability) => {
      const daySegmentKey: SegmentKey | null = getSegmentKey(
        availability.start,
      );
      if (daySegmentKey)
        (acc[daySegmentKey] as Availability[]).push(availability);
      return acc;
    }, emptySegments);
    return av;
  } catch (e) {
    console.log(e);
    return emptySegments;
  }
};

export const getSegmentedBundleAvailabilities = (
  availabilities: BundleAvailability[],
): SegmentedAvailabilities =>
  availabilities.reduce(
    (acc, availability) => {
      const daySegmentKey: SegmentKey | null = getSegmentKey(
        // Beware! I assume here that tor is the earliest attraction, but a better way is to find out the earliest
        // attraction in the object and use that as the key.
        availability.start.tor,
      );
      if (daySegmentKey)
        (acc[daySegmentKey] as BundleAvailability[]).push(availability);
      return acc;
    },
    { MORNING: [], AFTERNOON: [], EVENING: [] },
  );

type DiscountedData = {
  discountedPerformances?: { AK: string }[];
  discountedProducts?: BOSProduct[];
};

type SpecialEventPerformances = {
  ID?: string;
  AK: string;
  REACHABLE: string;
  TRANSFERRABLE: string;
};

export const getAvailabilities = (
  data: PerformanceResponseObject[],
  flow?: Flow,
  discountedData?: DiscountedData,
  sunsetPerformances?: SpecialEventPerformances[],
  fireworksPerformances?: SpecialEventPerformances[],
): AvailabilitySet => {
  const availabilities: Availability[] = data?.map(
    (performance: PerformanceResponseObject) => {
      // WARN: Shouldn't use due to timezone issues
      const time = new Date(performance.TIME);
      const timestamp = time.valueOf();

      const isDiscounted = discountedData?.discountedPerformances?.some(
        (discountedPerf) => discountedPerf?.AK === performance.AK,
      );
      const isSunset = sunsetPerformances?.some(
        (sunsetPerf) => sunsetPerf?.AK === performance.AK,
      );
      const isFireworks = fireworksPerformances?.some(
        (fireworksPerf) => fireworksPerf?.AK === performance.AK,
      );

      // Get the appropriate products based on whether this performance is discounted
      const performanceProducts = performance.PRODUCTLIST
        ? putInArray(performance.PRODUCTLIST?.PRODUCT)
        : [];

      // If the performance is discounted, get both original and discounted products
      const discountedProducts =
        isDiscounted && discountedData?.discountedProducts
          ? putInArray(discountedData.discountedProducts)
          : [];

      const prices: Price[] =
        flow && FLOW_PRODUCT_TITLES_BY_AK[flow]
          ? performanceProducts
              .map((product) => {
                const label =
                  FLOW_PRODUCT_TITLES_BY_AK[flow]?.[product.AK] || '';
                // Handle both string prices and complex price objects
                let value = '';

                if (isDiscounted) {
                  const keys = Object.values(
                    DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK[flow] ?? {},
                  );

                  if (keys.includes(product.AK)) {
                    return { label: '', value: '' };
                  }

                  value = product?.PRICE;
                } else {
                  value =
                    typeof product.PRICE === 'string'
                      ? product.PRICE
                      : product.PRICE?.GROSS || '';
                }
                return { label, value };
              })
              .filter((price) => price.label !== '')
              .filter(
                (price, index, self) =>
                  index === self.findIndex((p) => p.label === price.label),
              )
              .sort((a, b) => {
                const order = ['Child', 'Senior', 'Adult'];
                return order.indexOf(a.label) - order.indexOf(b.label);
              })
          : [];

      const products: Product[] = performanceProducts.map((product) => ({
        ak: product.AK,
        price:
          typeof product.PRICE === 'string'
            ? product.PRICE
            : product.PRICE?.GROSS || '',
      }));

      const discountedProductsList: Product[] = discountedProducts.map(
        (product) => ({
          ak: product.AK,
          price:
            typeof product.PRICE === 'string'
              ? product.PRICE
              : product.PRICE?.GROSS || '',
        }),
      );

      // Update discountedProductsList prices with matching prices from products
      // We want the AK of the discounted product but the VARIABLE price that is returned
      // from the performance products.
      discountedProductsList.forEach((discountedProduct) => {
        const matchingProduct = products.find(
          (product) => product.ak === discountedProduct.ak,
        );
        if (matchingProduct) {
          discountedProduct.price = matchingProduct.price;
        }
      });

      // Parse the end time from ENDTIME
      const endTimePart = performance.ENDTIME;
      const [endHours, endMinutes] = endTimePart?.split(':').map(Number) || [
        0, 0,
      ];
      const endRaw = {
        hours: endHours,
        minutes: endMinutes.toString(),
        meridian: endHours >= 12 ? 'PM' : 'AM',
      };

      return {
        ak: performance.AK,
        start: timestamp,
        startRaw: performance.RAW_TIME,
        endRaw,
        timeRaw: performance.TIME,
        isPopular: true,
        availability: performance.AVAILABILITY.AVAILABLE,
        prices,
        products,
        isDiscounted,
        isSunset,
        isFireworks,
        discountedProducts: discountedProductsList,
        // make sure to always add this flag. This flag saves us from complex logic in the component
        isBundle: false,
        priceTableAK: performance.PRICETABLEAK,
      };
    },
  );
  return { availabilities, isBundle: false };
};

export const generateAvailabilities = (): AvailabilitySet => {
  const theDateTime = new Date(2022, 6, 1, MORNING_FLOOR);
  const theLimit = new Date(2022, 6, 1, CLOSING_TIME);
  let theCursorDateTime = theDateTime;
  const theTimestamps: number[] = [];
  while (theCursorDateTime < theLimit) {
    theTimestamps.push(theCursorDateTime.valueOf());
    theCursorDateTime = addMinutes(theCursorDateTime, 10);
  }

  const availabilities: Availability[] = theTimestamps.map((timestamp) => ({
    ak: '',
    start: timestamp,
    isPopular: true,
    availability: 100,
    prices: [
      { label: 'Adult & Child', value: 3500 },
      { label: 'Tot', value: 1400 },
    ],
    products: [],
    // make sure to always add this flag. This flag saves us from complex logic in the component
    isBundle: false,
    priceTableAK: '',
    startRaw: { hours: 0, minutes: '0', meridian: 'am' },
    endRaw: { hours: 0, minutes: '0', meridian: 'am' },
    timeRaw: `${timestamp}`,
  }));
  return { availabilities, isBundle: false };
};
/*
  This function creates dummy data for the bundle availabilities.
  Please remove it once the backend is ready.
*/
export const generateBundleAvailabilities = (): AvailabilitySet => {
  const availabilities: BundleAvailability[] = [];
  const dayStart = startOfDay(Date.now()).valueOf();
  const serviceStarts = addHours(dayStart, MORNING_FLOOR);
  const serviceEnds = addHours(dayStart, CLOSING_TIME);

  // create a set of availabilities from opening to closing time
  for (
    let cursor = serviceStarts;
    cursor < serviceEnds;
    cursor = addMinutes(cursor, 15)
  ) {
    const torStart = cursor;
    const theRinkStart = addMinutes(cursor, 30);

    if (theRinkStart > serviceEnds) break;

    const start = {
      [theRink]: theRinkStart.valueOf(),
      [tor]: torStart.valueOf(),
    };
    const samplePrices: Price[] = [
      { label: 'Adult', value: 30 },
      { label: 'Tot', value: 12 },
    ];
    const prices = { [theRink]: samplePrices, [tor]: samplePrices };
    const availability = {
      [theRink]: generateRandomAvailability(),
      [tor]: generateRandomAvailability(),
    };
    availabilities.push({
      start,
      prices,
      availability,
      isBundle: true, // this is the secret sauce from having complex logic to separate bundle and non-bundle availabilities
    });
  }
  return { availabilities, isBundle: true };
};
