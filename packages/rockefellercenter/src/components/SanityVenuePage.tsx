import React from 'react';
import {
  Box,
  Container,
  type CuratorFeedName,
  Faqs,
  Flex,
  SecondaryMenuBar,
  Section,
  TishmanThemeName,
} from '@tishman/components';

import { Layout } from '../layouts';
import ShareYourExperienceBlock from '../blocks/ShareYourExperienceBlock';
import { PrivateEventsNews } from '../blocks/PrivateEvents/PrivateEventsNews';
import { PrivateEventsGallery } from '../blocks/PrivateEvents/Gallery/PrivateEventsGallery';
import { TPrivateEventsGalleryCarouselItem } from '../blocks/PrivateEvents/Gallery/useCarousel';
import { PrivateEventsImageCallout } from '../blocks/PrivateEvents/PrivateEventsImageCallout';
import { mergeFaqsByCategory } from '../utils/mergeFaqsByCategory';
import { PageConfig } from '../PageConfig';
import { PrivateEventsVenueHero } from '../blocks/PrivateEvents/Hero/PirvateEventsVenueHero';
import { isTruthy } from '../utils';

import type { Block } from '@sanity/block-content-to-react';

type TSanityVenuePage = {
  data: Queries.SanityVenueDetailPageTemplate;
  pageConfig: PageConfig;
};

export const SanityVenuePage = ({ data, pageConfig }: TSanityVenuePage) => {
  const mergedFaqs = mergeFaqsByCategory(data?.faqs ?? []);

  const pdfsToLinks = (
    pdfs: readonly Queries.Maybe<Queries.SanityPdfDownloadOrUrlType>[],
  ) => {
    return pdfs.filter(isTruthy).map((pdf) => {
      if ('caption' in pdf) {
        return {
          text: pdf.caption || '',
          url: pdf.url || '',
        };
      }

      return {
        text: pdf.pdfButtonText || '',
        url: pdf.pdfFile?.asset?.url || '',
      };
    });
  };

  return (
    <Layout theme="Rock Center Black">
      {data?.subNavigationFeatureFlag && (
        <React.Fragment>
          <Box sx={{ width: '100%', height: '32px' }} />
          <SecondaryMenuBar
            cta={pageConfig.cta}
            links={data?.subnavLinks
              ?.filter((link) => link && link.url && link.caption)
              .map((link) => ({
                label: link!.caption || '',
                url: link!.url || '',
              }))}
            sticky
            threshold={0.5}
            title="Explore Private Events"
          />
        </React.Fragment>
      )}
      <Box sx={{ width: '100%', height: '16px' }} />
      {data?.heroFeatureFlag && (
        <Section
          id="details"
          sx={{ marginTop: ['24px', 0], paddingBottom: [0, 0, '64px'] }}
          theme="Rock Center White"
        >
          <PrivateEventsVenueHero
            image={(data?.heroImage ?? {}) as Queries.SanitySimpleImageType}
            pageName={`Private Venue ${data?.seo?.metaTitle ?? ''}`}
            textSection={
              (data?.heroTextSection ?? {}) as Queries.SanityHeroTextSection
            }
            venueDetails={[
              {
                content: (data?.capacities ?? []) as Queries.SanityBlock[],
                links: pdfsToLinks(data?.capacityPDFs ?? []),
              },
              {
                content: (data?.catering ?? []) as Queries.SanityBlock[],
                links: pdfsToLinks(data?.cateringPDFs ?? []),
              },
              {
                content: (data?.additionalSpaces ??
                  []) as Queries.SanityBlock[],
              },
            ]}
            venueDetailsFeatureFlag
          />
        </Section>
      )}
      {data?.galleryFeatureFlag && (
        <PrivateEventsGallery
          items={
            (data?.gallery?.items ??
              []) as unknown as TPrivateEventsGalleryCarouselItem[]
          }
          title={data?.gallery?.title ?? ''}
        />
      )}

      {data?.faqsFeatureFlag && mergedFaqs.length > 0 && (
        <Section id="faqs" theme="Rock Center White">
          <Container sx={{ px: [3, 4], py: 0, pb: ['24px', 7], width: '100%' }}>
            <Flex
              sx={{
                flexDirection: ['column', null, 'row'],
                justifyContent: 'space-between',
                maxWidth: '820px',
                width: '100%',
              }}
            >
              <Faqs
                faqs={mergedFaqs as unknown as Block[]}
                title="FAQs"
                variant="private-events"
              />
            </Flex>
          </Container>
        </Section>
      )}
      {data?.newsFeatureFlag && (
        <PrivateEventsNews
          items={Array.from(data?.news ?? []).filter(
            (item): item is Queries.SanityStory => !!item,
          )}
          paddingBottom="22px"
          theme={
            (data?.newsColorSchema ?? 'Rock Center Black') as TishmanThemeName
          }
        />
      )}
      {data?.socialMediaFeatureFlag && (
        <Section id="news" sx={{ paddingTop: [0, '32px'] }} theme="Rock Center">
          <ShareYourExperienceBlock
            bg="white"
            carouselVariant="private-events"
            desktopCardsPaddingLeft="calc((100vw - 1200px) / 2 + 16px)"
            feedName={(data?.feedName ?? 'Private Events') as CuratorFeedName}
            hashTags={[]}
            id="gallery"
            mobileContainerFullWidth
            theme="Rock Center Cream"
            title="Interested in hosting your event at Rockefeller Center? Take a look at some of our recent highlights."
          />
        </Section>
      )}
      {data?.imageCalloutFeatureFlag && data?.imageCallout && (
        <PrivateEventsImageCallout {...data.imageCallout} />
      )}
    </Layout>
  );
};
