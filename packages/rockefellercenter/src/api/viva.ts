/* eslint-disable @typescript-eslint/no-explicit-any */
import '../../sentry.server';
import { createClient } from 'soap';
import format from 'xml-formatter';
import * as Sentry from '@sentry/node';

import {
  BOS_TOR_PASSWORD,
  BOS_TOR_SESSION_ID,
  BOS_API_URL,
  BOS_TOR_USERNAME,
  BOS_TOR_WORKSTATION_AK,
  BOS_RINK_USERNAME,
  BOS_RINK_PASSWORD,
  BOS_RINK_WORKSTATION_AK,
  BOS_RINK_SESSION_ID,
} from '../environment';
import {
  Flow,
  VivaAPIOperation,
  VivaAPIType,
} from '../services/viva/constants';
import { isRinkOrRinkMembershipRelated } from '../services/viva/utils/is-rink-or-rink-membership-related';

const baseWsdlUrl = `${BOS_API_URL}/wsdl/`;
export interface ApiResponse {
  status: { code: number; text: string };
  operation: string;
  [key: string]: any;
}
const mapResponse = (response: any): ApiResponse => {
  const {
    return: {
      operation,
      ERROR: { CODE, TEXT, TYPE } = { CODE: 'N/A', TEXT: 'N/A', TYPE: 'N/A' },
      ...data
    },
  } = response;
  const mappedResponse = {
    status: { code: parseInt(CODE), text: TEXT },
    ERROR: { CODE, TEXT, TYPE },
    ...data,
  };

  // Creates Sentry message for the Viva API 500 errors Alert
  if (mappedResponse.status.code >= 500 && mappedResponse.status.code < 600) {
    Sentry.withScope((scope) => {
      scope.setTag('api_error', 'viva_500');
      scope.setTag('error_code', CODE);
      scope.setTag('error_type', TYPE);
      scope.setTag('api_endpoint', operation);
      scope.setExtra('error_text', TEXT);
      scope.setExtra('response_data', data);
      Sentry.captureMessage(`Viva API 500 Error: ${TEXT}`, 'error');
    });
  }

  return mappedResponse;
};
export const authSoapClient = async (isRink: boolean, sessionID: string) => {
  const clientCredentials = {
    AUserName: isRink ? BOS_RINK_USERNAME : BOS_TOR_USERNAME,
    APassword: isRink ? BOS_RINK_PASSWORD : BOS_TOR_PASSWORD,
    AWorkstationAK: isRink ? BOS_RINK_WORKSTATION_AK : BOS_TOR_WORKSTATION_AK,
  };
  createClient(
    `${baseWsdlUrl}IWsAPIUser?OvwSessionId=${sessionID}`,
    {
      endpoint: `${BOS_API_URL}/soap/IWsAPIUser?OvwSessionId=${sessionID}`,
    },
    async (err, client) => {
      if (err) {
        console.log('Client Create Error: ', err);
      } else if (client) {
        console.log('calling UserLogin');
        await client[`IWsAPIUserservice`][`IWsAPIUserPort`][`UserLogin`](
          clientCredentials,
          (err: any, response: any) => {
            console.log('UserLogin promise resolved');
            if (err) {
              console.log('UserLogin Error', err);
            } else if (response) {
              console.log('UserLogin response');
              console.log(mapResponse({ operation: 'UserLogin', ...response }));
            }
          },
        );
      }
    },
  );
};

export interface SoapRequestParams<T = any> {
  type: VivaAPIType;
  operation: VivaAPIOperation;
  args?: T | null;
  bosAccount?: Flow;
}
/**
 *
 * @param type SOAP WSDL value that is used in setting the XML Request Definition, example'APIUser';
 * @param operation SOAP operation value for the type, example 'FindAllProducts';
 * @returns JSON response
 */
export const soapRequest = async ({
  type,
  operation,
  args,
  bosAccount = Flow.TOR_GA,
}: SoapRequestParams) => {
  const isRink = () => isRinkOrRinkMembershipRelated(bosAccount);
  const sessionID = isRink() ? BOS_RINK_SESSION_ID : BOS_TOR_SESSION_ID;
  try {
    return new Promise<{ status: { code: number } }>((resolve, reject) => {
      const makeRequest = (client: any) => {
        client[`${type}service`][`${type}Port`][operation](
          args,
          (err: any, response: any) => {
            // Uncomment below to see the JSON version of the request
            // if (type === VivaAPIType.order) {
            //   logJsonRequest(args, operation, type);
            // }

            if (err) {
              console.log('Error making soap request:', err);
              if (err.response.data.toLowerCase().includes('login required')) {
                console.log('calling authSoapClient from soapRequest');
                authSoapClient(isRink(), sessionID).then(() =>
                  makeRequest(client),
                );
              } else {
                reject(err);
              }
            } else {
              const mappedResponse = mapResponse({
                operation,
                ...response,
              });

              if (
                mappedResponse.status.code === 401 ||
                mappedResponse.status.code === 601
              ) {
                console.log('calling authSoapClient from makeRequest');
                authSoapClient(isRink(), sessionID).then(() =>
                  makeRequest(client),
                );
              } else {
                console.log('resolving from makeRequest');
                // Uncomment below to see the XML version of the request
                // if (type === VivaAPIType.performance) {
                //   logXMLRequestAndResponse(client, type, response, {
                //     operation: 'GetDaysAvailability',
                //     event: 'TOR.EVN33',
                //   });
                // }

                resolve(mappedResponse);
              }
            }
          },
        );
      };

      createClient(
        `${baseWsdlUrl}${type}?OvwSessionId=${sessionID}`,
        {
          endpoint: `${BOS_API_URL}/soap/${type}?OvwSessionId=${sessionID}`,
          namespaceArrayElements: false,
        },
        (err, client) => {
          if (err) {
            console.log(err);
            reject(err);
          } else if (client) {
            makeRequest(client);
          }
        },
      );
    });
  } catch (error) {
    console.error('Error making soap request:', error);
    Sentry.captureException(error);
    throw error;
  } finally {
    await Sentry.flush(2000);
  }
};

const logJsonRequest = (args: any, operation: any, type: any) => {
  console.log(`TYPE: ${type}, operation: ${operation}`);
  console.log('ARGS:');
  console.dir(args, {
    showHidden: false,
    depth: null,
    colors: true,
  });
  console.log('---------------------------------------------');
};

const logXMLRequestAndResponse = (
  client: any,
  type: any,
  response: any,
  filter?: { operation?: string; event?: string },
) => {
  if (filter) {
    if (
      filter.operation &&
      client.lastRequest.indexOf(filter.operation) === -1
    ) {
      return;
    }
    if (filter.event && client.lastRequest.indexOf(filter.event) === -1) {
      return;
    }
  }

  console.log('=====================');
  console.log('CLIENT LAST REQUEST:');
  console.log('=====================');

  // Format the XML before logging
  const formattedXml = format(client.lastRequest, {
    indentation: '  ',
    collapseContent: true,
    lineSeparator: '\n',
  });

  console.log(formattedXml);
  console.log('======================');
  console.log(`${type} RESPONSE`);
  console.log('======================');
  console.dir(response, {
    showHidden: false,
    depth: null,
    colors: true,
  });
};
