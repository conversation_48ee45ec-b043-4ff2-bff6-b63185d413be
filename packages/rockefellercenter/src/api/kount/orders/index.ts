import axios from 'axios';

import { KOUNT_API_URL } from '../../../environment';

import type { GatsbyFunctionRequest, GatsbyFunctionResponse } from 'gatsby';

export default async (
  req: GatsbyFunctionRequest,
  res: GatsbyFunctionResponse,
) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const { riskInquiry } = req.query;
    const token = req.headers.authorization;
    const sessionId = req.headers.sessionid;

    if (!token) {
      return res.status(400).json({ error: 'Authorization token is required' });
    }

    const kountUrl = `${KOUNT_API_URL}/commerce/v2/orders${
      riskInquiry ? '?riskInquiry=true' : ''
    }`;

    const response = await axios.post(kountUrl, req.body, {
      headers: {
        'Authorization': token,
        'Content-Type': 'application/json',
        'SessionId': sessionId,
      },
    });

    console.log('Kount API response:', {
      status: response.status,
      hasOrderId: !!response.data?.order?.orderId,
      hasDecision: !!response.data?.order?.riskInquiry?.decision,
      responsePreview: JSON.stringify(response.data).substring(0, 200) + '...',
    });

    // Return the Kount response to the client
    return res.status(response.status).json(response.data);
  } catch (error: unknown) {
    const axiosError = error as {
      message: string;
      response?: { status?: number; data?: unknown };
    };

    console.error('Error from Kount API:', axiosError.message);
    console.error('Error details:', axiosError.response?.data);

    return res.status(axiosError.response?.status || 500).json({
      error: 'Failed to connect to Kount API',
      details: axiosError.response?.data || axiosError.message,
    });
  }
};
