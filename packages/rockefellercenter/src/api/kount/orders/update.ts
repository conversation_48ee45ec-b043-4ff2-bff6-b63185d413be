import axios from 'axios';

import { KOUNT_API_URL } from '../../../environment';

import type { GatsbyFunctionRequest, GatsbyFunctionResponse } from 'gatsby';

export default async (
  req: GatsbyFunctionRequest,
  res: GatsbyFunctionResponse,
) => {
  if (req.method !== 'PATCH') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const { orderId } = req.query;
    const token = req.headers.authorization;
    const sessionId = req.headers.sessionid;

    if (!token) {
      return res.status(400).json({ error: 'Authorization token is required' });
    }

    if (!orderId) {
      return res.status(400).json({ error: 'Order ID is required' });
    }

    const kountUrl = `${KOUNT_API_URL}/commerce/v2/orders/${orderId}`;

    const response = await axios.patch(kountUrl, req.body, {
      headers: {
        'Authorization': token,
        'Content-Type': 'application/json',
        'SessionId': sessionId,
      },
    });

    console.log('Kount API update response:', {
      status: response.status,
      responseData: response.data,
    });

    // Return the Kount response to the client
    return res.status(response.status).json(response.data);
  } catch (error: unknown) {
    const axiosError = error as {
      message: string;
      response?: { status?: number; data?: unknown };
    };

    console.error('Error updating Kount order:', axiosError.message);
    console.error('Error details:', axiosError.response?.data);

    return res.status(axiosError.response?.status || 500).json({
      error: 'Failed to update Kount order',
      details: axiosError.response?.data || axiosError.message,
    });
  }
};
