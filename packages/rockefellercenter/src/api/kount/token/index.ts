import axios from 'axios';

import { KOUNT_AUTH_URL, KOUNT_API_KEY } from '../../../environment';

import type { GatsbyFunctionRequest, GatsbyFunctionResponse } from 'gatsby';

export default async (
  req: GatsbyFunctionRequest,
  res: GatsbyFunctionResponse,
) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const response = await axios({
      url: KOUNT_AUTH_URL,
      method: 'post',
      headers: {
        authorization: `Basic ${KOUNT_API_KEY}`,
      },
      params: {
        grant_type: 'client_credentials',
        scope: 'k1_integration_api',
      },
    });

    // Return the token response to the client
    return res.status(200).json(response.data);
  } catch (error: unknown) {
    const axiosError = error as {
      message: string;
      response?: { status?: number; data?: unknown };
    };

    console.error('Error getting Kount token:', axiosError.message);
    console.error('Error details:', axiosError.response?.data);

    return res.status(axiosError.response?.status || 500).json({
      error: 'Failed to get Kount bearer token',
      details: axiosError.response?.data || axiosError.message,
    });
  }
};
