import type { GatsbyFunctionRequest, GatsbyFunctionResponse } from 'gatsby';

export interface MonthInfo {
  date: Date | string;
  day: number;
  month: number; // Month (1-12)
  year: number;
  hours: number;
  minutes: number;
  firstOfMonth: string; // First day of the month in "DD/MM/YYYY" format
  lastOfMonth: string; // Last day of the month in "DD/MM/YYYY" format
  totalDays: number; // Total number of days in the month
}

export interface DateTimeResponse extends MonthInfo {
  future: Record<string, MonthInfo>; // Array of future months' date information
}

export const formatShortDate = (date: Date) => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${year}-${month}-${day}`;
};

export const getMonthInfo = (date: Date): MonthInfo => {
  const day = date.getDate();
  const monthZeroIndexed = date.getMonth(); // Months are 0-indexed
  const month = monthZeroIndexed;
  const year = date.getFullYear();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  const firstOfMonth = new Date(year, monthZeroIndexed, 1);
  const lastOfMonth = new Date(year, monthZeroIndexed + 1, 0);
  const totalDays = lastOfMonth.getDate(); // Total days in the month

  return {
    date,
    day,
    month,
    year,
    hours,
    minutes,
    firstOfMonth: formatShortDate(firstOfMonth),
    lastOfMonth: formatShortDate(lastOfMonth),
    totalDays,
  };
};

export default async (
  req: GatsbyFunctionRequest,
  res: GatsbyFunctionResponse<DateTimeResponse>,
) => {
  if (req.method !== 'POST') {
    return res.status(405).end();
  }

  const nyDate = new Date(
    new Date().toLocaleString('en-US', {
      timeZone: 'America/New_York',
    }),
  );

  const response: DateTimeResponse = {
    ...getMonthInfo(nyDate),
    future: {},
  };

  for (let i = 1; i < 7; i++) {
    const nextMonthDate = new Date(
      nyDate.getFullYear(),
      nyDate.getMonth() + i,
      1,
    );
    // Create a unique key that includes year to handle year boundaries correctly
    const monthKey = `${nextMonthDate.getFullYear()}-${String(
      nextMonthDate.getMonth() + 1,
    ).padStart(2, '0')}`;
    response.future[monthKey] = getMonthInfo(nextMonthDate); // Assign month info to the corresponding month key
  }

  return res.status(200).json(response);
};
