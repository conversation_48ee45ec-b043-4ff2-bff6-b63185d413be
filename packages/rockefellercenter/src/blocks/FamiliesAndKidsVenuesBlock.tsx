/** @jsxImportSource theme-ui @jsxRuntime classic */
import { AnchorSection } from '@tishman/components';
import React, { useMemo, type ComponentPropsWithoutRef } from 'react';

import { FamiliesKidsCarousel } from '../components/FamiliesKidsCarousel/FamiliesKidsCarousel';
import { usePageData } from '../utils';

import type { FamiliesKidsCardProps } from '../components/FamiliesKidsCarousel/FamiliesKidsCarouselCard';
import type { FluidObject } from 'gatsby-image';

interface PrivateEventVenue {
  id: string;
  title: string;
  kidsExcerpt?: string;
  kidsPoster?: { alt: string; asset: { fluid: FluidObject } };
  imageGallery: {
    images: {
      alt: string;
      asset: { fluid: FluidObject };
    };
  };
  cta: {
    caption: string;
    url: string;
  };
}
interface AttractionVenue {
  id: string;
  kidsExcerpt?: string;
  kidsPoster?: { alt: string; asset: { fluid: FluidObject } };
  gallery: {
    title: string;
    items: {
      alt: string;
      asset: { fluid: FluidObject };
    };
  };
  heroCTA: {
    bodyCopy: string;
  };
}

interface BusinessVenue {
  id: string;
  titleAndSlug: { title: string; slug: { current: string } };
  excerpt?: string;
  poster?: { alt: string; asset: { fluid: FluidObject } };
  kidsExcerpt?: string;
  kidsPoster?: { alt: string; asset: { fluid: FluidObject } };
}

type Venue = PrivateEventVenue | AttractionVenue | BusinessVenue;

export default function FamiliesAndKidsVenuesBlock(
  props: ComponentPropsWithoutRef<typeof AnchorSection>,
): React.JSX.Element {
  const { sanityFamiliesAndKidsGuideLp } =
    usePageData<Queries.FamiliesAndKidsGuideLPQuery>();
  const data = useMemo(() => {
    if (!sanityFamiliesAndKidsGuideLp)
      throw new Error('Expected Family & Kids data');
    if (!sanityFamiliesAndKidsGuideLp?.familiesAndKidsGuideSectionHeadingItems)
      throw new Error('Expected Family & Kids section heading data');
    if (!sanityFamiliesAndKidsGuideLp?.familiesAndKidsVenue)
      throw new Error('Expected Family & Kids Venues data');

    return {
      sectionHeading:
        sanityFamiliesAndKidsGuideLp.familiesAndKidsGuideSectionHeadingItems,
      cards: sanityFamiliesAndKidsGuideLp.familiesAndKidsVenue,
    };
  }, [sanityFamiliesAndKidsGuideLp]);

  function isPrivateEventVenue(obj: Venue): obj is PrivateEventVenue {
    return (
      (obj as PrivateEventVenue).title !== undefined &&
      (obj as PrivateEventVenue).cta !== undefined
    );
  }

  function isAttractionVenue(obj: Venue): obj is AttractionVenue {
    return (obj as AttractionVenue).gallery?.title !== undefined;
  }

  function isBusinessVenue(obj: Venue): obj is BusinessVenue {
    return (obj as BusinessVenue).titleAndSlug?.title !== undefined;
  }

  const extractVenueTitle = (venue: Venue): string | undefined => {
    if (isPrivateEventVenue(venue)) {
      return venue.title;
    } else if (isAttractionVenue(venue)) {
      return venue.gallery.title;
    } else if (isBusinessVenue(venue)) {
      return venue.titleAndSlug.title;
    } else {
      return '';
    }
  };

  const updatedVenues = data.cards
    .filter((d) => d && typeof d === 'object' && Object.keys(d).length > 0)
    .map((venue) => {
      let url;
      const type = 'venue';
      const venueTitle = extractVenueTitle(venue as Venue);

      switch (venueTitle) {
        case 'Five Acres':
          url = '/dine/five-acres/';
          break;
        case 'Flippers Roller Skating Rink':
          url = '/attractions/flippers-roller-skating-rink';
          break;
        case 'The Rink at Rockefeller Center':
          url = '/attractions/the-rink-at-rockefeller-center/';
          break;
        case 'The Summer Rink at Rockefeller Center':
          url = '/attractions/the-rink-at-rockefeller-center/';
          break;

        default:
          url = null;
      }

      if (isPrivateEventVenue(venue as Venue)) {
        const privateVenue = venue as PrivateEventVenue;
        if (privateVenue.cta?.url) {
          url = privateVenue.cta.url;
        }
      }
      return { ...venue, url, type };
    });

  return (
    <AnchorSection {...props}>
      <FamiliesKidsCarousel
        heading={data.sectionHeading[5]?.sectionHeading ?? 'Venues'}
        items={
          updatedVenues as ReadonlyArray<
            Readonly<FamiliesKidsCardProps['item']>
          >
        }
        subheading={data.sectionHeading[5]?.sectionSubheading ?? 'Venues'}
      />
    </AnchorSection>
  );
}
