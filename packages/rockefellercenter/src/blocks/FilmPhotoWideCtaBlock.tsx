/** @jsxImportSource theme-ui @jsxRuntime classic */
import { WideCta, Container, Section, Button } from '@tishman/components';
import { graphql, useStaticQuery } from 'gatsby';
import React, { useMemo, useState } from 'react';
import invariant from 'invariant';

import { NewsletterSignUpModal } from './NewsletterSignupModalBlock/NewsletterSignupModal';

import type { ComponentPropsWithoutRef } from 'react';

const FILM_PHOTO_WIDE_CTA_QUERY = graphql`
  query FilmPhotoWideCta {
    dataJson(jsonId: { eq: "film-photo" }) {
      wideCta {
        title
        caption
        link {
          label
          url
        }
      }
    }
  }
`;

const FilmPhotoWideCtaBlock = (
  props: ComponentPropsWithoutRef<typeof Section>,
): React.JSX.Element => {
  const { dataJson } = useStaticQuery<Queries.FilmPhotoWideCtaQuery>(
    FILM_PHOTO_WIDE_CTA_QUERY,
  );

  invariant(dataJson, 'Film and Photo JSON data is required!');

  const [isModalOpen, setIsModalOpen] = useState(false);

  const FilmPhotoWideCtaProps = useMemo(
    () => ({
      title: dataJson.wideCta.title,
      caption: dataJson.wideCta.caption,
      link: dataJson.wideCta.link,
    }),
    [dataJson],
  );

  const handleSubscribeClick = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const customSubscribeButton = (
    <Button onClick={handleSubscribeClick} variant="underline">
      {FilmPhotoWideCtaProps.link?.label}
    </Button>
  );

  return (
    <Section data-section="FilmPhotoWideCta" {...props}>
      <Container
        sx={{
          'pt': 5,
          'pb': 4,
          '> [class*="WideCta"] > [class*="WideCta"]': {
            borderColor: 'initial',
          },
          'px': [0, 0, 0, 0],
        }}
      >
        <WideCta {...FilmPhotoWideCtaProps} customCTA={customSubscribeButton} />
      </Container>
      <NewsletterSignUpModal isOpen={isModalOpen} onClose={handleModalClose} />
    </Section>
  );
};

export default FilmPhotoWideCtaBlock;
