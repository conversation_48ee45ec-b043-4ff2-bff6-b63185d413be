import React, { Fragment, useMemo } from 'react';
import { Flex, Text } from '@tishman/components';

import {
  formatTimeRangeDisplay,
  formatTimeToNearestHalfHour,
  getTicketDisplayTitle,
} from '../../../components/BuyTickets';
import {
  Flow,
  ON_THE_ROCKS_PRODUCT_AK,
} from '../../../services/viva/constants';
import { formatMoney } from '../../../utils';
import {
  parseTimeslotHours,
  parseTimeslotTime,
} from '../../../utils/parse-timeslot-hours';
import { parseAndFormatDate } from '../../../services/date';

import type { ShopCartSelection } from '../../../store/types';
import type { Availability } from '../../../components/BuyTickets/SegmentedTimePicker/Availabilities';

type Props = {
  selection: ShopCartSelection;
  flow: Flow;
  price: number;
};

export const SelectionBlock = ({ selection, flow, price }: Props) => {
  const isOnTheRocks = selection.productAK === ON_THE_ROCKS_PRODUCT_AK;
  const selectionDates =
    'timeSlot' in selection && selection.timeSlot
      ? parseTimeslotTime(selection.timeSlot)
      : [];

  const hours = useMemo(() => {
    if ('timeSlot' in selection && selection.timeSlot) {
      return parseTimeslotHours(selection.timeSlot);
    }

    return [];
  }, [selection]);

  return (
    <Flex
      key={selection.productAK}
      sx={{
        flexDirection: 'column',
      }}
    >
      <Flex sx={{ justifyContent: 'space-between' }}>
        <Text
          sx={{
            fontWeight: 500,
          }}
        >
          {selection.count}{' '}
          {getTicketDisplayTitle(flow, selection.count, selection.label)}
        </Text>
        {flow !== Flow.REDEMPTION && flow !== Flow.CITY_PASS_REDEMPTION && (
          <Text
            data-testid="selection-block-item-price"
            sx={{
              fontWeight: 500,
            }}
          >
            {formatMoney(price)}
          </Text>
        )}
      </Flex>
      {/* TIME & DATE */}
      {flow !== Flow.TOR_EXPRESS &&
        selectionDates.map((selectionDate, index) => (
          <Fragment key={index}>
            {index === 0 && (
              <Text
                data-date={parseAndFormatDate(selectionDate, 'MMMM d, yyyy')}
                data-testid="selection-block-date"
              >
                {parseAndFormatDate(selectionDate, 'MMMM d, yyyy')}
              </Text>
            )}
            <Text
              data-testid="selection-block-time"
              data-time={formatTimeRangeDisplay(
                selection.timeSlot,
                index,
                isOnTheRocks,
              )}
            >
              {formatTimeRangeDisplay(selection.timeSlot, index, isOnTheRocks)}
            </Text>
          </Fragment>
        ))}
    </Flex>
  );
};
