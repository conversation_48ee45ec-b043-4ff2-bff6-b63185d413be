/** @jsxImportSource theme-ui @jsxRuntime classic */
import { AnchorSection } from '@tishman/components';
import React, { useMemo, type ComponentPropsWithoutRef } from 'react';

import { FamiliesKidsCarousel } from '../components/FamiliesKidsCarousel/FamiliesKidsCarousel';
import { usePageData } from '../utils';

import type { FamiliesKidsCardProps } from '../components/FamiliesKidsCarousel/FamiliesKidsCarouselCard';

export default function FamiliesAndKidsAttractionsBlock(
  props: ComponentPropsWithoutRef<typeof AnchorSection>,
): React.JSX.Element {
  const { sanityFamiliesAndKidsGuideLp } =
    usePageData<Queries.FamiliesAndKidsGuideLPQuery>();

  const data = useMemo(() => {
    if (!sanityFamiliesAndKidsGuideLp)
      throw new Error('Expected Family & Kids data');
    if (!sanityFamiliesAndKidsGuideLp?.familiesAndKidsGuideSectionHeadingItems)
      throw new Error('Expected Family & Kids section heading data');
    if (!sanityFamiliesAndKidsGuideLp?.familiesAndKidsAttractions)
      throw new Error('Expected Family & Kids attractions data');

    return {
      sectionHeading:
        sanityFamiliesAndKidsGuideLp.familiesAndKidsGuideSectionHeadingItems,
      cards: sanityFamiliesAndKidsGuideLp.familiesAndKidsAttractions,
    };
  }, [sanityFamiliesAndKidsGuideLp]);

  const sanitizeTicketReference = (card: any) => {
    if (card?._type === 'ticketReference') {
      const selectedTicketIndex = card.selectedTicket.split(':')[0];
      const selectedTicket = card.ticketCategory?.tickets[selectedTicketIndex];

      return {
        type: 'attraction', // use the attraction card for ticket reference
        url: selectedTicket?.url?.url,
        gallery: {
          title: selectedTicket?.title,
          items: [{ asset: selectedTicket?.image?.asset }],
        },
        heroCTA: {
          bodyCopy: selectedTicket?._rawDescription,
        },
      };
    }

    return card;
  };

  const updatedAttractions = data.cards
    .map(sanitizeTicketReference)
    .map((attraction) => {
      let url;

      switch ((attraction as { gallery?: { title?: string } }).gallery?.title) {
        case 'Top of the Rock':
          url = '/buy-tickets/#top-of-the-rock-observation-deck';
          break;
        case 'Rockefeller Center Tour':
          url = '/buy-tickets/#rockefeller-center-tour';
          break;
        case 'The Rink at Rockefeller Center':
          url = '/buy-tickets/#nyc-attractions';
          break;
        case 'The Holidays':
          url = '/holidays/';
          break;
        case 'The Summer Rink at Rockefeller Center':
          url = '/attractions/the-rink-at-rockefeller-center/';
          break;

        default:
          url = attraction.url;
      }
      return { ...attraction, type: 'attraction', url: url };
    });

  // Hardcoded Bloom card
  const hardcodedCard = {
    id: 'bloom',
    kidsExcerpt:
      'Discover the immersive sound and light experience located on the Rink Level of Rockefeller Center.',
    kidsPoster: {
      alt: 'kids playing joyfully in the BLOOM exhibit',
    },
    gallery: {
      title: 'HERO Experience',
      items: [{}],
    },
    heroCTA: {
      bodyCopy: 'BUY TICKETS',
    },
    type: 'attraction',
    url: 'https://www.tixr.com/groups/hero/',
    hardcoded: true, // This has been added as optional to the type -- please remove that too if you remove this hardcoded card
  };

  // Add the hardcoded card to the updatedAttractions array
  updatedAttractions.push(hardcodedCard);

  return (
    <AnchorSection {...props}>
      <FamiliesKidsCarousel
        heading={data.sectionHeading[0]?.sectionHeading ?? 'Attractions'}
        items={
          updatedAttractions as ReadonlyArray<
            Readonly<FamiliesKidsCardProps['item']>
          >
        }
        subheading={data.sectionHeading[0]?.sectionSubheading ?? 'Attractions'}
      />
    </AnchorSection>
  );
}
