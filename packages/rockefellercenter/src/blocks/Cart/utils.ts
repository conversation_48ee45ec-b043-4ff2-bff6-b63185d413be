import { Flow } from '../../services/viva/constants';

export const flowToSanityTicketMap: { [key in Flow]?: string } = {
  [Flow.ALL_IN_PASS]: 'top-of-the-rock-observation-deck',
  [Flow.TOR_GA]: 'top-of-the-rock-observation-deck',
  [Flow.TOR_EXPRESS]: 'top-of-the-rock-observation-deck',
  [Flow.TOR_VIP]: 'top-of-the-rock-observation-deck',
  [Flow.ON_THE_ROCKS]: 'promotions',
  // [Flow.THE_BEAM_PASS]: 'top-of-the-rock-observation-deck',

  [Flow.RINK]: 'the-rink',
  [Flow.RINK_VIP]: 'the-rink',
  [Flow.RINK_MEMBERSHIP]: 'the-rink',

  [Flow.RC_TOUR]: 'rockefeller-center-tour',
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 'rockefeller-center-tour',
  [Flow.RC_ROCK_PASS]: 'rockefeller-center-tour',

  [Flow.TOR_VIP_ROCKSTAR]: 'VIP',
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 'VIP',

  [Flow.CITY_PASS]: 'citypass',

  [Flow.SANTA_PHOTO]: 'santa-photo',

  [Flow.TREE_PHOTO]: 'holiday',
  [Flow.CHALETS]: 'holiday',
};

/**
 * NOTE:
 * The images that come back from Sanity look like:
 *
 * [
 *   {
 *     slug: {
 *       current: 'top-of-the-rock-observation-deck',
 *     },
 *     tickets: [TOR image, Express Pass Image, etc.]
 *   }
 * ]
 *
 * without any indication of which flow the image is for. So we need to
 * hard-code map a flow to an index in the array
 **/

export const flowToImageIndex: { [key in Flow]?: number } = {
  [Flow.TOR_GA]: 0,
  [Flow.TOR_EXPRESS]: 1,
  [Flow.TOR_VIP]: 2,
  [Flow.THE_BEAM_PASS]: 3,
  [Flow.ALL_IN_PASS]: 1,
  [Flow.ON_THE_ROCKS]: 0,

  // slug: rockefeller-center-tour
  [Flow.RC_TOUR]: 0,
  [Flow.RC_ROCK_PASS]: 1,
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 3,

  // slug: VIP
  [Flow.TOR_VIP_ROCKSTAR]: 1,
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 2,

  [Flow.RINK]: 0,
  [Flow.RINK_VIP]: 1,
  [Flow.RINK_MEMBERSHIP]: 2,

  // slug: holiday
  [Flow.TREE_PHOTO]: 0,
  [Flow.CHALETS]: 1,

  [Flow.CITY_PASS]: 0,
};
