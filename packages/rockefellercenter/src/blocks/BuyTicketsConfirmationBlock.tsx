/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, Section, Flex } from '@tishman/components';
import { graphql, useStaticQuery } from 'gatsby';
import invariant from 'invariant';
import React, { useContext, useMemo } from 'react';

import { Flow, getBosEventKey } from '../services/viva/constants';
import {
  Contact,
  Intro,
  OrderSummaryViva,
  RedemptionIntro,
  RefundInfo,
  RefundSummary,
} from '../components/BuyTickets';
import { RinkMembersSummary } from '../components/BuyTickets/Payment/RinkMembersSummary';
import { ConfirmationPageContext } from '../utils/confirmation-page-context-provider';
import {
  selectCheckoutCartTotals,
  selectEvent,
  selectFlow,
  selectShopCartSelections,
  useAppSelector,
} from '../store';

import type { CheckoutTotals } from '../store/types';

const BUY_TICKETS_CONFIRMATION_QUERY = graphql`
  query BuyTicketsConfirmation {
    dataJson(jsonId: { eq: "buy-tickets" }) {
      confirmation {
        deck {
          title
          description
        }
        vip {
          title
          description
        }
        expressPass {
          title
          description
        }
        tour {
          title
          description
        }
        rockPass {
          title
          description
        }
        cityPassRedemption {
          title
          description
        }
        cityPassRedeemed {
          title
          description
        }
        refund {
          title
          description
        }
        rink {
          title
          description
          knowBeforeYouGo
        }
      }
    }
  }
`;

const BuyTicketsConfirmationBlock = (): React.JSX.Element => {
  const { checkout, isCityPassRedemption, cartSelectionsByDate } = useContext(
    ConfirmationPageContext,
  );
  const flow = useAppSelector(selectFlow);
  const event = useAppSelector(selectEvent);
  const shopCartSelections = useAppSelector(selectShopCartSelections);
  const totals = useAppSelector(selectCheckoutCartTotals);
  const { dataJson } = useStaticQuery<Queries.BuyTicketsConfirmationQuery>(
    BUY_TICKETS_CONFIRMATION_QUERY,
  );

  invariant(dataJson, 'Buy Tickets confirmation JSON data is required!');

  const confirmationProps = useMemo(
    () => ({
      deck: {
        title: dataJson.confirmation.deck.title ?? '',
        description: dataJson.confirmation.deck.description ?? '',
      },
      tour: {
        title: dataJson.confirmation.tour.title ?? '',
        description: dataJson.confirmation.tour.description ?? '',
      },
      vip: {
        title: dataJson.confirmation.vip.title ?? '',
        description: dataJson.confirmation.vip.description ?? '',
      },
      expressPass: {
        title: dataJson.confirmation.expressPass.title ?? '',
        description: dataJson.confirmation.expressPass.description ?? '',
      },
      rockPass: {
        title: dataJson.confirmation.rockPass.title ?? '',
        description: dataJson.confirmation.rockPass.description ?? '',
      },
      cityPassRedemption: {
        title: dataJson.confirmation.cityPassRedemption.title ?? '',
        description: dataJson.confirmation.cityPassRedemption.description ?? '',
      },
      cityPassRedeemed: {
        title: dataJson.confirmation.cityPassRedeemed.title ?? '',
        description: dataJson.confirmation.cityPassRedeemed.description ?? '',
      },
      rink: {
        title: dataJson.confirmation.rink?.title ?? '',
        description: dataJson.confirmation.rink?.description ?? '',
        knowBeforeYouGo: dataJson.confirmation.rink?.knowBeforeYouGo,
      },
      treePhoto: {
        title: dataJson.confirmation.deck.title ?? '',
        description: dataJson.confirmation.deck.description ?? '',
      },
      refund: {
        title: dataJson.confirmation.refund.title ?? '',
        description: dataJson.confirmation.refund.description ?? '',
      },
    }),
    [dataJson],
  );

  const introData = useMemo(() => {
    const orderFlow = event ? getBosEventKey(event) : null;

    switch (orderFlow) {
      case Flow.DECK:
      case Flow.CITY_PASS:
      case Flow.C3:
        return isCityPassRedemption
          ? confirmationProps.cityPassRedemption
          : confirmationProps.deck;
      case Flow.RC_TOUR_KIDS_AND_FAMILY:
      case Flow.RC_TOUR:
        return confirmationProps.tour;
      case Flow.TOR_VIP_ROCKSTAR:
      case Flow.RC_ROCK_PASS_VIP_HOLIDAY:
      case Flow.TOR_VIP:
        return confirmationProps.vip;
      case Flow.TOR_EXPRESS:
        return confirmationProps.expressPass;
      case Flow.REFUND:
        return confirmationProps.refund;
      case Flow.RC_ROCK_PASS:
        return confirmationProps.rockPass;
      case Flow.REDEMPTION:
        return confirmationProps.cityPassRedeemed;
      case Flow.CHALETS:
      case Flow.RINK_VIP:
      case Flow.RINK:
        return confirmationProps.rink;
      case Flow.RINK_MEMBERSHIP:
        return {
          ...confirmationProps.rink,
          knowBeforeYouGo: undefined,
        };
      case Flow.TREE_PHOTO:
        return confirmationProps.treePhoto;
      default:
        return confirmationProps.deck;
    }
  }, [isCityPassRedemption, confirmationProps]);

  return (
    <Section data-section="BuyTicketsConfirmation" sx={{ px: [0, 8] }}>
      <Flex
        sx={{
          justifyContent: 'space-between',
          flexDirection: ['column', 'column', 'column', 'row'],
        }}
      >
        {flow !== Flow.REFUND && (
          <Box
            sx={{
              width: ['100%', '100%', '100%', '50%'],
              marginTop: [0, 6],
            }}
          >
            {isCityPassRedemption ? (
              <RedemptionIntro {...introData} />
            ) : (
              <Intro {...introData} />
            )}
          </Box>
        )}
        <Box
          sx={{
            width: ['100%'],
            maxWidth: ['100%', '100%', '100%', '500px'],
            marginTop: [5, 7],
            marginBottom: 7,
          }}
        >
          {flow === Flow.REFUND ? (
            <RefundSummary />
          ) : (
            <OrderSummaryViva
              cartSelectionsByDate={
                flow === Flow.CITY_PASS_REDEMPTION
                  ? {
                      [Flow.CITY_PASS_REDEMPTION]: [shopCartSelections],
                    }
                  : cartSelectionsByDate
              }
              flow={flow}
              isLoading={false}
              isOrderConfirmed={true}
              totals={totals}
            />
          )}
          {flow === Flow.RINK_MEMBERSHIP && checkout.attributes && (
            <RinkMembersSummary cartAttributes={checkout.attributes} />
          )}
          {flow === Flow.REFUND ? <RefundInfo /> : <Contact />}
        </Box>
      </Flex>
    </Section>
  );
};

export default BuyTicketsConfirmationBlock;
