import {
  Box,
  Button,
  Flex,
  Form,
  Input,
  Modal,
  Radio,
  Text,
  ThemeProvider,
  TishmanThemeName,
  getThemeByName,
} from '@tishman/components';
import React, { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import * as Icons from '@tishman/icons';
import { animated, useSpring } from '@react-spring/web';
import { useStaticQuery, graphql } from 'gatsby';
import { EmailIcon } from 'react-share';
import { useLocation } from '@reach/router';

import { popUpStyles, scrollBoxStyles } from './style';

interface PopUpScrollBoxFormValues extends SignupModalType {
  email: string;
  id: string;
  nycCitizen?: string;
  mailchimpAudienceID: string;
  server?: string;
}

interface SignupModalType {
  title: string;
  subscriptionPopupType: string;
  subscriptionPopupTheme: string;
  headline: string;
  subheadline?: string;
  ctaLabel: string;
  radioButtonQuestion?: boolean;
  pathContainsUrl: readonly (string | undefined)[];
  showOnHomepage?: boolean;
  mailchimpAudienceID: string;
}

interface NewsletterSignUpModalProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const NEWSLETTER_SIGNUP_QUERY = graphql`
  query SubscriptionPopup {
    allSanitySubscriptionPopup {
      nodes {
        title
        subscriptionPopupType
        subscriptionPopupTheme
        headline
        subheadline
        ctaLabel
        radioButtonQuestion
        pathContainsUrl
        showOnHomepage
        mailchimpAudienceID
      }
    }
  }
`;

export const NewsletterSignUpModal = ({
  isOpen: manualIsOpen,
  onClose: manualOnClose,
}: NewsletterSignUpModalProps = {}): JSX.Element | null => {
  const { allSanitySubscriptionPopup } =
    useStaticQuery<Queries.SubscriptionPopupQuery>(NEWSLETTER_SIGNUP_QUERY);

  const formMethods = useForm<PopUpScrollBoxFormValues>();
  const { formState, register, setValue } = formMethods;

  const [isVisible, setIsVisible] = useState(false);
  const [displayType, setDisplayType] = useState('');
  const [cookiePath, setCookiePath] = useState('');
  const [modalContent, setModalContent] = useState<SignupModalType | undefined>(
    {
      title: '',
      subscriptionPopupType: '',
      subscriptionPopupTheme: '',
      headline: '',
      subheadline: '' ?? undefined,
      radioButtonQuestion: false ?? undefined,
      ctaLabel: '',
      pathContainsUrl: [] ?? undefined,
      showOnHomepage: false,
      mailchimpAudienceID: '',
    },
  );

  const location = useLocation();

  const currentURL = location?.href;
  const currentPathname = location?.pathname;

  // Determine if we're using manual control
  const isManuallyControlled = manualIsOpen !== undefined;

  // Use manual values when provided, otherwise use internal state
  const actualIsOpen = isManuallyControlled ? manualIsOpen : isVisible;

  useEffect(() => {
    const isSubscriptionPopupOnCurrentPage = () => {
      let matchingNode: SignupModalType | undefined = undefined;

      const homepageURLs = [
        'https://www.rockefellercenter.com/',
        'http://localhost:8000/',
      ];

      allSanitySubscriptionPopup.nodes.forEach((popup) => {
        matchingNode = popup as SignupModalType;

        if (
          popup.showOnHomepage === true &&
          homepageURLs.includes(currentURL)
        ) {
          setModalContent(matchingNode);
          setDisplayType(popup.subscriptionPopupType || '');
          setCookiePath('homepage');
          return;
        }
        popup.pathContainsUrl?.forEach((path) => {
          if (path && currentURL && currentURL.includes(path)) {
            // matchingNode = popup;
            setModalContent(matchingNode);
            setDisplayType(popup.subscriptionPopupType || '');
            setCookiePath(path);
            return;
          }
        });
      });

      if (!matchingNode) {
        resetDefaultStates();
      }

      return matchingNode;
    };

    isSubscriptionPopupOnCurrentPage();
  }, [displayType, currentURL, allSanitySubscriptionPopup.nodes, modalContent]);

  // Set the mailchimpAudienceID in the form when modalContent changes
  useEffect(() => {
    if (modalContent?.mailchimpAudienceID) {
      setValue('mailchimpAudienceID', modalContent.mailchimpAudienceID);
    }
  }, [modalContent?.mailchimpAudienceID, setValue]);

  const setCookie = () => {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 7);

    document.cookie = `newsletter-${cookiePath}=${cookiePath}; expires=${expirationDate.toUTCString()}; path=/`;
  };

  // Pop up logic to check if cookie exists and if not, display the popup
  useEffect(() => {
    // Skip automatic cookie/scroll logic if manually controlled
    if (isManuallyControlled) return;

    const getCookie = (name: string) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) {
        const cookieValue = parts.pop();
        if (cookieValue !== undefined) {
          return cookieValue.split(';').shift();
        }
      }
    };

    const validateCookie = () => {
      const existingCookie = getCookie(`newsletter-${cookiePath}`);

      if (existingCookie) {
        setIsVisible(false);
      } else {
        if (displayType === 'popup') {
          setTimeout(() => setIsVisible(true), 1000);
        } else if (displayType === 'scrollbox') {
          const checkScroll = () => {
            if (window.pageYOffset > document.body.offsetHeight / 10) {
              setIsVisible(true);
              window.removeEventListener('scroll', checkScroll);
            }
          };
          window.addEventListener('scroll', checkScroll);

          return () => {
            window.removeEventListener('scroll', checkScroll);
          };
        }
      }
    };

    validateCookie();
  }, [displayType, currentPathname, cookiePath, isManuallyControlled]);

  const resetDefaultStates = () => {
    setModalContent(undefined);
    setDisplayType('');
    setCookiePath('');
  };

  // A cookie will be set when a user closes the popup box
  const closeModal = () => {
    if (isManuallyControlled && manualOnClose) {
      manualOnClose();
    } else {
      setIsVisible(false);
      setCookie();
    }
  };

  const scrollBoxTransition = useSpring({
    transform: actualIsOpen ? 'translateY(0)' : 'translateY(100%)',
    config: { tension: 190, friction: 32 },
    bottom: 0,
    right: 0,
  });

  const determineModalBackgroundColor = (subscriptionPopupTheme: string) => {
    switch (subscriptionPopupTheme) {
      case 'Rock Center Black':
        return 'BLACK';
      case 'Rock Center White':
        return 'WHITE';
      case 'Rock Center Cream':
        return 'CREAM';
      case 'Rock Center Green':
        return 'GREEN';
      default:
        return 'WHITE';
    }
  };

  const ScrollBox = (props: SignupModalType): JSX.Element | null => {
    const {
      headline,
      subheadline,
      radioButtonQuestion,
      ctaLabel,
      subscriptionPopupTheme,
    } = props;

    return (
      <ThemeProvider
        theme={getThemeByName(subscriptionPopupTheme as TishmanThemeName)}
      >
        <animated.div
          data-section="NewsletterSignupScrollBox"
          style={{
            ...scrollBoxTransition,
            position: 'fixed',
            zIndex: 100,
          }}
        >
          <Flex
            sx={{
              ...scrollBoxStyles.scrollBoxContainer,
              backgroundColor: determineModalBackgroundColor(
                subscriptionPopupTheme,
              ),
            }}
          >
            <Button
              onClick={closeModal}
              sx={{
                'background': 'none',
                'border': 'none',
                'padding': 2,
                'position': 'absolute',
                'top': 3,
                'right': 3,
                ':hover': {
                  cursor: 'pointer',
                },
              }}
              variant="text"
            >
              <Icons.Close />
            </Button>
            <Flex
              sx={{
                width: '100%',
                border: `1px solid #8D594C`,
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                p: 3,
                height: formState.isSubmitSuccessful ? '200px' : 'auto',
              }}
            >
              <EmailIcon
                bgStyle={{ fill: 'none' }}
                iconFillColor={
                  subscriptionPopupTheme === 'Rock Center White' ||
                  subscriptionPopupTheme === 'Rock Center Cream'
                    ? 'black'
                    : 'white'
                }
              />
              {formState.isSubmitSuccessful ? (
                <Flex data-section="PopupThankYouBlock" {...props}>
                  <Box>
                    <Text
                      sx={{
                        mt: 2,
                        fontSize: 4,
                        fontWeight: 'medium',
                        textTransform: 'uppercase',
                      }}
                    >
                      You are now subscribed
                    </Text>
                  </Box>
                </Flex>
              ) : (
                <React.Fragment>
                  <Flex
                    sx={{
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '100%',
                      textAlign: 'center',
                    }}
                  >
                    <Text
                      sx={{
                        mt: 2,
                        fontSize: 4,
                        fontWeight: 'medium',
                        textTransform: 'uppercase',
                      }}
                    >
                      {headline}
                    </Text>
                    {subheadline && <Text sx={{ mt: 2 }}>{subheadline}</Text>}
                  </Flex>
                  <FormProvider {...formMethods}>
                    <Form data-section="popUpScrollBox" id="popUpScrollBox">
                      <Flex
                        sx={{
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          width: ['300px', '375px'],
                          p: 0,
                          mt: 3,
                        }}
                      >
                        {formState.errors.server && (
                          <Text variant="formError">
                            {formState.errors.server.message}
                          </Text>
                        )}
                        <Input
                          placeholder="Enter your email"
                          sx={{
                            'bg': 'white',
                            'color': 'black',
                            'border':
                              subscriptionPopupTheme === 'Rock Center Cream'
                                ? 'none'
                                : '1px solid black',
                            '::placeholder': {
                              color: 'black',
                              fontSize: '16px',
                              fontWeight: 400,
                              textAlign: 'center',
                            },
                          }}
                          text=""
                          {...register('email', {
                            required: 'Required',
                            pattern: {
                              value: /^\S+@\S+\.\S+$/,
                              message: 'Please enter a valid email address',
                            },
                          })}
                        />
                        {radioButtonQuestion && (
                          <Box sx={scrollBoxStyles.radioButtonContainer}>
                            <Text sx={scrollBoxStyles.radioText}>
                              Do you live or work in New York City?
                            </Text>
                            <Radio
                              text="Yes"
                              value="true"
                              {...register('nycCitizen')}
                            />
                          </Box>
                        )}
                        {/* Hidden field for mailchimpAudienceID */}
                        <input
                          type="hidden"
                          {...register('mailchimpAudienceID')}
                        />
                        <Button
                          aria-label="email submit button"
                          data-element="SubmitButton"
                          disabled={
                            formState.isSubmitting ||
                            formState.isSubmitSuccessful
                          }
                          role="button"
                          sx={{
                            ...scrollBoxStyles.submitButton,
                            ':disabled': {
                              bg:
                                subscriptionPopupTheme ===
                                  'Rock Center White' ||
                                subscriptionPopupTheme === 'Rock Center Cream'
                                  ? 'black'
                                  : 'white',
                            },
                          }}
                          type="submit"
                          variant="submit"
                        >
                          {formState.isSubmitting
                            ? 'Subscribing...'
                            : `${ctaLabel}`}
                        </Button>
                      </Flex>
                    </Form>
                  </FormProvider>
                </React.Fragment>
              )}
            </Flex>
          </Flex>
        </animated.div>
      </ThemeProvider>
    );
  };

  const PopUp = (props: SignupModalType): JSX.Element | null => {
    const { headline, subheadline, ctaLabel, subscriptionPopupTheme } = props;

    return (
      <ThemeProvider
        theme={getThemeByName(subscriptionPopupTheme as TishmanThemeName)}
      >
        <Modal
          data-section="NewsletterSignupPopup"
          id="center-sign-up-modal"
          isOpen={actualIsOpen}
          onClose={closeModal}
        >
          <Flex
            data-close-exit-modal
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                closeModal();
              }
            }}
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 10,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Flex
              sx={{
                ...popUpStyles.popupContainer,
                backgroundColor: determineModalBackgroundColor(
                  subscriptionPopupTheme,
                ),
              }}
            >
              <Flex
                sx={{
                  ...popUpStyles.secondBorderSection,
                  border:
                    subscriptionPopupTheme === 'Rock Center White' ||
                    subscriptionPopupTheme === 'Rock Center Cream'
                      ? '1px solid black'
                      : '1px solid white',
                }}
              >
                <Flex
                  sx={{
                    width: '95%',
                    height: '320px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'column',
                    position: 'relative',
                    border:
                      subscriptionPopupTheme === 'Rock Center White' ||
                      subscriptionPopupTheme === 'Rock Center Cream'
                        ? '1px solid black'
                        : '1px solid white',
                  }}
                >
                  <Button
                    onClick={closeModal}
                    sx={{
                      'background': 'none',
                      'border': 'none',
                      'padding': 2,
                      'position': 'absolute',
                      'top': 0,
                      'right': 0,
                      ':hover': {
                        cursor: 'pointer',
                      },
                    }}
                    variant="text"
                  >
                    <Icons.Close />
                  </Button>
                  <Flex
                    sx={{
                      width: '100%',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      py: 5,
                      px: 2,
                    }}
                  >
                    {formState.isSubmitSuccessful ? (
                      <Flex
                        data-section="PopupThankYouBlock"
                        {...props}
                        sx={{
                          width: '100%',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <EmailIcon
                          bgStyle={{ fill: 'none' }}
                          iconFillColor={
                            subscriptionPopupTheme === 'Rock Center White' ||
                            subscriptionPopupTheme === 'Rock Center Cream'
                              ? 'black'
                              : 'white'
                          }
                        />
                        <Box>
                          <Text
                            sx={{
                              mt: 2,
                              fontSize: 4,
                              fontWeight: 'medium',
                              textTransform: 'uppercase',
                            }}
                          >
                            You are now subscribed
                          </Text>
                        </Box>
                      </Flex>
                    ) : (
                      <React.Fragment>
                        <Flex
                          sx={{
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%',
                            textAlign: 'center',
                          }}
                        >
                          <Text variant="smallTitle">{headline}</Text>
                          {subheadline && (
                            <Text sx={{ marginTop: 2 }}>{subheadline}</Text>
                          )}
                        </Flex>
                        <FormProvider {...formMethods}>
                          <Form data-section="popupBox" id="popupBox">
                            <Flex
                              sx={{
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: ['300px', '375px'],
                                p: 0,
                                mt: 3,
                              }}
                            >
                              {formState.errors.server && (
                                <Text variant="formError">
                                  {formState.errors.server.message}
                                </Text>
                              )}
                              <Input
                                placeholder="Enter your email"
                                sx={{
                                  'bg': 'white',
                                  'color': 'black',
                                  'border':
                                    subscriptionPopupTheme ===
                                    'Rock Center White'
                                      ? '1px solid black'
                                      : 'none',
                                  '::placeholder': {
                                    color: 'black',
                                    fontSize: '16px',
                                    fontWeight: 400,
                                    textAlign: 'center',
                                  },
                                }}
                                text=""
                                {...register('email', {
                                  required: 'Required',
                                  pattern: {
                                    value: /^\S+@\S+\.\S+$/,
                                    message:
                                      'Please enter a valid email address',
                                  },
                                })}
                              />
                              {/* Hidden field for mailchimpAudienceID */}
                              <input
                                type="hidden"
                                {...register('mailchimpAudienceID')}
                              />
                              <Button
                                aria-label="email submit button"
                                data-element="SubmitButton"
                                disabled={formState.isSubmitting}
                                role="button"
                                sx={{
                                  ...popUpStyles.submitButton,
                                  ':disabled': {
                                    bg:
                                      subscriptionPopupTheme ===
                                        'Rock Center White' ||
                                      subscriptionPopupTheme ===
                                        'Rock Center Cream'
                                        ? 'black'
                                        : 'white',
                                  },
                                }}
                                type="submit"
                                variant="submit"
                              >
                                {formState.isSubmitting
                                  ? 'Submitting...'
                                  : `${ctaLabel}`}
                              </Button>
                            </Flex>
                          </Form>
                        </FormProvider>
                      </React.Fragment>
                    )}
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Modal>
      </ThemeProvider>
    );
  };

  if (!actualIsOpen || !modalContent) {
    return null;
  }

  return modalContent && displayType === 'scrollbox' ? (
    <ScrollBox {...modalContent} />
  ) : modalContent && displayType === 'popup' ? (
    <PopUp {...modalContent} />
  ) : null;
};
