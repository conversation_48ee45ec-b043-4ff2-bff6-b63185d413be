import { Block } from '@sanity/block-content-to-react';
import React from 'react';
import {
  Box,
  Flex,
  IntrinsicImage,
  SanityRichText,
  Section,
} from '@tishman/components';

import { LinkWithArrowLeftCorner } from '../LinkWithArrowLeftCorner';
import { PrivateEventsHeroVenueDetails } from '../PrivateEventsHeroVenueDetails';

import { PrivateEventsHeroTextSection } from './PrivateEventsHeroTextSection';
import { PrivateEventsHeroCta } from './PrivateEventsHeroCta';
type TVenueDetail = {
  content: Queries.SanityBlock[];
  links?: { text: string; url: string }[];
};

type TPrivateEventsVenueHero = {
  image: Queries.SanitySimpleImageType;
  textSection: Queries.SanityHeroTextSection;
  venueDetailsFeatureFlag?: boolean;
  venueDetails?: TVenueDetail[];
  cta?: Queries.SanityUrlType;
  pageName?: string;
};

export const PrivateEventsVenueHero = ({
  image,
  textSection,
  venueDetailsFeatureFlag = false,
  venueDetails,
  cta,
  pageName = '',
}: TPrivateEventsVenueHero) => {
  const { eyebrow, title, paragraph, ctaButton } = textSection;
  const ctaData = {
    link: (cta ? cta.url : ctaButton?.ctaLink) ?? undefined,
    caption: (cta ? cta.caption : ctaButton?.ctaText) ?? undefined,
  };

  return (
    <Section
      id="details"
      sx={{ marginTop: ['24px', 0] }}
      theme="Rock Center White"
    >
      <Flex
        sx={{
          flexDirection: ['column-reverse', 'column-reverse', 'row'],
          gap: ['32px', 0],
          paddingBottom: ['32px', 0],
          alignItems: 'stretch',
        }}
      >
        <Flex
          sx={{
            padding: [
              '4px 16px',
              '80px 67px 80px 72px',
              '80px 67px 80px 72px',
              '215px 67px 215px 72px',
            ],
            alignItems: ['flex-start', 'flex-start', 'center'],
            alignContent: 'center',
            gap: '48px',
            flex: ['1 0 50%'],
          }}
        >
          <Flex
            sx={{
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: '32px',
            }}
          >
            <PrivateEventsHeroTextSection
              cta={ctaData}
              eyebrow={eyebrow ?? ''}
              pageName={pageName}
              paragraph={paragraph ?? ''}
              showMobileCta={false}
              showTabletCta={false}
              title={title ?? ''}
              titleLetterSpacing={[1, 1]}
              titleSx={{ marginTop: [1, '16px'] }}
            />
          </Flex>
        </Flex>
        <Flex
          sx={{
            flexDirection: 'column',
            alignItems: 'center',
            flex: ['1 0 100%', '1 0 50%'],
            width: '100%',
            minHeight: 'fit-content',
          }}
        >
          {image && image.asset?.gatsbyImageData && (
            <IntrinsicImage
              alt={image.alt || ''}
              image={image.asset?.gatsbyImageData}
              sx={{
                width: '100%',
                height: ['278px', '278px', '100%'],
                minHeight: '100%',
              }}
            />
          )}
        </Flex>
      </Flex>

      {venueDetailsFeatureFlag && (
        <React.Fragment>
          <Box
            sx={{
              display: ['block', 'block', 'none'],
              padding: ['4px 16px', '0 67px 0 72px'],
              alignItems: 'flex-start',
              alignContent: 'center',
              gap: '48px',
              flex: ['1 0 100%', '1 0 50%'],
              flexWrap: 'wrap',
            }}
          >
            {venueDetails?.map((venueDetail, index) => (
              <PrivateEventsHeroVenueDetails
                blocks={venueDetail.content}
                key={index}
                last={index === venueDetails.length - 1}
                links={venueDetail.links}
                openInNewWindow={true}
                type="venue-detail"
              />
            ))}
          </Box>
          {ctaData && (
            <Box
              sx={{
                display: ['block', 'block', 'none'],
                padding: ['0 16px 54px', '0 67px 54px 72px'],
              }}
            >
              <PrivateEventsHeroCta
                caption={ctaData.caption}
                containerSx={{ marginTop: 0 }}
                link={ctaData.link}
                pageName={pageName}
                showMobileCta
              />
            </Box>
          )}
          <Section theme="Rock Center Black">
            <Flex
              sx={{
                marginBottom: 0,
                padding: '64px 64px 72px 72px',
                gap: '96px',
                display: ['none', 'none', 'flex'],
              }}
            >
              {venueDetails?.map((venueDetail, index) => (
                <PrivateEventsHeroVenueDetailsDesktop
                  blocks={venueDetail.content}
                  key={index}
                  links={venueDetail.links}
                  openInNewWindow={true}
                  type="venue-detail"
                />
              ))}
            </Flex>
          </Section>
        </React.Fragment>
      )}
    </Section>
  );
};

type TLink = {
  text: string;
  url: string;
};

type TPrivateEventsHeroVenueDetailsDesktop = {
  blocks?: Queries.SanityBlock[];
  links?: TLink[];
  openInNewWindow?: boolean;
  type?: 'default' | 'venue-category' | 'venue-detail';
};

export const PrivateEventsHeroVenueDetailsDesktop = ({
  blocks,
  links,
  openInNewWindow,
}: TPrivateEventsHeroVenueDetailsDesktop) => {
  const richTextBlocks = blocks?.map((block) => ({
    ...block,
    markDefs: (block as any).markDefs || [],
  }));

  return (
    <Box
      sx={{
        '& p': {
          fontSize: '21px',
          lineHeight: '1.16',
        },
        '& h4': {
          fontSize: '24px',
          letterSpacing: [2, '-1px'],
          lineHeight: '1.16',
          color: '#A98363',
          marginBottom: '8px',
        },
      }}
    >
      {richTextBlocks && (
        <SanityRichText blocks={richTextBlocks as unknown as Block[]} />
      )}
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '26px',
          marginTop: '16px',
          flexDirection: ['row'],
        }}
      >
        {links
          ?.filter((link) => !!link.text)
          .map((link) => (
            <LinkWithArrowLeftCorner
              key={link.url}
              openInNewWindow={openInNewWindow}
              text={link.text || ''}
              url={link.url || ''}
            />
          ))}
      </Box>
    </Box>
  );
};
