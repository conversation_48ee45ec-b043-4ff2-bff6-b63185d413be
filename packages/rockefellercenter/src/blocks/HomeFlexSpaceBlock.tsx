/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Section, FlexSpace } from '@tishman/components';
import { graphql, useStaticQuery } from 'gatsby';
import React, { useMemo } from 'react';

import type { IGatsbyImageData } from 'gatsby-plugin-image';
import type { ComponentPropsWithoutRef } from 'react';

const HOME_FLEX_SPACE_QUERY = graphql`
  query HomeFlexSpace {
    sanityHomePage(_type: { eq: "homePage" }) {
      flexSpaceField {
        bgImage {
          alt
          asset {
            gatsbyImageData(layout: CONSTRAINED, width: 2500)
          }
        }
        spaceOne {
          title
          description
          link {
            url
            label: caption
          }
        }
        spaceTwo {
          title
          description
          link {
            url
            label: caption
          }
        }
      }
    }
  }
`;
const HomeFlexSpaceBlock = (
  props: ComponentPropsWithoutRef<typeof Section>,
): JSX.Element | null => {
  const { sanityHomePage } = useStaticQuery<Queries.HomeFlexSpaceQuery>(
    HOME_FLEX_SPACE_QUERY,
  );

  const flexSpaceField = sanityHomePage?.flexSpaceField;

  const data = useMemo(() => {
    if (!flexSpaceField) return null;

    if (!flexSpaceField.spaceOne) throw new Error('Expected spaceOne data');
    if (!flexSpaceField.spaceOne.title)
      throw new Error('Expected spaceOne title');
    if (!flexSpaceField?.spaceOne?.link?.url)
      throw new Error('Expected spaceOne link url');
    if (!flexSpaceField.spaceOne.link.label)
      throw new Error('Expected spaceOne link label');

    const { bgImage } = flexSpaceField;

    return {
      bgImage: bgImage?.asset?.gatsbyImageData as IGatsbyImageData,
      bgImageAlt: bgImage?.alt || '',
      spaceOne: {
        title: flexSpaceField.spaceOne.title,
        description: flexSpaceField.spaceOne?.description ?? '',
        link: {
          url: flexSpaceField.spaceOne.link.url,
          label: flexSpaceField.spaceOne.link.label,
        },
      },
      spaceTwo: {
        title: flexSpaceField?.spaceTwo?.title ?? '',
        description: flexSpaceField?.spaceTwo?.description ?? '',
        link: {
          url: flexSpaceField?.spaceTwo?.link?.url ?? '',
          label: flexSpaceField?.spaceTwo?.link?.label ?? '',
        },
      },
    };
  }, [flexSpaceField]);

  return (
    data && (
      <Section data-section="HomeFlexSpace" {...props}>
        <FlexSpace {...data} />
      </Section>
    )
  );
};

export default HomeFlexSpaceBlock;
