/** @jsxImportSource theme-ui @jsxRuntime classic */
import { Box, Flex, SanityRichText, Text } from '@tishman/components';
import React, {
  Fragment,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import pluralize from 'pluralize';
import { getWindow } from '@hzdg/dom-utils';
import * as Icons from '@tishman/icons';

import {
  actions,
  selectHasAdult,
  selectHasChaperone,
  selectHasToddler,
  selectShopCartSelections,
  useAppDispatch,
  useAppSelector,
  selectFlow,
  selectWizardCurrentStep,
  selectTotalQuantity,
  selectCheckoutCoupon,
} from '../../store';
import {
  Step,
  ContinueButton,
  getTicketDisplayTitle,
  safeDataLayerPush,
} from '../../components/BuyTickets';
import { NewTicketQuantity } from '../../components/BuyTickets/NewTicketQuantity';
import { InterpolatedString } from '../../components/InterpolatedString';
import { useCurrentEventByFlow } from '../../services/viva';
import {
  BosEvent,
  Flow,
  FlowToBosEventMap,
  StatGroup,
} from '../../services/viva/constants';
import { CountdownTimerContext } from '../../utils/countdown-timer-context-provider';
import { BuyTicketsLayoutContext } from '../../layouts/BuyTicketsLayout';
import { newRelic } from '../../utils/new-relic';

import type { Block } from '@sanity/block-content-to-react';
import type { TicketType } from '../../services/viva/types';

const ICON_INTERPOLATION_RE = /\{\{\s*.+?\s*\|\s*icon\s*\}\}/;

const renderList = (copy: string) => (
  <Box
    sx={{
      '> ul': {
        'listStyle': 'none',
        'marginLeft': 24,
        '> li:before': {
          content: '"•"',
          fontSize: '22px',
          lineHeight: '20px',
          marginRight: '4px',
          marginLeft: '-14px',
        },
      },
    }}
  >
    <ul>
      {copy
        .split('- ')
        .slice(1)
        .map((copy, i) => (
          <li
            key={i}
            style={{
              marginTop: 6,
              marginBottom: 6,
            }}
          >
            {copy.startsWith('*') ? (
              <Box as="strong" sx={{ fontWeight: 500 }}>
                {copy.replace(/\*/g, '')}
              </Box>
            ) : (
              copy
            )}
          </li>
        ))}
    </ul>
  </Box>
);

export interface TicketStepProps {
  ticketTypes?: TicketType[];
  title: string;
  description: string;
  ticketCount?: number;
  whatsIncluded?: {
    children: Array<{
      _key: string;
      whatsIncludedText: string;
      style?: string;
      level?: string;
      listItem?: string;
      marks?: string[];
      markDefs?: Array<{
        _key: string;
        _type: string;
        href?: string;
      }>;
    }>;
  } | null;
}

interface TicketStepBlockProps {
  hideLabel?: boolean;
  stepNumber: number;
  width?: 'wide' | 'default';
  ticketStepDataOverride?: TicketStepProps;
}

export const BuyTicketsTicketStepBlock = ({
  hideLabel,
  stepNumber = 1,
  width = 'wide',
  ticketStepDataOverride,
}: TicketStepBlockProps) => {
  const { ticketStepData: _ticketStepData } = useContext(
    BuyTicketsLayoutContext,
  );

  const ticketStepData = ticketStepDataOverride ?? _ticketStepData;

  const [isDataLoading, setIsDataLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const counterContext = useContext(CountdownTimerContext);

  const dispatch = useAppDispatch();

  const flow = useAppSelector(selectFlow);
  const currentStep = useAppSelector(selectWizardCurrentStep);
  const shopCartSelections = useAppSelector(selectShopCartSelections);
  const checkoutCoupon = useAppSelector(selectCheckoutCoupon);
  const isRinkMembershipFlow = flow === Flow.RINK_MEMBERSHIP;

  // Check for ticketCount in URL Param when page loads..
  useEffect(() => {
    if (ticketStepData?.ticketTypes?.length) {
      setIsDataLoading(false);
    }
  }, [ticketStepData?.ticketTypes]);

  useEffect(() => {
    const window = getWindow();
    const urlSearch = new URLSearchParams(window?.location.search);
    const ticketCountParam = urlSearch.get('ticketCount');
    const ticketCount = Number(ticketCountParam);

    if (isDataLoading || !ticketStepData) return;

    if (isNaN(ticketCount) || !ticketCountParam) return;

    ticketStepData.ticketTypes?.forEach((ticketType) => {
      if (ticketType && ticketCount) {
        const {
          label,
          price,
          priceRange,
          pricetableAK,
          productAK,
          statGroup,
          tax,
          type,
        } = ticketType;

        const shopCartSelections = {
          count: ticketCount,
          label,
          price,
          priceRange,
          pricetableAK,
          productAK,
          statGroup,
          tax,
          type,
        };

        newRelic.logCartAction(
          'BuyTicketsTicketStepBlock',
          isRinkMembershipFlow
            ? 'setShopCartSelectionsByTicketTypeByPush'
            : 'setShopCartSelections',
          { shopCartSelections },
        );

        dispatch(
          actions.order[
            isRinkMembershipFlow
              ? 'setShopCartSelectionsByTicketTypeByPush'
              : 'setShopCartSelections'
          ](shopCartSelections),
        );
      }
    });
  }, [isDataLoading]);

  // GA4 view_item_list track event when component mounts
  useEffect(() => {
    if (
      !isInitialLoad ||
      !ticketStepData?.ticketTypes ||
      ticketStepData?.ticketTypes.length < 1
    )
      return;

    const items = ticketStepData.ticketTypes.map((ticketType) => ({
      coupon: checkoutCoupon,
      item_category: flow,
      item_name: ticketType.label,
      ticket_id: ticketType.productAK,
      price: (ticketType.price / 100).toFixed(2),
      quantity: 0,
    }));

    safeDataLayerPush({
      event: 'view_item_list',
      item_list_id:
        FlowToBosEventMap[flow as keyof typeof FlowToBosEventMap] ??
        'ticket_selection',
      item_list_name: flow,
      items,
    });

    setIsInitialLoad(false);
  }, [isInitialLoad, flow, ticketStepData?.ticketTypes]);

  const stepData = useMemo(() => {
    const { title, ticketTypes } = ticketStepData || {
      title: '',
      description: '',
      ticketTypes: [],
    };

    const descriptionHasIcon = ICON_INTERPOLATION_RE.test(
      ticketStepData?.description ?? '',
    );

    const whatsIncluded = ticketStepData?.whatsIncluded ? (
      <Fragment>
        <Flex
          sx={{
            'alignItems': 'flex-start',
            'lineHeight': 'normal',
            'minHeight': '36px',
            'gap': '16px',
            ' ul': {
              'listStyle': 'none',
              'marginLeft': 24,
              'paddingLeft': 0,
              '> li:before': {
                content: '"•"',
                fontSize: '22px',
                lineHeight: '20px',
                marginRight: '4px',
                marginLeft: '-14px',
              },
            },
          }}
        >
          <Icons.TicketIcon
            aria-hidden
            style={{ color: '#BEB1D7', flex: '0 0 50px' }}
          />

          <Text sx={{ maxWidth: '360px' }}>
            <SanityRichText
              blocks={
                ticketStepData.whatsIncluded.children.map((child) => ({
                  _key: child._key,
                  _type: 'block',
                  style: child.style || 'normal',
                  level: child.level?.toString(),
                  listItem: child.level ? 'none' : child.listItem,
                  children: [
                    {
                      _key: `${child._key}-span`,
                      _type: 'span',
                      text: child.whatsIncludedText,
                      marks: child.marks || [],
                    },
                  ],
                  markDefs: child.markDefs || [],
                })) as Block[]
              }
            />
          </Text>
        </Flex>
      </Fragment>
    ) : null;

    const description = (
      <InterpolatedString
        renderChunk={(chunk, i) => {
          if (typeof chunk !== 'string') {
            return (
              <Box key={i} sx={{ flex: '0 0 50px' }}>
                {chunk}
              </Box>
            );
          }

          return (
            <Fragment key={i}>
              {chunk.split('\n\n').map((subchunk, j) => {
                if (subchunk.startsWith('-')) {
                  return <Fragment key={j}>{renderList(subchunk)}</Fragment>;
                }

                if (subchunk.startsWith('*')) {
                  return (
                    <strong
                      key={j}
                      style={{
                        fontWeight: 500,
                        marginTop: 3,
                        marginBottom: 2,
                      }}
                    >
                      {subchunk.replace(/\*/g, '')}
                    </strong>
                  );
                }

                if (subchunk.startsWith('_')) {
                  return (
                    <strong
                      key={j}
                      style={{
                        fontWeight: 500,
                        marginTop: 3,
                        marginBottom: 2,
                      }}
                    >
                      <em style={{ fontStyle: 'italic' }}>
                        {subchunk.replace(/__/g, '')}
                      </em>
                    </strong>
                  );
                }

                if (descriptionHasIcon) {
                  const [paragraph0, ...paragraphs] = subchunk.split('\n');

                  const shouldRenderAsList = /^-(\s|)/.test(
                    paragraphs[0] ?? '',
                  );

                  const paragraphsMarkup = shouldRenderAsList
                    ? renderList(paragraphs.join('\n'))
                    : paragraphs.join('\n');

                  return (
                    <Flex key={j} sx={{ flexDirection: 'column', gap: 1 }}>
                      <p
                        style={{
                          alignItems: 'flex-end',
                          display: 'flex',
                          lineHeight: 'normal',
                          minHeight: '36px',
                          maxWidth: '360px',
                        }}
                      >
                        {paragraph0}
                      </p>

                      {paragraphsMarkup}
                    </Flex>
                  );
                }

                return (
                  <Text as="span" key={j}>
                    {subchunk}
                  </Text>
                );
              })}
            </Fragment>
          );
        }}
      >
        {ticketStepData?.description ?? ''}
      </InterpolatedString>
    );

    return {
      title,
      description: ticketStepData?.description && (
        <Flex
          sx={{
            flexDirection: descriptionHasIcon ? 'row' : 'column',
            mb: 3,
            gap: 3,
          }}
        >
          {description}
        </Flex>
      ),
      descriptionHasIcon,
      whatsIncluded,
      ticketTypes,
    };
  }, [ticketStepData]);

  const Summary = (): JSX.Element | null => {
    const selections = [...shopCartSelections].filter((sel) => sel.count > 0);

    if (!ticketStepData) return null;

    if (flow === Flow.CITY_PASS || flow === Flow.ALL_IN_PASS) {
      selections.sort((a, b) => {
        if (a.label < b.label) {
          return -1;
        } else if (a.label > b.label) {
          return 1;
        }

        return 0;
      });
    }

    return (
      <div>
        {selections.map((selection) => {
          const ticket = ticketStepData.ticketTypes
            ?.filter(
              (ticketType): ticketType is NonNullable<typeof ticketType> =>
                Boolean(ticketType),
            )
            ?.find(({ type }) => selection.type === type);

          const label =
            ticket && ticket?.productAK === selection.productAK
              ? ticket.label
              : selection.label;

          return (
            <Text key={selection.productAK}>
              {`${selection.count} ${getTicketDisplayTitle(
                flow,
                selection.count,
                label,
              )}`}
            </Text>
          );
        })}
      </div>
    );
  };

  const RinkMembershipSummary = (): React.JSX.Element | null => (
    <div>
      <Text>
        {shopCartSelections.length} Season{' '}
        {pluralize('Pass', shopCartSelections.length)}
      </Text>
    </div>
  );

  // Ticket Selection Validation
  const { isSelectionValid } = useContinueButtonValidation();

  const onClick = () => {
    dispatch(actions.wizard.setNextStep());

    if (flow === Flow.RINK_MEMBERSHIP) {
      counterContext.startCounter();
    }
  };

  const ticketTypes = useMemo(() => {
    return ticketStepData?.ticketTypes?.filter((ticket) => {
      if (flow === Flow.RINK_MEMBERSHIP) {
        if (
          Boolean(ticket) &&
          ticket.statGroup.includes(StatGroup.RINK_MEMBERSHIP)
        ) {
          return true;
        }
        return false;
      }
      return Boolean(ticket);
    });
  }, [ticketStepData]);

  const translatedContinue = 'continue';

  if (!ticketStepData) return null;

  return (
    <Step
      data-section="BuyTicketsTicketStep"
      showLoadingModal={currentStep === stepNumber && isDataLoading}
      stepNumber={stepNumber}
      summary={
        flow === Flow.RINK_MEMBERSHIP ? <RinkMembershipSummary /> : <Summary />
      }
      title={stepData.title}
      width={width}
    >
      <NewTicketQuantity
        continueButton={
          <ContinueButton
            disabled={!isSelectionValid || isDataLoading}
            onClick={onClick}
            sx={{
              width: ['100%', '100%', 'unset'],
              height: 72,
              mt: 3,
              alignSelf: hideLabel ? ['unset', 'flex-end'] : 'unset',
              order: 2,
            }}
            title={translatedContinue}
          />
        }
        description={
          flow === Flow.RINK_MEMBERSHIP ? (
            <Text>
              Season Pass holders access to The Rink may be restricted at times
              the venue is at capacity
            </Text>
          ) : (
            stepData.whatsIncluded || stepData.description
          )
        }
        hideLabel={hideLabel}
        ticketTypes={ticketTypes ?? []}
      />
    </Step>
  );
};

export const transformTicketStep = (
  data: Queries.Maybe<
    Pick<Queries.BuyTicketsFlowJsonTicketStep, 'title' | 'description'> & {
      readonly ticketTypes: Queries.Maybe<
        readonly Queries.Maybe<
          Pick<
            Queries.BuyTicketsFlowJsonTicketStepTicketTypes,
            'label' | 'ticketTypeId'
          >
        >[]
      >;
    }
  >,
): TicketStepProps => ({
  title: data?.title ?? '',
  description: data?.description ?? '',
  ticketTypes: data?.ticketTypes
    ? (data.ticketTypes as unknown as TicketType[])
    : [],
});

export const useContinueButtonValidation = () => {
  const shopCartQuantity = useAppSelector(selectTotalQuantity);
  const event = useCurrentEventByFlow();

  const isRink = event === BosEvent.RINK;

  const hasAdult = useAppSelector(selectHasAdult);
  const hasChaperone = useAppSelector(selectHasChaperone);
  const hasToddler = useAppSelector(selectHasToddler);

  const hasAdultOrChaperoneTickets = hasAdult || hasChaperone;

  const isSelectionValid =
    shopCartQuantity > 0 &&
    (!isRink || (hasAdultOrChaperoneTickets && (hasToddler ? hasAdult : true)));

  const msg = `* 1 Adult ${!hasToddler ? ' or Chaperone ' : ' '} is required`;

  return { isSelectionValid, msg };
};
