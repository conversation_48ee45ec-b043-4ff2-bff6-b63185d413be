/** @jsxImportSource theme-ui @jsxRuntime classic */

import { Flex, SanityRichText } from '@tishman/components';
import React, { useContext, useEffect, useRef, useState, useMemo } from 'react';
import { useStaticQuery, graphql } from 'gatsby';

import {
  AddOnItem,
  BuyTicketsVariantPageData,
  ModalLoader,
  Step,
} from '../../../components/BuyTickets';
import {
  selectFirstTimeSlot,
  selectFlow,
  selectHasPerformances,
  selectIsFetchingPerformancesForAddOns,
  selectPerformances,
  selectShopCartDate,
  selectShopCartDateAsValid,
  selectShopCartDateTime,
  selectShopCartSelections,
  selectTotalQuantity,
  selectWizardCurrentStep,
  useAppSelector,
  useAppDispatch,
  actions,
} from '../../../store';
import {
  AddonLimit,
  AddOnStatGroupsByFlowFlat,
  ADDON_EVENTS_WITH_TIME_CAPACITY,
  BosEventForAddOnStatGroup,
  Flow,
  SINGLE_QTY_ADDONS,
  StatGroupFromAddOnAK,
  DYNAMIC_ADDONS,
  StatGroup,
  ADDON_EVENTS_WITH_CAPACITY,
  SantaDays,
  TICKET_SPECIALS,
} from '../../../services/viva/constants';
import {
  useCheckBasketMutation,
  useFindAllProductByStatGroup,
  useGetDayPerformancesMutation,
} from '../../../services/viva';
import { AddOn } from '../../../components/BuyTickets/AddOns/AddOn';
import { AddToCartButton } from '../../../components/AddToCartButton';
import {
  isWithinSantaTimeConstraints,
  isWithinTimeConstraints,
  putInArray,
  isWithinChampagneToastTimeConstraint,
  isWithinDateConstraints,
} from '../../../services/viva/utils';
import { parseTimeslotTime } from '../../../utils/parse-timeslot-hours';
import { BuyTicketsLayoutContext } from '../../../layouts/BuyTicketsLayout';
import useIsMobile from '../../../breakpoints/useIsMobile';
import addOnMarketingText from '../../../data/translations/addOns';

import { styles } from './BuyTicketsAddOnStepBlock.styles';

import type { ThemeUIStyleObject } from 'theme-ui';

type TProps = {
  data: BuyTicketsVariantPageData;
  stepNumber: number;
  width?: 'wide' | 'default';
  allowedFlows: Flow[];
};

export const useAddOnItems = ({ stepNumber }: { stepNumber: number }) => {
  const [addOns, setAddOns] = useState<AddOnItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const shopCartDateTime = useAppSelector(selectShopCartDate);
  const { day: selectedDay } = shopCartDateTime;
  const flow = useAppSelector(selectFlow);
  const currentStep = useAppSelector(selectWizardCurrentStep);
  const statGroups = AddOnStatGroupsByFlowFlat[flow] ?? [];
  const selectedPerformance = useAppSelector(selectPerformances)[flow][0];
  const shopCartSelections = useAppSelector(selectShopCartSelections);

  const getDayPerformanceMutation = useGetDayPerformancesMutation();
  const checkBasketMutation = useCheckBasketMutation(flow);
  const selectionDates =
    shopCartSelections[0] && shopCartSelections[0].timeSlot
      ? parseTimeslotTime(shopCartSelections[0].timeSlot)
      : [];

  const { data: addOnData } = useFindAllProductByStatGroup({
    statGroups,
    enabled: currentStep === stepNumber && statGroups.length > 0,
  });

  // Responsible for fetching and setting the add-on items whenever the selected performance or add-on data changes.
  // Ensures that the add-ons are updated based on the current state of the shopping cart and available performances.
  useEffect(() => {
    if (!addOnData) return; // Ensure addOnData is available

    const filterAndReorderAddOns = (
      addOns: (AddOnItem | null)[],
    ): AddOnItem[] => {
      const addonOrder = [
        StatGroup.SANTA_PHOTO,
        StatGroup.SKYLIFT,
        StatGroup.THE_BEAM,
        StatGroup.PHOTO_PACKAGE,
        StatGroup.CHAMPAGNE_TOAST,
        StatGroup.RESTAURANT_JUPITER,
        StatGroup.FIVE_ACRES,
      ];

      const filteredAddOns = addOns.filter(
        (addOn): addOn is AddOnItem => addOn !== null,
      );

      filteredAddOns.sort((a, b) => {
        const indexA = addonOrder.indexOf(a.statGroup);
        const indexB = addonOrder.indexOf(b.statGroup);

        // If both add-ons are in the addonOrder, sort by their order
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB;
        }

        // If only one add-on is in the addonOrder, prioritize it
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;

        // If neither add-on is in the addonOrder, maintain their original order
        return 0;
      });

      return filteredAddOns;
    };

    const fetchAddOns = async () => {
      setIsLoading(true);
      try {
        const productList = putInArray(addOnData?.PRODUCTLIST?.PRODUCT);

        // Process each item sequentially to ensure all async operations complete
        const processedAddOns: (AddOnItem | null)[] = [];

        for (let i = 0; i < productList.length; i++) {
          const item = productList[i];

          if (!item || !item.AK || !item.PRICE) {
            processedAddOns.push(null); // Skip this item if it's invalid
            continue;
          }

          const statGroup = StatGroupFromAddOnAK[item.AK];

          let itemPrice = parseFloat(item.PRICE.GROSS);

          // if (TAX_INCLUSIVE_PRODUCTS.includes(statGroup)) {
          //   // Basically, this product comes from the product list with a nice rounded price with
          //   // the tax included. We show the tax as a separate line item so we need to show the
          //   // price without the tax. That is given to us when the inclusive item is sent to checkBasket.
          //   const event = BosEventForAddOnStatGroup[statGroup];

          //   try {
          //     const performanceList =
          //       await getDayPerformanceMutation.mutateAsync({
          //         bosEvent: event,
          //         day: selectedDay,
          //       });

          //     // Filter the performance list to only include performances that have availability,
          //     // for some products we have an array of performances returned and it may be the second
          //     // index that has availability, ex: Bunny Photo
          //     const filteredPerformanceList = performanceList.filter(
          //       (performance) =>
          //         parseInt(performance.AVAILABILITY.AVAILABLE) > 0,
          //     );

          //     if (filteredPerformanceList.length > 0) {
          //       const itemPriceCheckBasket =
          //         await checkBasketMutation.mutateAsync({
          //           items: [
          //             {
          //               AK: item.AK,
          //               QTY: 1,
          //               performances: [filteredPerformanceList[0].AK],
          //             },
          //           ],
          //         });

          //       const _item = putInArray(itemPriceCheckBasket.ITEMLIST.ITEM)[0];
          //       itemPrice = parseFloat(_item.PRICE.NET);
          //     } else {
          //       console.log(
          //         `item ${i + 1} has no performances for tax inclusive item`,
          //       );
          //     }
          //   } catch (error) {
          //     console.error(
          //       `error processing tax inclusive item ${i + 1}:`,
          //       error,
          //     );
          //   }
          // }

          if (DYNAMIC_ADDONS.includes(statGroup)) {
            if (selectionDates && selectionDates[0]) {
              const event = BosEventForAddOnStatGroup[statGroup];

              try {
                const performanceList =
                  await getDayPerformanceMutation.mutateAsync({
                    bosEvent: event,
                    day: selectedDay,
                  });

                if (performanceList && performanceList.length) {
                  const filteredPerformanceList = performanceList.filter(
                    (performance) =>
                      parseInt(performance.AVAILABILITY.AVAILABLE) > 0,
                  );

                  if (filteredPerformanceList.length > 0) {
                    const basket = await checkBasketMutation.mutateAsync({
                      items: [
                        {
                          AK: item.AK,
                          QTY: 1,
                          performances: [filteredPerformanceList[0].AK],
                        },
                      ],
                    });

                    const _item = putInArray(basket.ITEMLIST.ITEM)[0];
                    itemPrice = parseInt(_item.PRICE.NET);
                  } else {
                    console.log(
                      `item ${
                        i + 1
                      } has no available performances for dynamic addon`,
                    );
                  }
                } else {
                  console.log(
                    `item ${i + 1} has no performances for dynamic addon`,
                  );
                }
              } catch (error) {
                console.error(
                  `error processing dynamic addon item ${i + 1}:`,
                  error,
                );
              }
            } else {
              console.log(
                `item ${i + 1} has no selection dates for dynamic addon`,
              );
            }
          }

          const addOn: AddOnItem = {
            ak: item.AK,
            price: itemPrice,
            description: item.DESCRIPTION,
            marketingText: '',
            name: item.NAME,
            statGroup,
            flow,
          };

          processedAddOns.push(addOn);
        }

        setAddOns(filterAndReorderAddOns(processedAddOns));
      } catch (error) {
        console.error('Error in fetchAddOns:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAddOns();
  }, [selectedPerformance, addOnData]);

  return { addOns, isLoading };
};

// Move Sanity query and utilities here
const getDescriptionText = (
  description:
    | { children?: { text: string }[] }[]
    | { children?: { text: string }[] },
): string => {
  if (!description) return '';

  // If description is an array of blocks
  if (Array.isArray(description)) {
    return description
      .map((block) => {
        // Get all text from children
        if (block.children) {
          return block.children
            .map((child) => child.text)
            .filter(Boolean)
            .join(' ');
        }
        return '';
      })
      .filter(Boolean)
      .join('\n');
  }

  // If description is a single block
  if (description.children) {
    return description.children
      .map((child) => child.text)
      .filter(Boolean)
      .join(' ');
  }

  return '';
};

const useSanityAddons = () => {
  const data = useStaticQuery(graphql`
    query GetAddons {
      allSanityAddon {
        edges {
          node {
            _id
            name
            image {
              asset {
                url
                metadata {
                  dimensions {
                    width
                    height
                  }
                }
              }
              alt
              caption
            }
            _rawDescription
            productAK
            additionalLabel
            _rawAdditionalText
          }
        }
      }
    }
  `);

  return useMemo(() => {
    return data.allSanityAddon.edges.reduce(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (acc: { [key: string]: any }, edge: any) => {
        const node = edge.node;
        acc[node.productAK] = {
          name: node.name,
          description: getDescriptionText(node._rawDescription),
          image: node.image?.asset?.url || null,
          imageAlt: node.image?.alt || '',
          additionalLabel: node.additionalLabel,
          additionalText: node._rawAdditionalText,
        };
        return acc;
      },
      {},
    );
  }, [data]);
};

export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
  const dispatch = useAppDispatch();
  const availablePerformancesHash =
    useAppSelector((state) => state.order.shopCart.addOnsPerformancesHash) ??
    {};

  const { isAddOnLoading } = useContext(BuyTicketsLayoutContext);

  const flow = useAppSelector(selectFlow);
  const currentStep = useAppSelector(selectWizardCurrentStep);
  const isValidDate = useAppSelector(selectShopCartDateAsValid);
  const selectedTimeSlot = useAppSelector(selectFirstTimeSlot);
  const shopCartDate = useAppSelector(selectShopCartDate);
  const shopCartDateTime = useAppSelector(selectShopCartDateTime);
  const shopCartQty = useAppSelector(selectTotalQuantity);
  const { addOns, isLoading } = useAddOnItems({ stepNumber });
  const hasPerformances = useAppSelector(selectHasPerformances);
  const sanityAddons = useSanityAddons();

  const evaluateSpecial = (statgroup: StatGroup, hour: number) => {
    return false;
    // Disable the special
    // return statgroup === StatGroup.THE_BEAM && hour >= 21;
  };
  const isFetchingPerformances = useAppSelector(
    selectIsFetchingPerformancesForAddOns,
  );

  const title = 'Add Ons';

  const isDateStepBlock =
    Flow.TOR_EXPRESS === flow || Flow.RC_ROCK_PASS === flow;

  const scrollToRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (currentStep !== stepNumber || !scrollToRef.current) return;

    const elementViewportTop = scrollToRef.current.getBoundingClientRect().top;
    const scrollYPosition = window.scrollY;
    const offset = 160;

    const top = elementViewportTop + scrollYPosition - offset;

    window.scrollTo({
      top,
      behavior: 'smooth',
    });
  }, [currentStep, stepNumber, isMobile]);

  const addOnShouldShow = (addOn: AddOnItem) => {
    const addOnIsTimed = ADDON_EVENTS_WITH_TIME_CAPACITY.includes(
      BosEventForAddOnStatGroup[addOn.statGroup],
    );

    const addOnHasCapacity = ADDON_EVENTS_WITH_CAPACITY.includes(
      BosEventForAddOnStatGroup[addOn.statGroup],
    );

    if (!availablePerformancesHash[addOn.ak]) {
      // AddOn ${addOn.ak} not found in availablePerformancesHash
      return { shouldShow: false, isTimed: false };
    }

    if (
      (addOnIsTimed || addOnHasCapacity) &&
      (availablePerformancesHash[addOn.ak]?.available ?? 0) === 0
    ) {
      // AddOn ${addOn.ak} has no available performances
      return { shouldShow: false, isTimed: false };
    }

    const { day, month } = shopCartDate;
    const dateKey = `${month + 1}-${day.toString().padStart(2, '0')}`;

    if (
      flow === Flow.TOR_EXPRESS &&
      addOn.statGroup === StatGroup.PHOTO_PACKAGE
    ) {
      const isASantaDay = SantaDays.indexOf(dateKey) !== -1;
      if (isASantaDay) {
        // Photo Package not shown on Santa day ${dateKey} for TOR_EXPRESS
        return { shouldShow: false, isTimed: false };
      }
    }

    if (
      (flow === Flow.TOR_GA || flow === Flow.THE_BEAM_PASS) &&
      (addOn.statGroup === StatGroup.PHOTO_PACKAGE ||
        addOn.statGroup === StatGroup.SANTA_PHOTO ||
        addOn.statGroup === StatGroup.BUNNY_PHOTO)
    ) {
      const { hours, minutes } = selectedTimeSlot?.startRaw || {
        hours: 0,
        minutes: '00',
      };
      const showSantaPhoto = isWithinSantaTimeConstraints(
        dateKey,
        hours,
        minutes,
      );
      const showEasterBunnyPhoto =
        isWithinTimeConstraints(StatGroup.BUNNY_PHOTO, hours, minutes) &&
        isWithinDateConstraints(StatGroup.BUNNY_PHOTO, dateKey);

      if (
        addOn.statGroup === StatGroup.PHOTO_PACKAGE &&
        (showSantaPhoto || showEasterBunnyPhoto)
      ) {
        // Photo Package not shown when Santa Photo OR Easter Bunny Photo is available for ${flow}
        return { shouldShow: false, isTimed: false };
      }
      if (addOn.statGroup === StatGroup.SANTA_PHOTO && showSantaPhoto) {
        // Santa Photo shown for ${flow}
        return { shouldShow: true, isTimed: addOnIsTimed };
      }
      if (addOn.statGroup === StatGroup.BUNNY_PHOTO && showEasterBunnyPhoto) {
        // Easter Bunny Photo shown for ${flow}
        return { shouldShow: true, isTimed: addOnIsTimed };
      }
    }

    if (addOn.statGroup === StatGroup.CHAMPAGNE_TOAST) {
      const date = new Date(shopCartDateTime);
      if (
        [
          Flow.TOR_GA,
          Flow.TOR_EXPRESS,
          Flow.RC_ROCK_PASS,
          Flow.THE_BEAM_PASS,
        ].includes(flow) &&
        isWithinChampagneToastTimeConstraint(
          date,
          selectedTimeSlot?.startRaw.hours ?? 0,
          selectedTimeSlot?.startRaw.minutes ?? '00',
        )
      ) {
        // AddOn ${addOn.ak} should be shown for flow ${flow}
        return { shouldShow: true, isTimed: addOnIsTimed };
      }
    }

    if (
      flow !== Flow.TOR_EXPRESS &&
      flow !== Flow.TOR_VIP_ROCKSTAR &&
      flow !== Flow.RC_ROCK_PASS_VIP_HOLIDAY &&
      !isWithinTimeConstraints(
        addOn.statGroup,
        selectedTimeSlot?.startRaw.hours ?? 0,
        selectedTimeSlot?.startRaw.minutes ?? '00',
      )
    ) {
      // AddOn ${addOn.ak} not within time constraints for flow ${flow}
      return { shouldShow: false, isTimed: false };
    }

    // AddOn ${addOn.ak} should be shown for flow ${flow}
    return { shouldShow: true, isTimed: addOnIsTimed };
  };

  const noAddonsAvailableToShow = addOns.every((addOn) => {
    const doNotShow = !addOn.statGroup || !addOnShouldShow(addOn).shouldShow;
    return doNotShow;
  });

  // Handle add-on selections at the parent level
  useEffect(() => {
    if (!addOns?.length) return;

    const addOnSelections = addOns
      .filter((addOn) => addOn && addOn.ak) // Ensure addOn exists and has ak
      .map((addOn) => {
        const sanityData = addOn.ak ? sanityAddons[addOn.ak] : null;
        const marketingCopy =
          sanityData?.description ||
          (addOnMarketingText.byFlow[flow]?.[addOn.statGroup]?.copy ??
            addOnMarketingText.byStatGroup[addOn.statGroup]?.copy ??
            '');

        return {
          description: marketingCopy,
          ak: addOn.ak,
          price: addOn.price * 100,
          marketingText: addOn.marketingText || '',
          name: sanityData?.name || addOn.name,
          image: sanityData?.image || null,
          quantity: 0,
          isAddingOn: false,
          statGroup: addOn.statGroup,
          flow,
        };
      });

    // Only dispatch if we have valid selections
    if (addOnSelections.length > 0) {
      dispatch(actions.order.setShopCartAddOnSelections(addOnSelections));
    } else {
      // If no valid selections, clear the add-ons
      // dispatch(actions.order.setShopCartAddOnSelections([]));
    }
  }, [addOns, flow, dispatch, sanityAddons]);

  // Hide step until we get here
  if (currentStep !== stepNumber) return null;
  // We're now at this step in the flow, but we don't have any addons to show
  // We can skip this step and add to cart
  if (
    !addOns.some((addOn) => {
      return addOnShouldShow(addOn).shouldShow;
    }) &&
    !isAddOnLoading
  ) {
    return null;
  }
  if (isAddOnLoading && isLoading) return <ModalLoader />;

  return (
    <Step
      data-section="BuyTicketsAddOnsStepBlock"
      data-testid="add-on-step"
      showLoadingModal={isAddOnLoading}
      stepIsValid={
        isDateStepBlock
          ? selectedTimeSlot !== undefined
          : isValidDate && selectedTimeSlot !== undefined
      }
      stepNumber={stepNumber}
      title={noAddonsAvailableToShow ? '' : title}
      width={width}
    >
      <Flex ref={scrollToRef} sx={styles.container as ThemeUIStyleObject}>
        {addOns?.map((addOn) => {
          const { shouldShow, isTimed: addOnIsTimed } = addOnShouldShow(addOn);

          if (!shouldShow) return null;

          const sanityData = addOn.ak ? sanityAddons[addOn.ak] : null;
          const marketingCopy =
            sanityData?.description ||
            (addOnMarketingText.byFlow[flow]?.[addOn.statGroup]?.copy ??
              addOnMarketingText.byStatGroup[addOn.statGroup]?.copy ??
              '');

          const currentHour = selectedTimeSlot?.startRaw.hours || 0;
          const ticketSpecial = TICKET_SPECIALS[addOn.statGroup];
          const ticketOnSpecial =
            ticketSpecial &&
            ticketSpecial.onSpecial &&
            (!ticketSpecial.hoursActive?.length ||
              (ticketSpecial.hoursActive[0] <= currentHour &&
                (!ticketSpecial.hoursActive[1] ||
                  ticketSpecial.hoursActive[1] >= currentHour)));

          // Function to determine the maximum quantity of a specific add-on
          const maxQuantity = () => {
            if (SINGLE_QTY_ADDONS.includes(addOn.statGroup)) {
              return 1;
            } else if (Object.keys(AddonLimit).includes(addOn.statGroup)) {
              return AddonLimit[addOn.statGroup];
            }

            return addOnIsTimed
              ? Math.min(
                  availablePerformancesHash[addOn.ak]?.available ?? 0,
                  shopCartQty,
                )
              : shopCartQty;
          };

          return (
            <AddOn
              additionalInfo={
                sanityData?.additionalLabel
                  ? {
                      additionalLabel: sanityData.additionalLabel,
                      additionalText: sanityData.additionalText ? (
                        <SanityRichText blocks={sanityData.additionalText} />
                      ) : (
                        ''
                      ),
                    }
                  : undefined
              }
              addOn={addOn}
              available={availablePerformancesHash[addOn.ak]?.available ?? 0}
              image={sanityData?.image}
              key={addOn.ak}
              marketingText={marketingCopy}
              maxQuantity={maxQuantity()}
              name={sanityData?.name}
              ticketSpecial={ticketOnSpecial ? ticketSpecial : undefined}
            />
          );
        })}
        <AddToCartButton
          disabled={
            flow !== Flow.RC_ROCK_PASS &&
            flow !== Flow.TOR_EXPRESS &&
            (!hasPerformances || isLoading)
          }
          isAddOn
          isLoading={isAddOnLoading || isFetchingPerformances}
          stepNumber={stepNumber}
        />
      </Flex>
    </Step>
  );
};
