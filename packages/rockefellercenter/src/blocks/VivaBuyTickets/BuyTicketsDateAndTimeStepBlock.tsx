/** @jsxImportSource theme-ui @jsxRuntime classic */
import React, { useContext, useEffect, useMemo } from 'react';
import { Box, Flex, Text } from '@tishman/components';
import * as Icons from '@tishman/icons';

import {
  Step,
  StepProps,
  TimePicker,
  useBuyTicketsModal,
} from '../../components/BuyTickets';
import { Translatable, useTranslations } from '../../utils/use-translations';
import { formatMoney, usePageData } from '../../utils';
import {
  selectFirstTimeSlot,
  selectFlow,
  selectShopCartDateTime,
  useAppSelector,
} from '../../store';
import { BuyTicketsLayoutContext } from '../../layouts/BuyTicketsLayout';
import { SUNSET_ADD_ON_PRICE } from '../../buy-tickets/constants/constants';
import {
  FLOWS_WITH_DISCOUNTED_TICKETS,
  Flow,
} from '../../services/viva/constants';
import { isRinkRelated } from '../../services/viva/utils/is-rink-related';
import { formatToLongDate } from '../../utils/format-date';

import { BuyTicketsCalendar } from './BuyTicketsCalendar';
import { useContinueButtonValidation } from './BuyTicketsTicketStepBlock';

import type { TicketTypesForFlow } from '../../data/translations/ticketType';

type Props = {
  stepNumber: number;
  width?: StepProps['width'];
  isLastStep?: boolean;
  isVip?: boolean;
};

export const BuyTicketsDateAndTimeStepBlock = ({
  isLastStep = false,
  isVip = false,
  stepNumber,
  width,
}: Props) => {
  const { getDaysAvailabilityQuery, isSunsetSlotSelected } = useContext(
    BuyTicketsLayoutContext,
  );
  const isSuccess = getDaysAvailabilityQuery?.isSuccess ?? false;

  const { setValue: setModal } = useBuyTicketsModal();
  const { page } = usePageData<Queries.BuyTicketsTorQuery>();
  const currentStep = useAppSelector((state) => state.wizard.currentStep);
  const shopCartDate = useAppSelector(selectShopCartDateTime);
  const selectedTimeSlot = useAppSelector(selectFirstTimeSlot);
  const flow = useAppSelector(selectFlow);

  const translations = useTranslations<TicketTypesForFlow>(
    Translatable.TicketType,
  );
  // if ticket selection is valid
  const { isSelectionValid } = useContinueButtonValidation();

  const hours = useMemo(() => {
    if (!selectedTimeSlot?.startRaw.hours) return null;

    return selectedTimeSlot.startRaw.hours % 12 === 0
      ? 12
      : selectedTimeSlot.startRaw.hours % 12;
  }, [selectedTimeSlot?.startRaw.hours]);

  // Upsell modal logic
  useEffect(() => {
    if (
      currentStep === stepNumber &&
      !isVip &&
      !isRinkRelated(flow) &&
      ![Flow.NONE, Flow.TREE_PHOTO, Flow.TOR_GA].includes(flow)
    ) {
      setModal(new Error('UPSELL'));
    }
  }, [currentStep, flow, isVip, setModal, stepNumber]);

  const formattedDate = formatToLongDate({
    dateTime: shopCartDate ? new Date(shopCartDate).toISOString() : '',
    includesDayOfWeek: true,
  });

  return (
    <Step
      data-section="BuyTicketsDateAndTimeStepBlock"
      showLoadingModal={false}
      stepIsValid={isSelectionValid}
      stepNumber={stepNumber}
      summary={
        selectedTimeSlot ? (
          <Box>
            <Text
              data-date={formattedDate}
              data-testid="date-time-step-selected-date"
            >
              {formattedDate}
            </Text>
            <Text
              data-date={formattedDate}
              data-testid="date-time-step-selected-time"
            >
              {`${hours}:${selectedTimeSlot.startRaw.minutes} ${selectedTimeSlot.startRaw.meridian}`}
              {(() => {
                if (!isSuccess) return null;

                return isSunsetSlotSelected && flow !== Flow.RC_TOUR
                  ? ` (Sunset +${formatMoney(SUNSET_ADD_ON_PRICE)})`
                  : null;
              })()}
            </Text>
          </Box>
        ) : null
      }
      title={
        translations?.forFlow[flow]?.dateStep ?? page?.dateStep?.title ?? ''
      }
      width={width}
    >
      <Flex
        sx={{
          flexDirection: ['column', 'column', 'column', 'row'],
          justifyContent: [null, null, 'space-between'],
          rowGap: [6, 6, 6, 3],
          columnGap: 4,
        }}
      >
        <BuyTicketsCalendar stepNumber={stepNumber} />
        {/* TIME PICKER */}
        <Flex
          sx={{
            flexDirection: 'column',
            flex: 1,
            maxWidth: ['none', 'none', 'none', '500px'],
            transform: ['none', 'none', 'none', 'translateY(-46px)'],
            rowGap: [3, 3, 4],
          }}
        >
          <Text
            as="p"
            data-date={formattedDate}
            data-testid="selected-date"
            variant="mediumP"
          >
            {formattedDate}
          </Text>
          <Flex
            sx={{
              flexDirection: 'column',
            }}
          >
            <Text variant="mediumTitle">Time</Text>
            {FLOWS_WITH_DISCOUNTED_TICKETS.includes(flow) && (
              <Flex
                backgroundColor="OLIVE"
                sx={{
                  padding: '14px',
                  borderRadius: '4px',
                  marginTop: '16px',
                }}
              >
                <Box
                  sx={{
                    'display': 'inline-flex',
                    'color': 'OLIVE',
                    'stroke': 'black',
                    'svg path': { stroke: 'black' },
                  }}
                >
                  <Icons.DiscountTag />
                </Box>
                <Text
                  sx={{
                    fontWeight: '500',
                    lineHeight: '150%',
                    marginLeft: '8px',
                  }}
                  variant="mediumP"
                >
                  Online Exclusive - Save on Evening Tickets!
                </Text>
              </Flex>
            )}
          </Flex>
          <TimePicker isLastStep={isLastStep} stepNumber={stepNumber} />
        </Flex>
      </Flex>
    </Step>
  );
};
