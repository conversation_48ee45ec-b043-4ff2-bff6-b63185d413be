/** @jsxImportSource theme-ui @jsxRuntime classic */
import {
  Flex,
  Text,
  Grid,
  Spinner,
  Button,
  Box,
  Link,
} from '@tishman/components';
import React, { useCallback, useContext, useEffect, useMemo } from 'react';
import { CalendarArrow } from '@tishman/icons';
import { differenceInDays, startOfMonth, startOfWeek } from 'date-fns';
import { monthDays, range } from '@formkit/tempo';

import {
  actions,
  selectDateInNYCToday,
  selectFlow,
  selectShopCartDate,
  selectShopCartTimeAsDate,
  selectWizardCurrentStep,
  useAppDispatch,
  useAppSelector,
} from '../../store';
import { BuyTicketsLayoutContext } from '../../layouts/BuyTicketsLayout';
import { usePageData } from '../../utils';
import { isRinkRelated } from '../../services/viva/utils/is-rink-related';
import { rinkMonth } from '../../store/slices';
import { getDateString } from '../../utils/format-date';
import { useCapacityAddon } from '../../utils/use-capacity-addon';
import { newRelic } from '../../utils/new-relic';
import { Flow, checkAdaptivePricing } from '../../services/viva/constants';
import { PerformanceResponseObject } from '../../services/viva/types';
import { putInArray } from '../../services/viva/utils';

type Props = {
  twoColumn?: boolean;
  continueButton?: React.ReactNode;
  stepNumber?: number;
};

export const BuyTicketsCalendar = ({
  twoColumn = false,
  continueButton,
  stepNumber = 2,
}: Props) => {
  const { page, allSanityTicketAvailabilityOverride } =
    usePageData<Queries.BuyTicketsAllInPassQuery>();

  const { getDaysAvailabilityQuery, getDayPerformances } = useContext(
    BuyTicketsLayoutContext,
  );
  const isLoading = getDaysAvailabilityQuery?.isLoading;

  const availableDays = getDaysAvailabilityQuery?.data;

  const filteredAvailableDays = useMemo(() => {
    if (!allSanityTicketAvailabilityOverride?.nodes?.[0]?.availableDates) {
      return availableDays;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pickDateFromISOString = (availableDate: any) =>
      availableDate?.date?.split('T')[0];

    const availableDatesFromSanity =
      allSanityTicketAvailabilityOverride.nodes[0].availableDates.map(
        pickDateFromISOString,
      );

    const newAvailableDays = new Map();

    availableDatesFromSanity?.forEach((day: string) => {
      if (availableDays?.has(day)) {
        newAvailableDays.set(day, availableDays?.get(day));
      }
    });

    return newAvailableDays;
  }, [allSanityTicketAvailabilityOverride, availableDays]);

  useCapacityAddon();

  const dispatch = useAppDispatch();
  const shopCartDate = useAppSelector(selectShopCartDate);
  const shopCartDateTime = useAppSelector(selectShopCartTimeAsDate);
  const wizardYear = useAppSelector((state) => state.wizard.datePicker.year);
  const wizardMonth = useAppSelector((state) => state.wizard.datePicker.month);
  const flow = useAppSelector(selectFlow);
  const dateInNYCToday = useAppSelector(selectDateInNYCToday);
  const currentStep = useAppSelector(selectWizardCurrentStep);

  const hasAdaptivePricing = checkAdaptivePricing(flow);

  useEffect(() => {
    const isSameYear = shopCartDate.year === shopCartDateTime.getFullYear();
    const isSameMonth = shopCartDate.month === shopCartDateTime.getMonth();
    const isSameDay = shopCartDate.day === shopCartDateTime.getDate();

    if (!(isSameYear && isSameMonth && isSameDay)) {
      const dateTime = todayInNYC;

      dateTime.setDate(shopCartDate.day);
      dateTime.setMonth(shopCartDate.month);

      dispatch(
        actions.order.setShopCartDate({
          day: filteredAvailableDays
            ? parseInt(
                Array.from(filteredAvailableDays.keys())
                  .sort()[0]
                  .split('-')[2],
              )
            : shopCartDate.day,
          month: shopCartDate.month,
          year: shopCartDate.year,
          time: 0,
        }),
      );
      dispatch(actions.order.setShopCartDateTime(dateTime.valueOf()));
      // Express doesn't have a Timepicker so setting the date here sets the product and performance
      // state on load
    }
  }, [shopCartDate, shopCartDateTime]);

  useEffect(() => {
    if (flow === Flow.TOR_EXPRESS) setSelectedDate(shopCartDate.day);
  }, [currentStep]);

  // MONTH LOGIC & FORMATTING
  const todayInNYC = useMemo(
    () => new Date(dateInNYCToday.date),
    [dateInNYCToday.date],
  );
  // Get month name from wizardMonth
  const monthIndex = wizardMonth;
  const monthNames = range('MMMM', 'en');
  const monthName = monthNames[monthIndex];
  // Create a new Date object for the first day of the wizardMonth in wizardYear
  const wizardDate = new Date(wizardYear, wizardMonth, 1);
  // Now use wizardDate instead of todayInNYC for calculating daysInMonth, monthStart, and weekStart
  const monthStart = startOfMonth(wizardDate);
  const weekStart = startOfWeek(monthStart);

  const setSelectedDate = (day: number) => {
    const dateTime = todayInNYC;
    dateTime.setDate(day);
    dateTime.setHours(0, 0, 0, 0);
    dateTime.setMonth(wizardMonth);
    dateTime.setFullYear(wizardYear);

    // Format the date string with padded month and day
    const dateStr = `${wizardYear}-${String(wizardMonth + 1).padStart(
      2,
      '0',
    )}-${String(day).padStart(2, '0')}`;
    dispatch(actions.order.setShopCartDateTime(dateTime.valueOf()));
    dispatch(
      actions.order.setShopCartDate({
        year: wizardYear,
        month: wizardMonth,
        day,
        time: 0,
      }),
    );
    dispatch(actions.order.resetShopCartSelectionsTimeSlots());

    newRelic.logCartAction(
      'BuyTicketsCalendar - Set selected Date',
      'setSelectedDate',
      {
        setShopCartDateTime: dateTime.valueOf(),
        setShopCartDate: {
          year: wizardYear,
          month: wizardMonth,
          day,
          time: 0,
        },
      },
    );

    getDayPerformances?.(dateStr).then(
      (response: PerformanceResponseObject[] | void) => {
        if (hasAdaptivePricing && response) {
          putInArray(response[0]?.PRODUCTLIST?.PRODUCT).forEach((product) => {
            dispatch(
              actions.order.updateShopCartSelectionsPrice({
                productAK: product?.AK,
                price: product?.PRICE * 100,
                tax: 0,
              }),
            );
          });
        }
      },
    );
  };

  // Returns a boolean indicating whether the provided date is available.
  const getIsDateAvailable = useCallback(
    (date: string) => {
      const hasPerformances = filteredAvailableDays?.get(date)?.available;

      return hasPerformances;
    },
    [filteredAvailableDays],
  );

  // Check if we can go back one month in calendar
  const canGoBackOneMonth = useMemo(() => {
    if (isRinkRelated(flow) && wizardMonth === rinkMonth()) return false;

    if (wizardMonth === dateInNYCToday.month) return false;

    return true;
  }, [flow, dateInNYCToday.month, wizardMonth]);

  // Legend component
  type LegendBoxProps = {
    backgroundColor: string;
    opacity: string;
    label: string;
  };

  const isFutureDate = (dayIndex: number) => {
    const currentYear = dateInNYCToday.year;
    const currentMonth = dateInNYCToday.month;
    const currentDate = dateInNYCToday.day;

    if (wizardYear > currentYear) return true;
    if (wizardMonth > currentMonth) return true;

    return dayIndex > currentDate;
  };

  const isSoldOut = (dayIndex: number) =>
    !isLoading &&
    isFutureDate(dayIndex + 1) &&
    !getIsDateAvailable(getDateString(wizardYear, wizardMonth, dayIndex + 1)) &&
    filteredAvailableDays?.get(
      getDateString(wizardYear, wizardMonth, dayIndex + 1),
    );

  const isDayActive = (dayIndex: number) =>
    shopCartDate &&
    shopCartDate.year === wizardYear &&
    shopCartDate.month === wizardMonth &&
    shopCartDate.day === dayIndex + 1;

  const LegendBox = ({ backgroundColor, opacity, label }: LegendBoxProps) => (
    <Flex sx={{ alignItems: 'center' }}>
      <Box
        backgroundColor={backgroundColor}
        sx={{
          borderRadius: '50%',
          width: ['20px', '25px'],
          height: ['20px', '25px'],
          opacity: opacity || 1,
          position: 'relative',
        }}
      >
        {label === 'Sold Out' && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%) rotate(45deg)',
              width: '100%',
              height: '1px',
              backgroundColor: 'white',
            }}
          />
        )}
      </Box>
      <Text sx={{ fontWeight: 400, mx: [2, 3] }} variant="smallP">
        {label}
      </Text>
    </Flex>
  );

  const buyPremiumText =
    flow === Flow.RINK ? (
      <Text as="span" sx={{ fontWeight: 'semiBold' }}>
        {' '}
        If your desired time is sold out, a limited number of immediate-access
        Skate Now tickets are available at The Rink Box Office each day or check
        out{' '}
        <Link
          href="/buy-tickets/the-rink-vip"
          id="BuyPremiumSkateLink"
          variant="text"
        >
          Premium Skate
        </Link>{' '}
        for availability at your desired time.
      </Text>
    ) : (
      ''
    );

  return (
    <Flex
      sx={{
        flexDirection: ['column', 'column', twoColumn ? 'row' : 'column'],
        flex: 1,
        alignItems: ['center', 'center', 'flex-start'],
      }}
    >
      {twoColumn && page?.dateStep?.description && (
        <Flex
          sx={{
            alignItems: twoColumn ? 'flex-start' : 'center',
            flexDirection: 'column',
            width: '100%',
            maxWidth: '286px',
            marginRight: 'auto',
            rowGap: 2,
          }}
        >
          <Text as="p" sx={{ fontSize: 1 }}>
            {page.dateStep.description} {buyPremiumText}
          </Text>
          {/* TODO : Bring back if possible & necessary */}
          {/* <Text
            sx={{
              fontWeight: 500,
            }}
          >
            {formatToLongDate({ dateTime: shopCartDateTime })}
          </Text> */}
        </Flex>
      )}
      <Flex
        sx={{
          flexDirection: 'column',
          flex: 1,
          width: '100%',
          maxWidth: ['none', 'none', 'none', '458px'],
          rowGap: '32px',
          transform: [
            'none',
            'none',
            'none',
            twoColumn ? 'translateY(-46px)' : 'none',
          ],
          position: 'relative',
        }}
      >
        {!twoColumn && page?.dateStep?.description && (
          <Flex
            sx={{
              alignItems: 'center',
              flexDirection: 'column',
              gap: 3,
            }}
          >
            <Text as="p" sx={{ fontSize: 1 }}>
              {page.dateStep.description} {buyPremiumText}
            </Text>
          </Flex>
        )}
        {/* MONTH SELECTION */}
        <Flex
          sx={{
            rowGap: '8px',
          }}
        >
          <Flex
            sx={{
              flex: 1,
              justifyContent: 'space-between',
            }}
          >
            <Text data-testid="month-name" variant="mediumTitle">
              {monthName}
            </Text>
            <Flex
              sx={{
                columnGap: '8px',
              }}
            >
              <Button
                data-testid="prev-month-button"
                disabled={!canGoBackOneMonth}
                onClick={() => {
                  const prevMonth = (wizardMonth - 1 + 12) % 12;
                  if (prevMonth === 11) {
                    dispatch(actions.wizard.setSelectedYear(wizardYear - 1));
                  }
                  dispatch(actions.wizard.setIsAdvancingMonths(false));
                  dispatch(actions.wizard.setSelectedMonth(prevMonth));
                  newRelic.logCartAction(
                    'BuyTicketsCalendar - Navigate to previous month',
                    'onClick',
                    {
                      setSelectedYear: wizardYear - 1,
                      setIsAdvancingMonths: false,
                      setSelectedMonth: prevMonth,
                    },
                  );
                }}
                sx={{
                  'background': 'none',
                  'border': 'none',
                  'color': 'inherit',
                  'cursor': 'pointer',
                  'padding': 0,
                  'transform': 'scaleX(-1)',
                  'transition': 'opacity 0.2s ease-in-out',
                  ':disabled': {
                    backgroundColor: 'transparent',
                    opacity: 0.15,
                  },
                }}
              >
                <CalendarArrow />
              </Button>
              <Button
                data-testid="next-month-button"
                onClick={() => {
                  const nextMonth = (wizardMonth + 1) % 12;

                  if (nextMonth === 0) {
                    dispatch(actions.wizard.setSelectedYear(wizardYear + 1));
                  }
                  dispatch(actions.wizard.setIsAdvancingMonths(true));
                  dispatch(actions.wizard.setSelectedMonth(nextMonth));
                  newRelic.logCartAction(
                    'BuyTicketsCalendar - Navigate to next month',
                    'onClick',
                    {
                      setSelectedYear: wizardYear + 1,
                      setIsAdvancingMonths: true,
                      setSelectedMonth: nextMonth,
                    },
                  );
                }}
                sx={{
                  background: 'none',
                  border: 'none',
                  color: 'inherit',
                  cursor: 'pointer',
                  padding: 0,
                }}
              >
                <CalendarArrow />
              </Button>
            </Flex>
          </Flex>
        </Flex>
        {/* LEGEND */}
        <Flex sx={{ justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <LegendBox backgroundColor="#D8D8D8" label="Available" opacity="1" />
          <LegendBox
            backgroundColor="#D8D8D8"
            label="Unavailable"
            opacity="0.35"
          />
          <LegendBox backgroundColor="#7b736a" label="Sold Out" opacity="1" />
        </Flex>
        {/* CALENDAR */}
        <Grid columns={['repeat(7, 1fr)']}>
          {/* DAYS OF THE WEEK */}
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, dayIndex) => (
            <Flex
              key={dayIndex}
              sx={{
                alignItems: 'center',
                aspectRatio: '1',
                justifyContent: 'center',
              }}
            >
              <Text
                as="span"
                sx={{
                  fontSize: ['20px', null, null, '24px'],
                  lineHeight: '1',
                }}
              >
                {day}
              </Text>
            </Flex>
          ))}
          {/* EMPTY SPACES FOR DAYS THAT ARE NOT IN THE MONTH TO FILL OUT THE CALENDAR */}
          {Array.from(
            {
              length: differenceInDays(monthStart, weekStart),
            },
            (_, i) => {
              return <Flex key={i} sx={{ aspectRatio: '1' }} />;
            },
          )}
          {/* CIRCULAR CALENDAR BUTTONS FOR EACH DAY */}
          {Array.from({
            length: monthDays(new Date(wizardYear, wizardMonth, 1)),
          }).map((_, dayIndex) => {
            return !isSoldOut(dayIndex) ? (
              <Box
                as="button"
                data-testid="calendar-day"
                disabled={
                  (dayIndex + 1 < dateInNYCToday.day &&
                    wizardMonth === dateInNYCToday.month) ||
                  !getIsDateAvailable(
                    getDateString(wizardYear, wizardMonth, dayIndex + 1),
                  )
                }
                key={dayIndex}
                onClick={() => {
                  setSelectedDate(dayIndex + 1);
                  newRelic.logCartAction(
                    'BuyTicketsCalendar - setSelectedDate on calendar day click',
                    'onClick',
                    {
                      setSelectedDate: dayIndex + 1,
                    },
                  );
                }}
                sx={{
                  'alignItems': 'center',
                  'aspectRatio': '1',
                  'backgroundColor': isDayActive(dayIndex) ? 'text' : '#D8D8D8',
                  'border': 'none',
                  'borderRadius': '100%',
                  'color': isDayActive(dayIndex) ? 'background' : 'inherit',
                  'cursor': 'pointer',
                  'display': 'flex',
                  'justifyContent': 'center',
                  'padding': 0,
                  'position': 'relative',
                  'transition':
                    'background-color 0.15s ease-in-out, color 0.15s ease-in-out, outline-color 0.15s ease-in-out',
                  '&::after': {
                    borderRadius: '100%',
                    bottom: '-7px',
                    content: '""',
                    display: 'block',
                    left: '-7px',
                    pointerEvents: 'none',
                    position: 'absolute',
                    right: '-7px',
                    top: '-7px',

                    // Active state
                    ...(isDayActive(dayIndex)
                      ? {
                          border: '2px solid black',
                        }
                      : {}),
                  },

                  // DISABLED STATE
                  // This logic checks if the date is disabled:
                  // 1. If it's a past date in the current month
                  // 2. Or if the date is not available according to the getIsDateAvailable function
                  ...((dayIndex + 1 < dateInNYCToday.day &&
                    wizardMonth === dateInNYCToday.month) ||
                  !getIsDateAvailable(
                    getDateString(wizardYear, wizardMonth, dayIndex + 1),
                  )
                    ? {
                        opacity: '0.35',
                        pointerEvents: 'none',
                      }
                    : {}),

                  '&:active': {
                    '&::after': {
                      borderColor: 'text',
                    },
                  },

                  '&:hover': {
                    backgroundColor: 'text',
                    color: 'background',
                  },
                }}
              >
                {/* NUMBERS */}
                <Text
                  as="span"
                  sx={{
                    color: 'inherit',
                    fontSize: ['20px', null, null, '24px'],
                    lineHeight: '1',
                  }}
                >
                  {dayIndex + 1}
                </Text>
              </Box>
            ) : (
              <Box
                as="button"
                key={dayIndex}
                sx={{
                  alignItems: 'center',
                  aspectRatio: '1',
                  backgroundColor: '#7b736a',
                  border: 'none',
                  borderRadius: '100%',
                  color: 'white',
                  cursor: 'unset',
                  display: 'flex',
                  justifyContent: 'center',
                  padding: 0,
                  position: 'relative',
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) rotate(45deg)',
                    width: '100%',
                    height: '2px',
                    backgroundColor: 'white',
                  }}
                />
                {/* NUMBERS */}
                <Text
                  as="span"
                  sx={{
                    color: 'inherit',
                    fontSize: ['20px', null, null, '24px'],
                    lineHeight: '1',
                  }}
                >
                  {dayIndex + 1}
                </Text>
              </Box>
            );
          })}
        </Grid>
        {continueButton}
        {/* LOADING */}
        {isLoading && (
          <Spinner
            sx={{
              display: 'block',
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
          />
        )}
      </Flex>
    </Flex>
  );
};
