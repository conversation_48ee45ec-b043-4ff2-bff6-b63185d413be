import type {
  DataProviderC<PERSON>,
  LangCode,
  EventCategoryCode,
  SearchAccountObjectType,
  StatGroup,
  SlugToBosEventMap,
  Flow,
  BosEvent,
  PerformanceCategory,
} from '../../constants';
import type { Shift4SaleResponse } from '../response';

// Common
interface PageReq {
  PAGEINDEX: number;
  PAGESIZE: number;
  SORTFIELD: string;
}

// SearchEvent
export interface SearchEventRequestDTO {
  SEARCHEVENTREQ: SearchEventReq;
}

export interface SearchEventRequest {
  eventCategoryCodes: EventCategoryCode[];
}

export interface GetAllProductsByEventSlugRequest {
  slug: keyof typeof SlugToBosEventMap;
}

export interface FindAllProductsByStatGroupRequest {
  statGroups: StatGroup[];
  enabled: boolean;
}

interface SearchEventReq {
  EVENTCATEGORYLIST: EventCategoryList[];
}

interface EventCategoryList {
  EVENTCATEGORY: EventCategory;
}

interface EventCategory {
  CODE: EventCategoryCode;
}

export interface PrintPdfTicketRequestDTO {
  ASaleAk: string;
}

export interface PrintPdfTicketRequest {
  saleAK: string;
}

export interface GetDaysAvailabilityRequestDTO {
  GETDAYSAVAILABILITYREQ: GetDaysAvailabilityReq;
}

export interface GetDaysAvailabilityRequest {
  date: { from: string; to: string };
  events: string[];
  sellable?: boolean;
}

interface GetDaysAvailabilityReq {
  DATE: FromTo;
  // TIME: FromTo;
  SELLABLE?: boolean;
  SPACESTRUCTUREAVAIL?: boolean;
  EVENTFILTERLIST?: EventFilterList[];
  PAGEREQ?: PageReq;
}

export interface GetDayPerformancesRequestDTO {
  GETDAYPERFORMANCESREQ: GetDayPerformancesReq;
}

export interface GetDayPerformancesRequest {
  date: string;
  time: { from: string; to: string };
  events: string[] | BosEvent[];
}

export interface GetDayPerformancesResponse {
  PERFORMANCELIST: PerformanceList;
}

interface GetDayPerformancesReq {
  DATE: string;
  TIME: FromTo;
  SELLABLE?: boolean;
  SPACESTRUCTUREAVAIL?: boolean;
  RETURNPRODUCTINFO?: boolean;
  EVENTFILTERLIST?: EventFilterList[];
  PAGEREQ?: PageReq;
}

export interface SearchCalendarAvailabilityRequestDTO {
  SEARCHCALENDARAVAILABILITYREQ: SearchCalendarAvailabilityReq;
}

export interface SearchCalendarAvailabilityRequest {
  date: { from: string; to: string };
  time?: { from: string; to: string } | null;
  day_only: boolean;
  events: string[];
}

interface SearchCalendarAvailabilityReq {
  DATE: FromTo;
  TIME?: FromTo | null;
  SELLABLE?: boolean;
  RETURNDAYONLY?: boolean;
  EVENTFILTERLIST?: EventFilterList[];
  PAGEREQ?: PageReq;
}

export interface FindAllPerformanceByCategoriesRequestDTO {
  FINDALLPERFORMANCEBYCATEGORIESREQ: FindAllPerformanceByCategoriesReq;
}

export interface FindAllPerformanceByCategoriesRequest {
  date: string;
  categories: string[];
}

interface FindAllPerformanceByCategoriesReq {
  DATE: FromTo;
  SELLABLE?: boolean;
  PERFORMANCECATEGORYLIST: { CATEGORY: { CODE: string } }[];
  PAGEREQ?: PageReq;
}

export interface SearchCalendarAvailabilityResponse {
  DAYLIST: {
    DAY: Array<{
      PERFORMANCELIST: {
        PERFORMANCE: Array<{
          AK: string;
        }>;
      };
    }>;
  };
}

interface EventFilterList {
  EVENT: Event;
}

interface Event {
  AK: string;
}

interface FromTo {
  FROM: string;
  TO: string;
}

// FindAllPerformanceProductByAKV2
export interface FindAllPerformanceProductByAkV2RequestDTO {
  FINDALLPERFORMANCEPRODUCTBYAKREQ: FindAllPerformanceByProductAkV2Req;
}

export interface ReadPerformanceByAKRequestDTO {
  APerformanceAK: string;
}

export interface ReadPerformanceByAKRequest {
  performanceAK: string;
}

export interface FindAllPerformanceProductByAkV2Request {
  performanceAK?: string;
  performanceTime?: number;
  statGroup?: StatGroup[][];
  priceTableAK?: string;
}
interface FindAllPerformanceByProductAkV2Req {
  PERFORMANCEAK: string;
  STATGROUPLIST: Array<StatGroupList>;
  PAGEREQ?: PageReq;
}

interface StatGroupList {
  STATGROUPITEM: StatGroupItem[];
}

interface StatGroupItem {
  CODE: StatGroup;
}

// DataMask
// ReadDataProviderByCode
export interface ReadDataProviderByCodeDTO {
  ADataProviderCode: DataProviderCode;
  ALangCode: LangCode;
}

export interface ReadDataProviderByCodeRequest {
  dataProviderCode: DataProviderCode;
  langCode?: LangCode;
}

// ReadAccountCategoryByAK
export interface ReadAccountCategoryByAKRequestDTO {
  ADmgCategoryAK: string;
}

export interface ReadAccountCategoryByAKRequest {
  ADmgCategoryAK: string;
}

// SearchAccount
export interface SearchAccountRequestDTO {
  SEARCHACCOUNTREQ: SearchAccountReq;
}

export interface SearchAccountRequest {
  email: string;
}

interface SearchAccountReq {
  FILTERLIST: FilterList[];
  PAGEREQ: PageReq;
}

interface FilterList {
  FILTER: Filter;
}

interface Filter {
  OBJTYPE: SearchAccountObjectType;
  VALUE: string;
}

// SaveAccount
export interface SaveAccountRequestDTO {
  SAVEACCOUNTREQ: SaveAccountReq;
}

export interface SaveAccountRequest {
  accountAK?: string;
  flow: Flow;
  fields: SaveAccountField[];
}

export interface SaveAccountField {
  value: boolean | number | string;
  type: number;
}

interface SaveAccountReq {
  ACCOUNTAK?: string;
  FIELDLIST: FieldList[];
  DMGCATEGORYAK: string;
}

interface FieldList {
  FIELD: Field;
}

interface Field {
  VALUE: boolean | number | string;
  OBJTYPE: number;
}

// CheckOut
export interface CheckOutRequestDTO {
  CHECKOUTREQ: CheckOutReq;
}

export interface CheckBasketRequestDTO {
  CHECKBASKETREQ: CheckBasketReq;
}

export interface CheckOutRequest {
  items: CheckOutItem[];
  accountAK?: string;
  flow: Flow;
  coupon?: string;
  capacityManagement?: boolean;
}

export interface CheckBasketRequest {
  items: CheckBasketItem[] | CheckBasketItem;
  coupon?: string | null;
  capacityManagement?: boolean;
}

export interface CheckBasketItem {
  AK: string;
  QTY: number;
  performances: string[];
  capacityGuid?: string;
}

export interface CheckOutItem {
  AK: string;
  QTY: number;
  performances: string[];
  capacityGuid?: string;
  voucherCode?: string;
  accountAK?: string;
  isAddOn?: boolean;
  flow?: Flow;
}

interface CheckOutReq {
  SHOPCART: ShopCart;
}

interface CheckBasketReq {
  SHOPCART: ShopCart;
  CAPACITYMANAGEMENT: boolean;
  IGNOREWARNINGS: boolean;
}

interface PackageList {
  PACKAGE: Package;
}

interface Package {
  CODE: number | string;
  ITEMLIST: ItemList[];
}

interface ShopCart {
  ITEMLIST?: ItemList[];
  PACKAGELIST?: PackageList[];
  FLAG: Flag;
  RESERVATION: Reservation;
  COUPONLIST: CouponList | null;
}

interface Reservation {
  RESERVATIONOWNER: ReservationOwner;
  PERFORMANCELIST?: Record<string, unknown>[];
  AK?: string;
}

interface ReservationOwner {
  AK: string;
}

interface Flag {
  APPROVED: boolean;
  PAID: boolean;
  ENCODED: boolean;
  VALIDATED: boolean;
  COMPLETED: boolean;
}

export interface ItemList {
  ITEM: ITEM;
}

interface ITEM {
  AK: string;
  QTY: number;
  PERFORMANCELIST: PerformanceList[];
  TICKETHOLDERLIST?: TicketHolderList[];
  CAPACITYGUID?: string;
}

export interface TicketHolderList {
  TICKETHOLDER: TicketHolder;
}

interface TicketHolder {
  AK: string;
}

interface PerformanceList {
  PERFORMANCE: Performance;
}

interface Performance {
  AK: string;
}

// CloseOrder
export interface CloseOrderRequestDTO {
  CloseOrderReq: CloseOrderReq;
}

export interface CloseOrderRequest {
  flow: Flow;
  saleAK: string;
  amount?: number;
  payment?: Shift4SaleResponse;
}

interface CloseOrderReq {
  AK: string;
  PAYMENTINFOLIST: PaymentInfoList;
}

interface PaymentInfoList {
  PAYMENTINFO: PaymentInfo[];
}

// type PaymentInfoCode = 'WCCARD' | 'CASH' | 'WEBCREDITCARD' | 'MCARD';

interface PaymentInfo {
  CODE: string;
  AMOUNT: number;
  CREDITCARDINFO?: CreditCardInfo;
}

interface CreditCardInfo {
  ACQREFDATA?: string;
  APPLABEL?: string;
  APPROVALCODE?: string;
  AVSRESULTCODE?: string;
  B24CODE?: string;
  CARDHOLDERNAME?: string;
  CARDNUMBER?: string;
  CCVRESULTCODE?: string;
  CHVERIFICATIONMODE?: string;
  CREDITCARDTYPE?: string;
  ENDVALIDITY?: string;
  ISOCODE?: string;
  RECORDNO?: string;
  REFERENCENUMBER?: string;
  RETRIEVALREFERENCE?: string;
  SEQUENCENUMBER?: string;
  STARTVALIDITY?: string;
  SYSTEMTRACEAUDITNUMBER?: string;
  TRANSACTIONDATETIME?: string;
  TRANSACTIONDATETIMEGMT?: string;
  VERIFICATIONRESULT?: string;
}

// ReadOrderByAK

export interface ReadOrderByAKRequestDTO {
  AOrderAK: string;
}
export interface ReadOrderByAKRequest {
  orderAK: string;
}
export interface AbortSaleRequest {
  orderAK: string;
}

export interface AbortSaleRequestDTO {
  AOrderAK: string;
}

// VIVA Response Specific Types
export interface PerformanceResponseObject {
  AK: string;
  AVAILABILITY: AVAILABILITY;
  TIME: string;
  RAW_TIME: {
    hours: number;
    minutes: string;
    meridian: string;
  };
  STATUS: string;
  SELLABLE: string;
  ENDTIME: string;
  ENDDATETIME: string;
  REACHABLE: string;
  PRICETABLEAK: string;
  EVENTAK: string;
  SPACESTRUCTURELIST: SPACESTRUCTURELIST;
  WAITLISTENABLED: string;
  TRANSFERRABLE: string;
  PRODUCTLIST: PRODUCTLIST;
}

interface PRODUCTLIST {
  PRODUCT: PRODUCT[];
}

interface PRODUCT {
  AK: string;
  PRICE: string;
}

interface SPACESTRUCTURELIST {
  SPACESTRUCTURE: SPACESTRUCTURE;
}

interface SPACESTRUCTURE {
  SPACESTRUCTUREAK: string;
  CAPACITYSEATCATEGORYLIST: CAPACITYSEATCATEGORYLIST;
}

interface CAPACITYSEATCATEGORYLIST {
  CAPACITYSEATCATEGORY: CAPACITYSEATCATEGORY;
}

interface CAPACITYSEATCATEGORY {
  CODE: string;
  DESCRIPTION: string;
  AVAILABILITY: AVAILABILITY;
}

interface AVAILABILITY {
  TOTAL: string;
  AVAILABLE: string;
  GENERALADMISSION: string;
}

export interface BOSProduct {
  AK: string;
  NAME: string;
  CODE: string;
  DESCRIPTION: string;
  PRICETABLE: {
    AK: string;
  };
  PRODUCTTYPE: string;
  EVENTLIST: {
    EVENTBASE: { AK: string; CODE: string };
  };
  PRICE: {
    CURRENCY: string;
    GROSS: string;
    NET: string;
    PRINTED: string;
    TAX: string;
    TAXLIST: {
      TAX: {
        NAME: string;
        RATE: string;
        VALUE: string;
      };
    };
  };
  PRICELIST: {
    CODE: string;
    ID: string;
  };
  STATGROUPLIST: {
    STATGROUP: {
      DESCRIPTION: string;
      CODE: string;
    }[];
  };
  status: string;
}

export interface ValidateCouponCodeRequest {
  coupon: string;
}

export interface ValidateCouponCodeRequestDTO {
  ACouponCode: string;
}

interface CouponList {
  COUPON: Coupon[];
}

interface Coupon {
  CODE: string | null;
}

export interface SearchReservationRequest {
  reservationAK: string;
}

export interface SearchReservationRequestDTO {
  SEARCHRESERVATIONREQ: {
    RESERVATION: {
      AK: string;
    };
  };
}

export interface SearchReservationResponse {
  RESERVATIONITEMLIST: {
    RESERVATIONITEM: Reservation | Reservation[];
  };
  GRANDTOTAL: {
    AMOUNT: string;
    TAX: string;
  };
}

export interface DeleteReservationRequest {
  reservationCode: string;
  total: number;
}

export interface DeleteReservationRequestDTO {
  DELETERESERVATIONREQ: {
    RESERVATIONAK: string;
    PAYMENTINFOLIST: {
      PAYMENTINFO: {
        CODE: string;
        AMOUNT: number;
        CREDITCARDINFO?: {
          APPROVALCODE: string;
          CREDITCARDTYPE: string;
          SEQUENCENUMBER: string;
          RETRIEVALREFERENCE: string;
          CARDNUMBER: string;
          STARTVALIDITY: string;
          ENDVALIDITY: string;
          CARDHOLDERNAME: string;
          CHVERIFICATIONMODE: string;
          VERIFICATIONRESULT: string;
          APPLABEL: string;
          B24CODE: string;
          ISOCODE: string;
          SYSTEMTRACEAUDITNUMBER: string;
          TRANSACTIONDATETIME: string;
          TRANSACTIONDATETIMEGMT: string;
          AVSRESULTCODE: string;
          CCVRESULTCODE: string;
          REFERENCENUMBER: string;
        };
      };
    };
  };
}

export interface DeleteReservationResponse {
  ERROR: {
    CODE: string;
  };
}

export interface ReadExternalPackageByCodeRequest {
  APackageCode: string;
  APackageSerial: string;
}
