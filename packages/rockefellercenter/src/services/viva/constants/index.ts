import { CardType } from '../types/payments';
import { ROUTES } from '../../../constants';
import {
  ENV_ALL_IN_PASS_ADULT_PEAK_AK,
  ENV_ALL_IN_PASS_SENIOR_PEAK_AK,
  ENV_ALL_IN_PASS_CHILD_PEAK_AK,
  ENV_BUNNY_PHOTO_PRODUCT_AK,
  ENV_FIVE_ACRES_PRODUCT_AK,
  ENV_ON_THE_ROCKS_EVENT_AK,
} from '../../../environment';

import type { FluidObject } from 'gatsby-image';

export { VivaAPIOperation } from './soap';
export { VivaAPIType } from './soap';

export const CART_COUNTER_TIME_LIMIT = 15;
export const CART_LOCAL_STORAGE_KEY = 'rc_cart';

export enum Flow {
  ALL_IN_PASS = 'ALL_IN_PASS',
  C3 = 'C3',
  CART = 'CART',
  CHALETS = 'CHALETS',
  CHECKOUT = 'CHECKOUT',
  CITY_PASS = 'CITY_PASS',
  CITY_PASS_REDEMPTION = 'CITY_PASS_REDEMPTION',
  CITY_PASS_UPGRADE = 'CITY_PASS_UPGRADE',
  DECK = 'DECK',
  NONE = 'NONE',
  ON_THE_ROCKS = 'ON_THE_ROCKS',
  RC_ROCK_PASS = 'RC_ROCK_PASS',
  RC_ROCK_PASS_VIP_HOLIDAY = 'RC_ROCK_PASS_VIP_HOLIDAY',
  RC_TOUR = 'RC_TOUR',
  RC_TOUR_KIDS_AND_FAMILY = 'RC_TOUR_KIDS_AND_FAMILY',
  REDEMPTION = 'REDEMPTION',
  REFUND = 'REFUND',
  RINK = 'RINK',
  RINK_MEM_WITH_SKATES = 'RINK_MEMBERSHIP_SKATES',
  RINK_MEMBERSHIP = 'RINK_MEMBERSHIP',
  RINK_VIP = 'RINK_VIP',
  SANTA_PHOTO = 'SANTA_PHOTO',
  SKYLIFT = 'SKYLIFT',
  THE_BEAM_PASS = 'THE_BEAM_PASS',
  TOR_EXPRESS = 'TOR_EXPRESS',
  TOR_GA = 'TOR_GA',
  TOR_VIP = 'TOR_VIP',
  TOR_VIP_ROCKSTAR = 'TOR_VIP_ROCKSTAR',
  TREE_PHOTO = 'TREE_PHOTO',
}

export type FlowAsKeyType = {
  [key in Flow]: string;
};

export const FlowPrettyPrint = {
  [Flow.ALL_IN_PASS]: 'All-In Pass',
  [Flow.C3]: 'C3',
  [Flow.CART]: 'Cart',
  [Flow.CHALETS]: 'Chalets',
  [Flow.CHECKOUT]: 'Checkout',
  [Flow.CITY_PASS_REDEMPTION]: 'City Pass Redemption',
  [Flow.CITY_PASS_UPGRADE]: 'City Pass Upgrade',
  [Flow.CITY_PASS]: 'City Pass',
  [Flow.DECK]: 'Observation Deck',
  [Flow.NONE]: 'None',
  [Flow.ON_THE_ROCKS]: 'On the Rocks',
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 'VIP Rock Pass Holiday Edition',
  [Flow.RC_ROCK_PASS]: 'Rock Pass',
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 'Rockefeller Center Tour, Jr.',
  [Flow.RC_TOUR]: 'Rockefeller Center Tour',
  [Flow.REDEMPTION]: 'Redemption',
  [Flow.REFUND]: 'Refund',
  [Flow.RINK_MEM_WITH_SKATES]: 'Season Pass with Skates',
  [Flow.RINK_MEMBERSHIP]: 'Season Pass',
  [Flow.RINK_VIP]: 'Premium Skate',
  [Flow.RINK]: 'General Skate',
  [Flow.SANTA_PHOTO]: 'Santa Photo Experience',
  [Flow.THE_BEAM_PASS]: 'The Beam Pass',
  [Flow.TOR_EXPRESS]: 'Express Pass',
  [Flow.TOR_GA]: 'General Admission',
  [Flow.TOR_VIP_ROCKSTAR]: 'VIP Rock Pass',
  [Flow.TOR_VIP]: 'VIP',
  [Flow.TREE_PHOTO]: 'Tree Photo Package',
} as FlowAsKeyType;

export enum AccountMask {
  Country = 11,
  Password = 549,
  FirstName = 1,
  LastName = 3,
  Email = 21,
  MobileNumber = 15,
  RinkMembershipMobileNumber = 12,
  Address1 = 6,
  Address2 = 7,
  State = 9,
  City = 8,
  ZipCode = 10,
  OptInMail = 5002,
  OptInSMS = 5003,
  IPAddress = 60,
  DOB = 24,
}

export const AccountDmgCategoryAK = {
  [Flow.ALL_IN_PASS]: 'TOR.DMGCAT38',
  [Flow.CHALETS]: 'TOR.DMGCAT37',
  [Flow.CHECKOUT]: 'TOR.DMGCAT38',
  [Flow.CITY_PASS]: 'TOR.DMGCAT129',
  [Flow.DECK]: 'TOR.DMGCAT38',
  [Flow.ON_THE_ROCKS]: 'TOR.DMGCAT38',
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 'TOR.DMGCAT186',
  [Flow.RC_ROCK_PASS]: 'TOR.DMGCAT38',
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 'TOR.DMGCAT38',
  [Flow.RC_TOUR]: 'TOR.DMGCAT38',
  [Flow.REDEMPTION]: 'TOR.DMGCAT38',
  [Flow.RINK_MEMBERSHIP]: 'TOR.DMGCAT36',
  [Flow.RINK_VIP]: 'TOR.DMGCAT38',
  [Flow.RINK]: 'TOR.DMGCAT37',
  [Flow.SANTA_PHOTO]: 'TOR.DMGCAT38',
  [Flow.SKYLIFT]: 'TOR.DMGCAT38',
  [Flow.THE_BEAM_PASS]: 'TOR.DMGCAT38',
  [Flow.TOR_EXPRESS]: 'TOR.DMGCAT38',
  [Flow.TOR_GA]: 'TOR.DMGCAT38',
  [Flow.TOR_VIP_ROCKSTAR]: 'TOR.DMGCAT186',
  [Flow.TOR_VIP]: 'TOR.DMGCAT38',
  [Flow.TREE_PHOTO]: 'TOR.DMGCAT38',
} as FlowAsKeyType;

export const DmgCategoryAK = 'TOR.DMGCAT38';

export enum DataProviderCode {
  Country = 'COUNTRIES',
  USState = 'STATES',
}
export enum LangCode {
  zhCN = 'zh-CN',
  deDE = 'de-DE',
  enUS = 'en-US',
  esEX = 'es-EX',
  frFR = 'fr-FR',
  enGB = 'en-GB',
  zhHK = 'zh-HK',
  itIT = 'it-IT',
  ptPT = 'pt-PT',
}

// TODO: CLEAN UP
// TODO: This is out of date but currently unused.
// TODO: Currently referenced in DTO mapping func for searchEvent called in useSearchEventQuery
export enum EventCategoryCode {
  TopOfTheRock = 'TOR',
  TORDeckAdmissions = 'TORADM',
  TORTour = 'TORTOUR',
  RockefellerRinkCenter = 'RRC',
  RockefellerRink = 'RRCRINK',
}

export enum PaymentCode {
  Rink = 'RRC-CC-EC',
  TOR = 'TOR-CC-EC',
  CityPass = 'TOR-CP',
}

export enum PaymentResult {
  CityPassAPIOffline = 9,
  CityPassOrderFailure = 10,
  Declined = 2,
  Failed = 3,
  GuestExceededPurchasingCapacity = 7,
  InvalidSaleState = 6,
  None = 0,
  PaymentAlreadyProcessed = 5,
  PaymentGatewayCannotBeReached = 8,
  SaleTimeout = 4,
  Success = 1,
}

export enum PriceType {
  Fixed = 0,
  Variable = 1,
  Overridable = 2,
  RoundUp = 3,
  PercentageFixed = 4,
  PercentageVariable = 5,
  BasedOnPerformance = 6,
  DynamicPricing = 7,
}

export enum SearchAccountObjectType {
  email = 21,
}

// TODO: scope statgroups under events types
export enum StatGroup {
  FIVE_ACRES = '5ACRES',
  AIPPPEAKA = 'AIPPPEAKA', // All In Pass with (POST) Peak Adult
  AIPPPEAKC = 'AIPPPEAKC', // All In Pass with (POST) Peak Child
  AIPPPEAKS = 'AIPPPEAKS', // All In Pass with (POST) Peak Senior
  ALLINPSKYA = 'ALLINPSKYA', //All In Pass with Skylift Adult
  ALLINPSKYC = 'ALLINPSKYC', //All In Pass with Skylift Child
  ALLINPSKYS = 'ALLINPSKYS', // All In Pass with Skylift Senior
  BEANIE = 'BEANIE',
  BUNNY_PHOTO = 'EASTERBUN',
  CHALETS = 'CHALETTIER',
  CHAMPAGNE_TOAST = 'CHAMPTST', // Weather Room
  CHAMPAGNE_TOAST_COMP = 'BARCAFCOMP', // Weather Room Comp
  CITY_PASS_ADULT = '&CTYPSSAD',
  CITY_PASS_CHILD = '&CTYPSCH',
  CITY_PASS_REDEMPTION_ADULT = '&CTYPSSADR',
  CITY_PASS_REDEMPTION_CHILD = '&CTYPSSCHR',
  GA = '-GA',
  GA_ADULT = '&ADULT',
  GA_CHILD = '&CHILD',
  GA_SENIOR = '&SENIOR',
  GA_TODDLER = '&TODDLER', // Not currently being used
  HOT_COCOA = 'HOTCOCO',
  ON_THE_ROCKS = 'ONTHEROCK',
  ORNAMENT = 'ORNAMENT',
  PHOTO_PACKAGE = 'PHOTOPKG',
  PHOTO_PACKAGE_COMP = 'PHOTOPKGCM',
  RC_ROCK_PASS = '&ROCKPASS',
  RC_ROCK_PASS_VIP_HOLIDAY = 'VIPRPHOL',
  RC_TOUR = '&ROCKTOUR',
  RC_TOUR_KIDS_AND_FAMILY = 'RCTKF',
  RESTAURANT_JUPITER = 'JUPITERPRI',
  RINK = '+WEBRRCSP', // WEB Rink
  RINK_ADULT = '=ADU',
  RINK_CHAPERONE = '=CHAP',
  RINK_CHILD = '=CHD',
  RINK_GA = '+GA',
  RINK_MEM_WITH_LOCKER = '=MEM+LCK',
  RINK_MEM_WITH_SKATES = '=MEM+SKT',
  RINK_MEM_WITH_SKATES_LOCKER = '=MEM+SKTLK',
  RINK_MEMBERSHIP = '=MEM',
  RINK_TODDLER = '=TOD',
  RINK_VIP = '=VIPRINK',
  SANTA_PHOTO = 'SANTAPHOTO',
  SKATE_RENTAL = '=SKTRNT',
  SKYLIFT = 'SKYLIFT',
  SKYLIFT_COMP = 'SKYLIFTCMP',
  SUNSET = 'SUNSET',
  THE_BEAM = 'THEBEAM',
  THE_BEAM_COMP = 'BEAMWEBCMP',
  THE_BEAM_PASS = 'ALLINPASS',
  TOR = '-TOR',
  TOR_VIP = '&VIP',
  TOR_VIP_ROCKSTAR = 'VIPROCKPAS', // RockPASS VIP
  TOR_XPASS = '&XPASSALL',
  TREE_PHOTO = 'TREEPHOTO',
}

export enum PerformanceCategory {
  ALL_IN_PASS_POST_PEAK = 'AIPPPEAK',
  TOR_GA_SUNSET = 'GASUNSET',
  FIREWORKS = 'GAJULY4FW',
  ON_THE_ROCKS = 'ONROCK',
}

export const FLOWS_WITH_DISCOUNTED_TICKETS: Flow[] = [];

export const FLOWS_WITH_COMP_TICKETS = [
  Flow.TOR_VIP,
  Flow.RC_ROCK_PASS_VIP_HOLIDAY,
  Flow.TOR_VIP_ROCKSTAR,
];

export const COMP_TICKETS = [
  StatGroup.THE_BEAM_COMP,
  StatGroup.PHOTO_PACKAGE_COMP,
  StatGroup.SKYLIFT_COMP,
  StatGroup.CHAMPAGNE_TOAST_COMP,
];

export type StatGroupByEventType = {
  [key: string]: StatGroup[][];
};

export type StatGroupByFlowType = Partial<{ [key in Flow]: StatGroup[][] }>;

export const ProductOrderByFlow: Partial<{ [key in Flow]: StatGroup[] }> = {
  [Flow.ALL_IN_PASS]: [
    StatGroup.ALLINPSKYA,
    StatGroup.ALLINPSKYC,
    StatGroup.ALLINPSKYS,
  ],
  [Flow.ON_THE_ROCKS]: [StatGroup.ON_THE_ROCKS],
};

export const AddOnStatGroupsByFlow: StatGroupByFlowType = {
  [Flow.ALL_IN_PASS]: [
    [StatGroup.PHOTO_PACKAGE],
    [StatGroup.CHAMPAGNE_TOAST],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.ORNAMENT],
  ],
  [Flow.TOR_GA]: [
    [StatGroup.THE_BEAM],
    [StatGroup.CHAMPAGNE_TOAST],
    [StatGroup.PHOTO_PACKAGE],
    [StatGroup.SANTA_PHOTO],
    [StatGroup.BUNNY_PHOTO],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.SKYLIFT],
    [StatGroup.ORNAMENT],
  ],
  [Flow.TOR_EXPRESS]: [
    [StatGroup.THE_BEAM],
    [StatGroup.CHAMPAGNE_TOAST],
    [StatGroup.PHOTO_PACKAGE],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.SKYLIFT],
    [StatGroup.ORNAMENT],
  ],
  [Flow.TOR_VIP]: [
    [StatGroup.THE_BEAM_COMP],
    [StatGroup.PHOTO_PACKAGE_COMP],
    [StatGroup.CHAMPAGNE_TOAST_COMP],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.SKYLIFT_COMP],
    [StatGroup.ORNAMENT],
  ],
  [Flow.TOR_VIP_ROCKSTAR]: [
    [StatGroup.THE_BEAM_COMP],
    [StatGroup.PHOTO_PACKAGE_COMP],
    [StatGroup.CHAMPAGNE_TOAST_COMP],
    [StatGroup.SKYLIFT_COMP],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.ORNAMENT],
  ],
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: [
    [StatGroup.THE_BEAM_COMP],
    [StatGroup.PHOTO_PACKAGE_COMP],
    [StatGroup.CHAMPAGNE_TOAST_COMP],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
    [StatGroup.SKYLIFT_COMP],
  ],
  [Flow.RINK]: [
    [StatGroup.CHALETS],
    [StatGroup.SKATE_RENTAL],
    [StatGroup.BEANIE],
    [StatGroup.TREE_PHOTO],
    [StatGroup.ORNAMENT],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
  ],
  [Flow.CHALETS]: [[StatGroup.BEANIE], [StatGroup.ORNAMENT]],
  [Flow.RINK_VIP]: [
    [StatGroup.CHALETS],
    [StatGroup.BEANIE],
    [StatGroup.TREE_PHOTO],
    [StatGroup.ORNAMENT],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.FIVE_ACRES],
  ],
  [Flow.RINK_MEMBERSHIP]: [[StatGroup.RINK_MEM_WITH_SKATES]],
  [Flow.SANTA_PHOTO]: [[StatGroup.SANTA_PHOTO]],
  [Flow.TREE_PHOTO]: [[StatGroup.BEANIE], [StatGroup.ORNAMENT]],
  [Flow.RC_TOUR]: [[StatGroup.TREE_PHOTO]],
  [Flow.RC_ROCK_PASS]: [
    [StatGroup.THE_BEAM],
    [StatGroup.CHAMPAGNE_TOAST],
    [StatGroup.PHOTO_PACKAGE],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.SKYLIFT],
    [StatGroup.FIVE_ACRES],
    [StatGroup.ORNAMENT],
  ],
  [Flow.THE_BEAM_PASS]: [
    [StatGroup.CHAMPAGNE_TOAST],
    [StatGroup.SANTA_PHOTO],
    [StatGroup.PHOTO_PACKAGE],
    [StatGroup.RESTAURANT_JUPITER],
    [StatGroup.SKYLIFT],
  ],
};

export const AddOnStatGroupsByFlowFlat = Object.fromEntries(
  Object.entries(AddOnStatGroupsByFlow).map(
    ([key, value]) => [key, value?.flat() ?? []] as [Flow, StatGroup[]],
  ),
);

export type TAddOn = {
  itemName: string;
  statGroup: StatGroup;
  title: string;
  copy?: string;
  image?: FluidObject;
};

export type AddOnByStatGroupType = Partial<{ [key in StatGroup]: TAddOn }>;

export const AddOnItemByStatGroup: AddOnByStatGroupType = {
  [StatGroup.THE_BEAM]: {
    itemName: 'beam',
    statGroup: StatGroup.THE_BEAM,
    title: 'The Beam',
  },
  [StatGroup.CHAMPAGNE_TOAST]: {
    itemName: 'bar cafè item',
    statGroup: StatGroup.CHAMPAGNE_TOAST,
    title: 'Champagne Toast',
  },
  [StatGroup.SKYLIFT]: {
    itemName: 'skylift',
    statGroup: StatGroup.SKYLIFT,
    title: 'Skylift',
  },
  [StatGroup.PHOTO_PACKAGE]: {
    itemName: 'pass',
    statGroup: StatGroup.PHOTO_PACKAGE,
    title: 'Photo Pass',
  },
  [StatGroup.SKATE_RENTAL]: {
    itemName: 'rental',
    statGroup: StatGroup.SKATE_RENTAL,
    title: 'Skate Rentals',
  },
  [StatGroup.BEANIE]: {
    itemName: 'beanie',
    statGroup: StatGroup.BEANIE,
    title: 'Beanie',
  },
  [StatGroup.ORNAMENT]: {
    itemName: 'ornament',
    statGroup: StatGroup.ORNAMENT,
    title: 'Rockefeller Center Ornament',
  },
  [StatGroup.HOT_COCOA]: {
    itemName: 'hot cocoa',
    statGroup: StatGroup.HOT_COCOA,
    title: 'Hot Cocoa',
  },
  [StatGroup.TREE_PHOTO]: {
    itemName: 'tree photo',
    statGroup: StatGroup.TREE_PHOTO,
    title: 'Tree Photo',
  },
  [StatGroup.SANTA_PHOTO]: {
    itemName: 'santa photo',
    statGroup: StatGroup.SANTA_PHOTO,
    title: 'Santa Photo Experience',
  },
  [StatGroup.BUNNY_PHOTO]: {
    itemName: 'bunny photo',
    statGroup: StatGroup.BUNNY_PHOTO,
    title: 'Easter Bunny Photo Pass',
  },
  [StatGroup.RESTAURANT_JUPITER]: {
    itemName: 'jupiter',
    statGroup: StatGroup.RESTAURANT_JUPITER,
    title: '3 Course Meal at Jupiter',
  },
  [StatGroup.FIVE_ACRES]: {
    itemName: '5 acres',
    statGroup: StatGroup.FIVE_ACRES,
    title: '5 Acres',
  },
};

export const StatGroupByEvent = {
  'top-of-the-rock': [
    [StatGroup.TOR],
    [StatGroup.GA],
    [StatGroup.GA_ADULT],
    [StatGroup.GA_CHILD],
    [StatGroup.GA_SENIOR],
  ],
  'express-pass': [[StatGroup.TOR], [StatGroup.TOR_XPASS]],
  'vip': [[StatGroup.TOR], [StatGroup.TOR_VIP]],
  [ROUTES.VIP_ROCKSTAR]: [[StatGroup.TOR], [StatGroup.TOR_VIP_ROCKSTAR]],
  [ROUTES.RC_ROCK_PASS_VIP_HOLIDAY]: [
    [StatGroup.TOR],
    [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY],
  ],
  'the-rink': [
    [StatGroup.RINK],
    [StatGroup.RINK_GA],
    [StatGroup.RINK_ADULT],
    [StatGroup.RINK_CHILD],
    [StatGroup.RINK_TODDLER],
    [StatGroup.RINK_CHAPERONE],
  ],
  'rock-center-tour': [[StatGroup.RINK], [StatGroup.GA], [StatGroup.RC_TOUR]],
  [ROUTES.RC_TOUR_KIDS_AND_FAMILY]: [[StatGroup.RC_TOUR_KIDS_AND_FAMILY]],

  'the-rink-membership': [
    [StatGroup.RINK_MEMBERSHIP],
    [StatGroup.RINK_MEM_WITH_SKATES],
  ],
  'rock-pass': [[StatGroup.RC_ROCK_PASS]],
  'citypass': [[StatGroup.CITY_PASS_ADULT], [StatGroup.CITY_PASS_CHILD]],
  [ROUTES.CHALETS]: [[StatGroup.CHALETS]],
  [ROUTES.RINK_VIP]: [[StatGroup.RINK_VIP]],
  [StatGroup.TREE_PHOTO]: [[StatGroup.TREE_PHOTO]],
  [StatGroup.SANTA_PHOTO]: [[StatGroup.SANTA_PHOTO]],
  [ROUTES.THE_BEAM_PASS]: [[StatGroup.THE_BEAM_PASS]],
  [ROUTES.ALL_IN_PASS]: [
    [StatGroup.ALLINPSKYA],
    [StatGroup.ALLINPSKYS],
    [StatGroup.ALLINPSKYC],
  ],
  [ROUTES.ON_THE_ROCKS]: [[StatGroup.ON_THE_ROCKS]],
} as StatGroupByEventType;

export const DiscountedStatGroupByFlow = {
  [Flow.ALL_IN_PASS]: [
    [StatGroup.AIPPPEAKC],
    [StatGroup.AIPPPEAKS],
    [StatGroup.AIPPPEAKA],
  ],
  [Flow.ON_THE_ROCKS]: [[StatGroup.ON_THE_ROCKS]],
};

export const StatGroupByFlow = {
  [Flow.ALL_IN_PASS]: [
    [StatGroup.ALLINPSKYA],
    [StatGroup.ALLINPSKYS],
    [StatGroup.ALLINPSKYC],
  ],
  [Flow.ON_THE_ROCKS]: [[StatGroup.ON_THE_ROCKS]],
  [Flow.TOR_GA]: [
    [StatGroup.TOR],
    [StatGroup.GA],
    [StatGroup.GA_ADULT],
    [StatGroup.GA_CHILD],
    [StatGroup.GA_SENIOR],
  ],
  [Flow.TOR_EXPRESS]: [[StatGroup.TOR], [StatGroup.TOR_XPASS]],
  [Flow.TOR_VIP]: [[StatGroup.TOR], [StatGroup.TOR_VIP]],
  [Flow.RC_TOUR]: [[StatGroup.RINK], [StatGroup.GA], [StatGroup.RC_TOUR]],
  [Flow.RC_ROCK_PASS]: [[StatGroup.RC_ROCK_PASS]],
  [Flow.TOR_VIP_ROCKSTAR]: [[StatGroup.TOR], [StatGroup.TOR_VIP_ROCKSTAR]],
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: [
    [StatGroup.TOR],
    [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY],
  ],
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: [[StatGroup.RC_TOUR_KIDS_AND_FAMILY]],
  [Flow.RINK]: [
    [StatGroup.RINK],
    [StatGroup.RINK_GA],
    [StatGroup.RINK_ADULT],
    [StatGroup.RINK_CHILD],
    [StatGroup.RINK_TODDLER],
    [StatGroup.RINK_CHAPERONE],
  ],

  [Flow.RINK_MEMBERSHIP]: [
    [StatGroup.RINK_MEMBERSHIP],
    [StatGroup.RINK_MEM_WITH_SKATES],
  ],
  [Flow.CHALETS]: [[StatGroup.CHALETS]],
  [Flow.RINK_VIP]: [[StatGroup.RINK_VIP]],

  [Flow.CITY_PASS]: [[StatGroup.CITY_PASS_ADULT], [StatGroup.CITY_PASS_CHILD]],
  [Flow.CITY_PASS_REDEMPTION]: [
    [StatGroup.CITY_PASS_ADULT],
    [StatGroup.CITY_PASS_CHILD],
  ],
  [Flow.TREE_PHOTO]: [[StatGroup.TREE_PHOTO]],
  [Flow.SANTA_PHOTO]: [[StatGroup.SANTA_PHOTO]],
  [Flow.THE_BEAM_PASS]: [[StatGroup.THE_BEAM_PASS]],
} as StatGroupByFlowType;

export enum Attraction {
  RockefellerCenterTour = 2,
  TopOfTheRockObservationDeck = 1,
  Rink = 3,
}

export const FlowEvent = {
  [Flow.C3]: 'top-of-the-rock',
  [Flow.CITY_PASS]: 'top-of-the-rock',
  [Flow.CITY_PASS_UPGRADE]: 'top-of-the-rock',
  [Flow.DECK]: 'top-of-the-rock',
  [Flow.REDEMPTION]: 'top-of-the-rock',
  [Flow.REFUND]: 'top-of-the-rock',
  [Flow.RC_ROCK_PASS]: 'rock-pass',
  [Flow.RC_TOUR]: 'rock-center-tour',
  [Flow.TREE_PHOTO]: ROUTES.TREEPHOTO,
  [Flow.SANTA_PHOTO]: ROUTES.SANTA_PHOTO,
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: ROUTES.RC_TOUR_KIDS_AND_FAMILY,
  [Flow.TOR_GA]: 'top-of-the-rock',
  [Flow.TOR_EXPRESS]: 'express-pass',
  [Flow.TOR_VIP]: 'vip',
  [Flow.TOR_VIP_ROCKSTAR]: ROUTES.VIP_ROCKSTAR,
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: ROUTES.RC_ROCK_PASS_VIP_HOLIDAY,
  [Flow.RINK]: 'the-rink',
  [Flow.RINK_MEMBERSHIP]: 'the-rink-membership',
  [Flow.CHALETS]: ROUTES.CHALETS,
  [Flow.RINK_VIP]: 'the-rink-vip',
  [Flow.THE_BEAM_PASS]: ROUTES.THE_BEAM_PASS,
  [Flow.ALL_IN_PASS]: ROUTES.ALL_IN_PASS,
  [Flow.ON_THE_ROCKS]: ROUTES.ON_THE_ROCKS,
} as FlowAsKeyType;

export type T_TIME_INTERVALS_BY_TICKET_TYPE = {
  [key in Flow]: number;
};

export const TIME_INTERVALS_BY_TICKET_TYPE = {
  [Flow.TOR_VIP]: 60,
  [Flow.TOR_VIP_ROCKSTAR]: 60,
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 60,
  [Flow.RC_TOUR]: 30,
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 30,
  [Flow.RINK]: 10,
  [Flow.CHALETS]: 10,
  [Flow.RINK_VIP]: 10,
  [Flow.TREE_PHOTO]: 10,
  [Flow.SANTA_PHOTO]: 10,
  [Flow.SKYLIFT]: 30,
} as T_TIME_INTERVALS_BY_TICKET_TYPE;

export const DEFAULT_TIME_INTERVALS_FOR_TICKETS = 5;

export const BosEvent = {
  FIVE_ACRES: 'TOR.EVN33',
  ALL_IN_PASS: 'TOR.EVN3',
  BEANIE: 'TOR.EVN18',
  CHALETS: 'TOR.EVN12',
  CHAMPAGNE_TOAST: 'TOR.EVN16',
  CITY_PASS: 'TOR.EVN3', // CityPASS sells TOR_GA
  PHOTO_PACKAGE: 'TOR.EVN17',
  RC_ROCK_KIDS_AND_FAMILY: 'TOR.EVN13',
  RC_ROCK_PASS: 'combo-rock-pass', // Rock Pass is a combo of TOR_GA and TOR_TOUR
  RC_TOUR: 'TOR.EVN4',
  RINK: 'TOR.EVN2',
  RINK_MEMBERSHIP: 'TOR.EVN7778', // We don't know the updated AK yet
  RINK_VIP: 'RRC.EVN1',
  SKATE_RENTAL: 'TOR.EVN19',
  THE_BEAM: 'TOR.EVN14',
  TOR_EXPRESS: 'TOR.EVN9',
  TOR_GA: 'TOR.EVN3',
  TOR_VIP: 'TOR.EVN8',
  TOR_VIP_ROCKSTAR: 'TOR.EVN15',
  RC_ROCK_PASS_VIP_HOLIDAY: 'TOR.EVN26',
  TREE_PHOTO: 'RRC.EVN2',
  SANTA_PHOTO: 'TOR.EVN21',
  BUNNY_PHOTO: 'TOR.EVN32',
  RESTAURANT_JUPITER: 'TOR.EVN25',
  THE_BEAM_PASS: 'TOR.EVN3',
  SKYLIFT: 'TOR.EVN27',
  ORNAMENT: 'TOR.EVN29',
  ON_THE_ROCKS: ENV_ON_THE_ROCKS_EVENT_AK,
} as const;

// Used to query Sanity for Date Overrides
export const SanityTicketTypeFromBosEvent = {
  [BosEvent.BEANIE]: 'BEANIE',
  [BosEvent.CHAMPAGNE_TOAST]: 'CHAMPAGNE_TOAST',
  [BosEvent.PHOTO_PACKAGE]: 'PHOTO_PACKAGE',
  [BosEvent.RC_ROCK_KIDS_AND_FAMILY]: 'RC_ROCK_KIDS_AND_FAMILY',
  [BosEvent.RC_ROCK_PASS]: 'RC_ROCK_PASS',
  [BosEvent.RC_TOUR]: 'RC_TOUR',
  [BosEvent.RINK]: 'RINK',
  [BosEvent.RINK_MEMBERSHIP]: 'RINK_MEMBERSHIP',
  [BosEvent.RINK_VIP]: 'RINK_VIP',
  [BosEvent.SKATE_RENTAL]: 'SKATE_RENTAL',
  [BosEvent.THE_BEAM]: 'THE_BEAM',
  [BosEvent.TOR_EXPRESS]: 'TOR_EXPRESS',
  [BosEvent.TOR_GA]: 'TOR_GA',
  [BosEvent.TOR_VIP]: 'TOR_VIP',
  [BosEvent.TOR_VIP_ROCKSTAR]: 'TOR_VIP_ROCKSTAR',
  [BosEvent.RC_ROCK_PASS_VIP_HOLIDAY]: 'RC_ROCK_PASS_VIP_HOLIDAY',
  [BosEvent.TREE_PHOTO]: 'TREE_PHOTO',
  [BosEvent.SANTA_PHOTO]: 'SANTA_PHOTO',
  [BosEvent.BUNNY_PHOTO]: 'BUNNY_PHOTO',
  [BosEvent.RESTAURANT_JUPITER]: 'RESTAURANT_JUPITER',
  [BosEvent.SKYLIFT]: 'SKYLIFT',
  [BosEvent.ORNAMENT]: 'ORNAMENT',
};

// Used to query Sanity for Date Overrides
// This is used for performance lookups that do not use a BosEvent
export const SanityTicketTypeFromFlow = {
  [Flow.ALL_IN_PASS]: 'ALL_IN_PASS',
  [Flow.ON_THE_ROCKS]: 'ON_THE_ROCKS',
};

export type BosEvent = typeof BosEvent[keyof typeof BosEvent];

export function getBosEventKey(value: string): string | undefined {
  return Object.keys(BosEvent).find(
    (key) => BosEvent[key as keyof typeof BosEvent] === value,
  );
}

export const SlugToBosEventMap = {
  'the-rink': BosEvent.RINK,
  'the-rink-membership': BosEvent.RINK_MEMBERSHIP,
  'top-of-the-rock': BosEvent.TOR_GA,
  'express-pass': BosEvent.TOR_EXPRESS,
  'vip': BosEvent.TOR_VIP,
  [ROUTES.VIP_ROCKSTAR]: BosEvent.TOR_VIP_ROCKSTAR,
  [ROUTES.RC_ROCK_PASS_VIP_HOLIDAY]: BosEvent.RC_ROCK_PASS_VIP_HOLIDAY,
  'rock-center-tour': BosEvent.RC_TOUR, //  Do we need to add for tree photo?
  [ROUTES.RC_TOUR_KIDS_AND_FAMILY]: BosEvent.RC_ROCK_KIDS_AND_FAMILY,
  'rock-pass': BosEvent.RC_ROCK_PASS,
  'citypass': BosEvent.TOR_GA,
  'citypass-redemption': BosEvent.TOR_GA,
  [ROUTES.CHALETS]: BosEvent.CHALETS,
  [ROUTES.RINK_VIP]: BosEvent.RINK_VIP,
  [StatGroup.THE_BEAM]: BosEvent.THE_BEAM,
  [StatGroup.TREE_PHOTO]: BosEvent.TREE_PHOTO,
  [StatGroup.SANTA_PHOTO]: BosEvent.SANTA_PHOTO,
  [StatGroup.THE_BEAM_PASS]: BosEvent.THE_BEAM_PASS,
  [ROUTES.ALL_IN_PASS]: BosEvent.ALL_IN_PASS,
  [ROUTES.ON_THE_ROCKS]: BosEvent.ON_THE_ROCKS,
};

export const FlowToSanityTicketSlug = {
  [Flow.ALL_IN_PASS]: 'all-in-pass',
  [Flow.ON_THE_ROCKS]: 'on-the-rocks',
  [Flow.TREE_PHOTO]: 'tree-photo',
  [Flow.CHALETS]: 'the-rink-chalets',
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 'vip-rock-pass-holiday-edition',
  [Flow.TOR_VIP]: 'vip-tour',
  [Flow.TOR_VIP_ROCKSTAR]: 'vip-rock-pass',
  [Flow.RINK]: 'rink-general-admission',
  [Flow.RINK_VIP]: 'vip-skate',
  [Flow.RINK_MEMBERSHIP]: 'rink-membership',
  [Flow.RC_TOUR]: 'rock-center-tour',
  [Flow.RC_ROCK_PASS]: 'rock-pass',
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 'kids-tour',
  [Flow.TOR_GA]: 'general-admission',
  [Flow.TOR_EXPRESS]: 'express-pass',
  [Flow.CITY_PASS]: 'citypass-ga',
} as FlowAsKeyType;

export const FlowToBosEventMap = {
  [Flow.RINK]: BosEvent.RINK,
  [Flow.RINK_MEMBERSHIP]: BosEvent.RINK_MEMBERSHIP,
  [Flow.TOR_GA]: BosEvent.TOR_GA,
  [Flow.TOR_EXPRESS]: BosEvent.TOR_EXPRESS,
  [Flow.TOR_VIP]: BosEvent.TOR_VIP,
  [Flow.TOR_VIP_ROCKSTAR]: BosEvent.TOR_VIP_ROCKSTAR,
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: BosEvent.RC_ROCK_PASS_VIP_HOLIDAY,
  [Flow.RC_TOUR]: BosEvent.RC_TOUR,
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: BosEvent.RC_ROCK_KIDS_AND_FAMILY,
  [Flow.RC_ROCK_PASS]: BosEvent.RC_ROCK_PASS,
  [Flow.CITY_PASS]: BosEvent.TOR_GA,
  [Flow.CITY_PASS_REDEMPTION]: BosEvent.TOR_GA,
  [Flow.CHALETS]: BosEvent.CHALETS,
  [Flow.RINK_VIP]: BosEvent.RINK_VIP,
  [Flow.TREE_PHOTO]: BosEvent.TREE_PHOTO,
  [Flow.SANTA_PHOTO]: BosEvent.SANTA_PHOTO,
  [Flow.THE_BEAM_PASS]: BosEvent.THE_BEAM_PASS,
  [Flow.ALL_IN_PASS]: BosEvent.ALL_IN_PASS,
  [Flow.ON_THE_ROCKS]: BosEvent.ON_THE_ROCKS,
};

export const AddOnStatGroupToBosEventMap: { [key in StatGroup]?: BosEvent } = {
  [StatGroup.THE_BEAM]: BosEvent.THE_BEAM,
  [StatGroup.THE_BEAM_COMP]: BosEvent.THE_BEAM,
  [StatGroup.CHAMPAGNE_TOAST]: BosEvent.CHAMPAGNE_TOAST,
  [StatGroup.CHAMPAGNE_TOAST_COMP]: BosEvent.CHAMPAGNE_TOAST,
  [StatGroup.SANTA_PHOTO]: BosEvent.SANTA_PHOTO,
  [StatGroup.BUNNY_PHOTO]: BosEvent.BUNNY_PHOTO,

  [StatGroup.PHOTO_PACKAGE]: BosEvent.PHOTO_PACKAGE,
  [StatGroup.PHOTO_PACKAGE_COMP]: BosEvent.PHOTO_PACKAGE,

  [StatGroup.SKATE_RENTAL]: BosEvent.SKATE_RENTAL,
  [StatGroup.BEANIE]: BosEvent.BEANIE,
  [StatGroup.ORNAMENT]: BosEvent.ORNAMENT,
  [StatGroup.RESTAURANT_JUPITER]: BosEvent.RESTAURANT_JUPITER,
  [StatGroup.FIVE_ACRES]: BosEvent.FIVE_ACRES,
};

export const BosEventCombos: { [key in Flow]?: BosEvent[] } = {
  [Flow.RC_ROCK_PASS]: [BosEvent.RC_TOUR, BosEvent.TOR_GA],
};

type RequiredTicketLimitFlows = Exclude<
  Flow,
  | Flow.C3
  | Flow.CART
  | Flow.CHECKOUT
  | Flow.CITY_PASS_REDEMPTION
  | Flow.CITY_PASS_UPGRADE
  | Flow.DECK
  | Flow.NONE
  | Flow.REDEMPTION
  | Flow.REFUND
  | Flow.RINK_MEM_WITH_SKATES
  | Flow.SANTA_PHOTO
>;

type TicketLimitType = {
  [key in RequiredTicketLimitFlows]: number;
};

export const TicketLimit: TicketLimitType = {
  [Flow.TOR_GA]: 21,
  [Flow.RC_TOUR]: 21,
  [Flow.RC_ROCK_PASS]: 21,
  [Flow.TOR_EXPRESS]: 21,
  [Flow.RC_TOUR_KIDS_AND_FAMILY]: 21,
  [Flow.TOR_VIP]: 15,
  [Flow.TOR_VIP_ROCKSTAR]: 10,
  [Flow.RC_ROCK_PASS_VIP_HOLIDAY]: 10,
  [Flow.RINK]: 10,
  [Flow.RINK_MEMBERSHIP]: 10,
  [Flow.CITY_PASS]: 10,
  [Flow.CHALETS]: 1,
  [Flow.RINK_VIP]: 10,
  [Flow.TREE_PHOTO]: 1,
  [Flow.THE_BEAM_PASS]: 21,
  [Flow.ALL_IN_PASS]: 21,
  [Flow.ON_THE_ROCKS]: 10,
  [Flow.SKYLIFT]: 65,
};

export const AddonLimit: { [key in StatGroup]?: number } = {
  // The client elected to make this dynamic based on the total quantity of tickets purchased
  // Keeping this in case they change their mind or decide to put a hard limit on another AddOn
  [StatGroup.FIVE_ACRES]: 8,
  [StatGroup.SANTA_PHOTO]: 1,
  [StatGroup.BUNNY_PHOTO]: 1,
  [StatGroup.BEANIE]: 10,
  [StatGroup.ORNAMENT]: 10,
};

export const TimeConstraints: {
  [key in StatGroup]?: {
    startTime: { hours: number; minutes: string };
    endTime: { hours: number; minutes: string };
  };
} = {
  [StatGroup.RESTAURANT_JUPITER]: {
    startTime: { hours: 11, minutes: '00' },
    endTime: { hours: 21, minutes: '44' },
  },
  [StatGroup.FIVE_ACRES]: {
    startTime: { hours: 11, minutes: '45' },
    endTime: { hours: 20, minutes: '00' },
  },
  // [StatGroup.TOR_VIP]: {
  //   startTime: { hours: 11, minutes: '00' },
  //   endTime: { hours: 21, minutes: '44' },
  // },
  [StatGroup.TOR_VIP_ROCKSTAR]: {
    startTime: { hours: 10, minutes: '30' },
    endTime: { hours: 20, minutes: '44' },
  },
  [StatGroup.SANTA_PHOTO]: {
    startTime: { hours: 11, minutes: '30' }, // 11:30 AM
    endTime: { hours: 19, minutes: '00' }, // to 7 PM
  },
  [StatGroup.BUNNY_PHOTO]: {
    startTime: { hours: 10, minutes: '30' }, // 10:30 AM
    endTime: { hours: 18, minutes: '00' }, // to 6 PM
  },
  [StatGroup.RC_ROCK_PASS_VIP_HOLIDAY]: {
    startTime: { hours: 10, minutes: '30' },
    endTime: { hours: 20, minutes: '44' },
  },
  [StatGroup.CHAMPAGNE_TOAST]: {
    startTime: { hours: 10, minutes: '00' },
    endTime: { hours: 23, minutes: '45' },
  },
};

// When different dates have the same time constraints
export const DateConstraints: {
  [key in StatGroup]?: string[];
} = {
  [StatGroup.BUNNY_PHOTO]: ['4-12', '4-13', '4-18', '4-19', '4-20'],
};

export const ChampagneToastTimeConstraints = {
  weekday: {
    startTime: { hours: 8, minutes: '00' },
    endTime: { hours: 23, minutes: '00' },
  },
  sunday: {
    startTime: { hours: 10, minutes: '00' },
    endTime: { hours: 23, minutes: '00' },
  },
};

export const SantaTimeConstraints: {
  [key: string]: {
    startTime: { hours: number; minutes: string };
    endTime: { hours: number; minutes: string };
  };
} = {
  '7-22': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-23': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-24': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-25': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-26': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-27': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-28': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-29': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-30': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '7-31': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 15, minutes: '50' },
  },
  '12-06': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 19, minutes: '00' },
  },
  '12-13': {
    startTime: { hours: 11, minutes: '30' },
    endTime: { hours: 19, minutes: '00' },
  },
  '12-07': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-08': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-14': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-15': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-20': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-21': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-22': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-23': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-24': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
  '12-25': {
    startTime: { hours: 9, minutes: '30' },
    endTime: { hours: 17, minutes: '00' },
  },
};

export const SantaDays = [
  '7-22',
  '7-23',
  '7-24',
  '7-25',
  '7-26',
  '7-27',
  '7-28',
  '7-29',
  '7-30',
  '7-31',
  '12-06',
  '12-13',
  '12-07',
  '12-08',
  '12-14',
  '12-15',
  '12-20',
  '12-21',
  '12-22',
  '12-23',
  '12-24',
  '12-25',
];

export const CreditCardDisplayNames = {
  [CardType.AX]: 'American Express',
  [CardType.AP]: 'Alipay',
  [CardType.BC]: 'Backed Card',
  [CardType.CI]: 'Citgo',
  [CardType.DB]: 'Debit card',
  [CardType.GC]: 'Gift Card',
  [CardType.JC]: 'JCB',
  [CardType.MC]: 'Mastercard',
  [CardType.NS]: 'Discover/JCB/Novus',
  [CardType.PL]: 'Private Label',
  [CardType.SC]: 'Sears Canada',
  [CardType.VS]: 'Visa',
  [CardType.WP]: 'WeChat Pay',
  [CardType.YC]: "IT'S YOUR CARD",
};

export const ProductToPackageCode: Record<string, string> = {
  CPCHDTORWEB: 'CPCDWEB',
  CPADLTORWEB: 'CPADWEB',
};

export const packageCodeToStatGroup: Record<string, StatGroup> = {
  CPCDWEB: StatGroup.CITY_PASS_CHILD,
  CPADWEB: StatGroup.CITY_PASS_ADULT,
};

export enum Shift4Header {
  COMPANY_NAME = 'RCPI Landmark',
  INTERFACE_NAME = 'Rockefeller Center B2C',
}

export const RINK_RELATED = [
  Flow.RINK,
  Flow.CHALETS,
  Flow.RINK_VIP,
  Flow.TREE_PHOTO,
  Flow.SANTA_PHOTO,
];

export const RINK_OR_RINK_MEMBERSHIP_RELATED = [
  ...RINK_RELATED,
  Flow.RINK_MEMBERSHIP,
  Flow.RINK_MEM_WITH_SKATES,
];

export const ALL_IN_PASS_ADULT_AK = 'TOR.EVN1.MCC5971';
export const ALL_IN_PASS_SENIOR_AK = 'TOR.EVN1.MCC5970';
export const ALL_IN_PASS_CHILD_AK = 'TOR.EVN1.MCC5969';
export const ALL_IN_PASS_ADULT_PEAK_AK = ENV_ALL_IN_PASS_ADULT_PEAK_AK;
export const ALL_IN_PASS_CHILD_PEAK_AK = ENV_ALL_IN_PASS_CHILD_PEAK_AK;
export const ALL_IN_PASS_SENIOR_PEAK_AK = ENV_ALL_IN_PASS_SENIOR_PEAK_AK;

export const BEAM_AK = 'TOR.EVN1.MCC1622';
export const BEAM_CMP_AK = 'TOR.EVN1.MCC2147';
export const BEANIE_PRODUCT_AK = 'TOR.EVN1.MCC1728';
export const CHALET_AK = 'TOR.EVN1.MCC5640';
export const CHAMPAGNE_TOAST_AK = 'TOR.EVN1.MCC1606';
export const CHAMPAGNE_TOAST_COMP_AK = 'TOR.EVN1.MCC2000';
export const HOT_COCOA_AK = 'TOR.EVN1.MCC1750';
export const ORNAMENT_AK = 'TOR.EVN1.MCC5655';
export const PHOTO_PKG_AK = 'TOR.EVN1.MCC1720';
export const PHOTO_PKG_CMP_AK = 'TOR.EVN1.MCC1997';
export const RESTAURANT_JUPITER_AK = 'TOR.EVN1.MCC5131';
export const FIVE_ACRES_AK = ENV_FIVE_ACRES_PRODUCT_AK;
export const SANTA_PHOTO_PRODUCT_AK = 'TOR.EVN1.MCC5631';
export const SKATE_RENTAL_AK = 'TOR.EVN1.MCC5604';
export const SKYLIFT_AK = 'TOR.EVN1.MCC5645';
export const SKYLIFT_COMP_AK = 'TOR.EVN1.MCC5646';
export const TOR_EXPRESS_PRODUCT_AK = 'TOR.EVN1.MCC757';
export const TREE_PHOTO_PRODUCT_AK = 'TOR.EVN1.MCC1732';
export const ON_THE_ROCKS_PRODUCT_AK = 'TOR.EVN1.MCC5998';
export const BUNNY_PHOTO_PRODUCT_AK = ENV_BUNNY_PHOTO_PRODUCT_AK;

// Define a type for the discounted product mapping
export type DiscountedProductMapping = {
  [flow in Flow]?: {
    [productAK: string]: string;
  };
};

export const DISCOUNTED_PRODUCT_AK_TO_PRODUCT_AK: DiscountedProductMapping = {
  [Flow.ALL_IN_PASS]: {
    [ALL_IN_PASS_CHILD_PEAK_AK]: 'TOR.EVN1.MCC5969', // Child
    [ALL_IN_PASS_SENIOR_PEAK_AK]: 'TOR.EVN1.MCC5970', // Senior
    [ALL_IN_PASS_ADULT_PEAK_AK]: 'TOR.EVN1.MCC5971', // Adult
  },
};

export const AddOnAKForStatGroup: { [key in StatGroup]?: string } = {
  [StatGroup.BEANIE]: BEANIE_PRODUCT_AK,
  [StatGroup.THE_BEAM]: BEAM_AK,
  [StatGroup.THE_BEAM_COMP]: BEAM_CMP_AK,
  [StatGroup.CHALETS]: CHALET_AK,

  [StatGroup.PHOTO_PACKAGE]: PHOTO_PKG_AK,
  [StatGroup.PHOTO_PACKAGE_COMP]: PHOTO_PKG_CMP_AK,
  [StatGroup.CHAMPAGNE_TOAST]: CHAMPAGNE_TOAST_AK,
  [StatGroup.CHAMPAGNE_TOAST_COMP]: CHAMPAGNE_TOAST_COMP_AK,

  [StatGroup.SKATE_RENTAL]: SKATE_RENTAL_AK,
  [StatGroup.ORNAMENT]: ORNAMENT_AK,

  [StatGroup.TREE_PHOTO]: TREE_PHOTO_PRODUCT_AK,
  [StatGroup.SANTA_PHOTO]: SANTA_PHOTO_PRODUCT_AK,

  [StatGroup.BUNNY_PHOTO]: BUNNY_PHOTO_PRODUCT_AK,

  [StatGroup.HOT_COCOA]: HOT_COCOA_AK,

  [StatGroup.RESTAURANT_JUPITER]: RESTAURANT_JUPITER_AK,
  [StatGroup.FIVE_ACRES]: FIVE_ACRES_AK,
  [StatGroup.SKYLIFT]: SKYLIFT_AK,
  [StatGroup.SKYLIFT_COMP]: SKYLIFT_COMP_AK,
};

export const COMBO_FLOWS = [Flow.RC_ROCK_PASS];

// Represents addons that are time-sensitive
// meaning: They need a specific time slot to be chosen, usually 90min after
// the ticket time slot
export const ADDON_EVENTS_WITH_TIME_CAPACITY: (BosEvent | undefined)[] = [
  BosEvent.THE_BEAM,
  BosEvent.TREE_PHOTO,
  BosEvent.SANTA_PHOTO,
  BosEvent.BUNNY_PHOTO,
  BosEvent.SKYLIFT,
];

export const AddOnAKFromBosEvent: { [key in BosEvent]?: string } = {
  [BosEvent.THE_BEAM]: BEAM_AK,
  [BosEvent.TREE_PHOTO]: TREE_PHOTO_PRODUCT_AK,
  [BosEvent.SANTA_PHOTO]: SANTA_PHOTO_PRODUCT_AK,
  [BosEvent.BUNNY_PHOTO]: BUNNY_PHOTO_PRODUCT_AK,
};

export const ADDON_EVENTS_WITH_CAPACITY: (BosEvent | undefined)[] = [
  BosEvent.RESTAURANT_JUPITER,
  BosEvent.FIVE_ACRES,
  BosEvent.ORNAMENT,
];

export const BosEventFromAddOnAK = {
  [BEAM_AK]: BosEvent.THE_BEAM,
  [TREE_PHOTO_PRODUCT_AK]: BosEvent.TREE_PHOTO,
  [SANTA_PHOTO_PRODUCT_AK]: BosEvent.SANTA_PHOTO,
  [BUNNY_PHOTO_PRODUCT_AK]: BosEvent.BUNNY_PHOTO,
};

export const StatGroupFromAddOnAK: Record<string, StatGroup> = {
  [BEAM_AK]: StatGroup.THE_BEAM,
  [TREE_PHOTO_PRODUCT_AK]: StatGroup.TREE_PHOTO,
  [SANTA_PHOTO_PRODUCT_AK]: StatGroup.SANTA_PHOTO,
  [SKATE_RENTAL_AK]: StatGroup.SKATE_RENTAL,
  [BEANIE_PRODUCT_AK]: StatGroup.BEANIE,
  [ORNAMENT_AK]: StatGroup.ORNAMENT,
  [PHOTO_PKG_AK]: StatGroup.PHOTO_PACKAGE,
  [CHAMPAGNE_TOAST_AK]: StatGroup.CHAMPAGNE_TOAST,
  [RESTAURANT_JUPITER_AK]: StatGroup.RESTAURANT_JUPITER,
  [FIVE_ACRES_AK]: StatGroup.FIVE_ACRES,
  'TOR.EVN1.MCC1750': StatGroup.HOT_COCOA,
  [SKYLIFT_AK]: StatGroup.SKYLIFT,
  [BUNNY_PHOTO_PRODUCT_AK]: StatGroup.BUNNY_PHOTO,
};

export const BosEventForAddOnStatGroup: { [key in StatGroup]?: BosEvent } = {
  [StatGroup.BEANIE]: BosEvent.BEANIE,
  [StatGroup.CHAMPAGNE_TOAST]: BosEvent.CHAMPAGNE_TOAST,
  // [StatGroup.HOT_COCOA]: BosEvent.HOT_COCOA,
  [StatGroup.PHOTO_PACKAGE]: BosEvent.PHOTO_PACKAGE,
  [StatGroup.SKATE_RENTAL]: BosEvent.SKATE_RENTAL,
  [StatGroup.ORNAMENT]: BosEvent.ORNAMENT,
  [StatGroup.THE_BEAM]: BosEvent.THE_BEAM,
  [StatGroup.TREE_PHOTO]: BosEvent.TREE_PHOTO,
  [StatGroup.SANTA_PHOTO]: BosEvent.SANTA_PHOTO,
  [StatGroup.BUNNY_PHOTO]: BosEvent.BUNNY_PHOTO,
  [StatGroup.THE_BEAM_COMP]: BosEvent.THE_BEAM,
  [StatGroup.RESTAURANT_JUPITER]: BosEvent.RESTAURANT_JUPITER,
  [StatGroup.FIVE_ACRES]: BosEvent.FIVE_ACRES,
  [StatGroup.SKYLIFT]: BosEvent.SKYLIFT,
};

export const SINGLE_QTY_ADDONS = [StatGroup.TREE_PHOTO];

export const AVS_COUNTRIES = ['USA', 'Canada', 'Australia', 'New Zealand'];

export const TICKETS_THAT_DONT_SHOW_TIME = [TOR_EXPRESS_PRODUCT_AK];

export const ADDONS_THAT_DONT_SHOW_DATE = [ORNAMENT_AK];

export const ADDONS_THAT_DONT_SHOW_TIME = [
  SKATE_RENTAL_AK,
  HOT_COCOA_AK,
  BEANIE_PRODUCT_AK,
  BUNNY_PHOTO_PRODUCT_AK,
  PHOTO_PKG_AK,
  PHOTO_PKG_CMP_AK,
  SKYLIFT_COMP_AK,
  TOR_EXPRESS_PRODUCT_AK,
  CHAMPAGNE_TOAST_AK,
  CHAMPAGNE_TOAST_COMP_AK,
  ORNAMENT_AK,
  SKYLIFT_AK,
  BEAM_AK,
  FIVE_ACRES_AK,
  RESTAURANT_JUPITER_AK,
];

export const ADDITIONAL_EXPRESS_PASS_ADDONS_THAT_DONT_SHOW_TIME = [
  SKYLIFT_AK,
  BEAM_AK,
];

export const TICKETS_AND_ADDONS_THAT_DONT_SHOW_TIME = [
  ...TICKETS_THAT_DONT_SHOW_TIME,
  ...ADDONS_THAT_DONT_SHOW_TIME,
];

export const BEAM_AKS = [BEAM_AK, BEAM_CMP_AK];
export const SKYLIFT_AKS = [SKYLIFT_AK, SKYLIFT_COMP_AK];

export const BEAM_HIDDEN_EVENTS = [
  BosEvent.TOR_VIP_ROCKSTAR,
  BosEvent.RC_ROCK_PASS_VIP_HOLIDAY,
  BosEvent.TOR_VIP,
  BosEvent.TOR_EXPRESS,
];

export const SKYLIFT_HIDDEN_EVENTS = [
  BosEvent.TOR_VIP_ROCKSTAR,
  BosEvent.RC_ROCK_PASS_VIP_HOLIDAY,
  BosEvent.TOR_VIP,
  BosEvent.TOR_EXPRESS,
];

export const TIERED_FLOWS = [Flow.RINK, Flow.TOR_GA];

// Dynamic Pricing - When the same product has it's price affected due to date, time, and % remaining availability
// WIP: Updating this to match the terminology, atm the Rink is the only truly dynamic flow
export const DYNAMIC_PRICING_FLOWS = [Flow.RINK, Flow.RINK_VIP];

export const DYNAMIC_ADDONS = [StatGroup.THE_BEAM, StatGroup.SANTA_PHOTO];

// Variable Pricing - When the same product can have a different price based on it's associated performance (What time it is)
// Used for showing prices inline with the performance timeslot in the TimePicker
export const VARIABLE_PRICING_FLOWS = [
  Flow.ALL_IN_PASS,
  Flow.TOR_GA,
  Flow.CHALETS,
  Flow.TOR_EXPRESS,
  'NONE',
];

export const TAX_INCLUSIVE_PRODUCTS = [
  StatGroup.RESTAURANT_JUPITER,
  StatGroup.FIVE_ACRES,
];

export const checkAdaptivePricing = (flow: Flow) =>
  VARIABLE_PRICING_FLOWS.includes(flow) || DYNAMIC_PRICING_FLOWS.includes(flow);

export const FLOW_PRODUCT_TITLES_BY_AK = {
  [Flow.ALL_IN_PASS]: {
    ['TOR.EVN1.MCC5971']: 'Adult',
    ['TOR.EVN1.MCC5970']: 'Senior',
    ['TOR.EVN1.MCC5969']: 'Child',
    ['TOR.EVN1.MCC5984']: 'Adult',
    ['TOR.EVN1.MCC5985']: 'Senior',
    ['TOR.EVN1.MCC5986']: 'Child',
    ['TOR.EVN1.MCC5989']: 'Adult',
    ['TOR.EVN1.MCC5990']: 'Senior',
    ['TOR.EVN1.MCC5991']: 'Child',
  },
  [Flow.ON_THE_ROCKS]: {
    ['TOR.EVN1.MCC5998']: 'On the Rocks',
  },
  [Flow.TOR_GA]: {
    ['TOR.EVN1.MCC410']: 'Adult',
    ['TOR.EVN1.MCC644']: 'Senior',
    ['TOR.EVN1.MCC647']: 'Child',
  },
  [Flow.RINK_VIP]: {
    // PROD
    ['TOR.EVN1.MCC5602']: 'each',
    // DEV
    ['TOR.EVN1.MCC5272']: 'each',
  },
  [Flow.RINK]: {
    ['TOR.EVN1.MCC93']: 'Chaperone',
    // DEV
    ['TOR.EVN1.MCC5240']: 'Adult',
    ['TOR.EVN1.MCC5241']: 'Adult',
    ['TOR.EVN1.MCC5242']: 'Adult',
    ['TOR.EVN1.MCC5243']: 'Adult',
    ['TOR.EVN1.MCC5244']: 'Child',
    ['TOR.EVN1.MCC5245']: 'Child',
    ['TOR.EVN1.MCC5246']: 'Child',
    ['TOR.EVN1.MCC5247']: 'Child',
    ['TOR.EVN1.MCC5249']: 'Adult',
    ['TOR.EVN1.MCC5251']: 'Child',
    ['TOR.EVN1.MCC5252']: 'Toddler',
    ['TOR.EVN1.MCC5253']: 'Toddler',
    ['TOR.EVN1.MCC5254']: 'Toddler',
    ['TOR.EVN1.MCC5255']: 'Toddler',
    ['TOR.EVN1.MCC5256']: 'Toddler',
    // PROD
    ['TOR.EVN1.MCC5504']: 'Adult',
    ['TOR.EVN1.MCC5505']: 'Adult',
    ['TOR.EVN1.MCC5506']: 'Adult',
    ['TOR.EVN1.MCC5507']: 'Adult',
    ['TOR.EVN1.MCC5508']: 'Adult',
    ['TOR.EVN1.MCC5509']: 'Child',
    ['TOR.EVN1.MCC5510']: 'Child',
    ['TOR.EVN1.MCC5511']: 'Child',
    ['TOR.EVN1.MCC5512']: 'Child',
    ['TOR.EVN1.MCC5513']: 'Child',
    ['TOR.EVN1.MCC5519']: 'Toddler',
    ['TOR.EVN1.MCC5520']: 'Toddler',
    ['TOR.EVN1.MCC5521']: 'Toddler',
    ['TOR.EVN1.MCC5522']: 'Toddler',
    ['TOR.EVN1.MCC5523']: 'Toddler',
  },
  [Flow.CHALETS]: {
    // DEV
    ['TOR.EVN1.MCC5640']: 'each',
    // PROD
    ['TOR.EVN1.MCC5284']: 'each',
  },
} as Partial<Record<Flow, Record<string, string>>>;

export const MOST_POPULAR_ADDONS = [StatGroup.THE_BEAM];
export const NEW_EXPERIENCE_ADDONS = [StatGroup.SKYLIFT];

export type TicketSpecial = {
  hoursActive: number[];
  onSpecial: boolean;
  originalPrice: string;
  tag: string;
};

type TicketSpecialsType = {
  [key in StatGroup]?: TicketSpecial;
};

export const TICKET_SPECIALS: TicketSpecialsType = {
  [StatGroup.SANTA_PHOTO]: {
    hoursActive: [], // Special runs all day
    onSpecial: true, // Currently enabled
    originalPrice: '$75', // serves as the price that will be striked out
    tag: 'Special Promotion!',
  },
  [StatGroup.THE_BEAM]: {
    hoursActive: [], //[21] Only shows this special after 9 pm
    onSpecial: true, // remains true so the tag will show, but no time rules or price updates are relevant atm
    originalPrice: '', // $25
    tag: 'Most Popular!',
  },
  [StatGroup.SKYLIFT]: {
    hoursActive: [],
    onSpecial: true,
    originalPrice: '',
    tag: 'New Experience!',
  },
  [StatGroup.PHOTO_PACKAGE]: {
    hoursActive: [],
    onSpecial: true,
    originalPrice: '$40',
    tag: '',
  },
  [StatGroup.BUNNY_PHOTO]: {
    hoursActive: [],
    onSpecial: true,
    originalPrice: '$55',
    tag: '',
  },
  [StatGroup.BEANIE]: {
    hoursActive: [],
    onSpecial: true,
    originalPrice: '$25',
    tag: '',
  },
};

export enum CheckoutErrors {
  AccountError = 'Account Error',
  PaymentError = 'Payment Error',
  CheckBasketError = 'CheckBasket Error',
  CheckoutError = 'Checkout Error',
  CloseOrderError = 'Close Order Error',
}

export const TorGaPriceRanges: Record<string, { high: number; low: number }> = {
  adult: { high: 6600, low: 4200 },
  child: { high: 6000, low: 3600 },
  senior: { high: 6400, low: 4000 },
};

export const AllInPassPriceRanges: Record<
  string,
  { high: number; low: number }
> = {
  adult: { high: 8700, low: 11100 },
  child: { high: 8100, low: 10500 },
  senior: { high: 8500, low: 10900 },
};

export const TheBeamPackagePriceRanges: { high: number; low: number } = {
  high: 8600,
  low: 6500,
};

export const RinkPriceRanges: Record<string, { high: number; low: number }> = {
  adult: { high: 11400, low: 2100 },
  child: { high: 11400, low: 2100 },
  toddler: { high: 6100, low: 1100 },
  chaperone: { high: 0, low: 0 },
};

export const RinkPremiumPriceRanges: { high: number; low: number } = {
  high: 15500,
  low: 4700,
};

export const RinkChaletPriceRanges: { high: number; low: number } = {
  high: 4500,
  low: 18000,
};

export const ExpressPriceRanges: { high: number; low: number } = {
  high: 11500,
  low: 11500,
};

export type TCompAddOn = {
  ak: string;
  description: string;
};

export const CompAddOns: Record<string, TCompAddOn> = {
  champagneToast: {
    ak: CHAMPAGNE_TOAST_COMP_AK,
    description: 'Champagne Toast',
  },
  photoPackage: {
    ak: PHOTO_PKG_CMP_AK,
    description: 'Photo Pass COMP',
  },
  skylift: {
    ak: SKYLIFT_COMP_AK,
    description: 'Skylift',
  },
};

export const CompChampagneToastDefaultFlows = [
  Flow.TOR_VIP_ROCKSTAR,
  Flow.RC_ROCK_PASS_VIP_HOLIDAY,
  Flow.TOR_VIP,
];

export const CompPhotoPackageDefaultFlows = [Flow.RC_ROCK_PASS_VIP_HOLIDAY];

export const CompSkyliftDefaultFlows = [
  Flow.TOR_VIP,
  Flow.TOR_VIP_ROCKSTAR,
  Flow.RC_ROCK_PASS_VIP_HOLIDAY,
];

export const CouponsForLocalZipCodes = ['RINK10'];
export const COUPON_FOR_OTR_ZIP_CODE_EXEMPTION = 'OTR-ZO';
