import axios from 'axios';
import { useMutation } from 'react-query';

const ax = axios.create({ baseURL: '/api' });

export const useMailchimpMutation = () =>
  useMutation(async (data: { email: string }) => {
    await ax.post('/mailchimp', data);
  });

export const useMailchimpEcommMutation = () =>
  useMutation(async (data: { email: string }) => {
    await ax.post('/mailchimp/ecomm', data);
  });
