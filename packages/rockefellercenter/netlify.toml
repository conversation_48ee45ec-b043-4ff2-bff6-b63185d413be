[build]
command = "NODE_ENV=production CI=false yarn run build"
ignore = "exit 1"
publish = "public"
functions = "functions"

[build.environment]
CURATOR_API_KEY="4b44d4e7-724e-4003-b7f7-5d8f332f0baf"
FILM_PHOTO_AUDIENCE_ID = "97807a1c11"
MAILCHIMP_SKYLIFT_AUDIENCE_ID = "36c11103fb"
GATSBY_CPU_COUNT = "6"
GITHUB_TOKEN = "****************************************"
GOOGLE_API_KEY = "AIzaSyCgr2-B3rFy9Mk4fNFVg0ezBolRf-ZE1jQ"
GOOGLE_APPLICATION_CREDENTIALS = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
GOOGLE_OPTIMIZE_RAINBOW_ROOM_EXPERIMENT_ID = "Uw2m-NNWTkOYrM_lQZMH_w"
GOOGLE_OPTIMIZE_VIP_EXPERIMENT_ID = "IhghK7a6Sb2cqyng56bdPw"
GOOGLE_SEARCH_KEY = "016857086406217865809:xteydcxhtxm"
GTM_ID = "GTM-NKNK8R3"
GTM_ENV = "development"
MAILCHIMP_API_KEY = "*************************************"
MAILCHIMP_AUDIENCE_ID = "f398f9780a"
MAILCHIMP_ECOMM_API_KEY = "*************************************"
MAILCHIMP_ECOMM_AUDIENCE_ID = "5a6a04f393"
MAILCHIMP_URL = "https://us18.api.mailchimp.com/3.0"
NETLIFY_USE_YARN = "true"
NPM_CONFIG_GITHUB_TOKEN = "****************************************"
SANITY_ASSET_TOKEN = "skfDuu1OpYeg2murGOcFxUnpuEUpUPEJB8TUwstj2N0NRfwiAHnnolkLUbfeoNLvswPed4iPjixhLQGt5BkkiQSkKOH8OOxJ9c0rNuWkQDwijv9b2iNoQFqJRlGHPer8eArTGlHAx2KUMLqUrTfEGN1Mv3Br6cQSToT101AoBFHpCstU7ubP"
SANITY_DATASET = "main"
SANITY_PROJECT_ID = "bs9rmafh"
# SANITY_TOKEN = "sk0NmwwC4NsbciVCvVL4JwVX50NANlTCbFCIZGGj3NOzQX48FsIiPC1IbeKiRaFkPOwJbXdlKooaqG2xvbbaXL9RZp5LTK5g0O0XpDmWjhhmQXpSzhWTuOeYPwo5i2jxI4kMZS5c6dFxtd6snjKuSnZtvCGCFDFnDetIXs4tvhStlqpLenvD"
SANITY_TOKEN = "skKX8GXuCUGRmqTrfhQXdfHtwGIBNLv7sf9puMjr0inVFqg8vIc9bvxO7IUyN7aOg902LuIisw7L3na10BzbFpqQM2DnqXa9ZfxaJBANfC1Yg4uKz4ec4JxRa8HQtTx68KIOjb6mx5PZuN0iv3SlKPM9DWf5e4FjgcGzHaM7GsS04qGQgeZB"
SENDGRID_API_KEY = "*********************************************************************"
TRIPLESEAT_PUBLIC_KEY = "fb17f1715ad42ba617463352a2ebb5174224b3ae"
TZ = "America/New_York"
YARN_FLAGS = "--immutable"
YARN_NPM_AUTH_TOKEN = "c5fbdbf0061328ee3660aedc2ce6dda158fe4722"
SENTRY_DSN = "https://<EMAIL>/5937505"
CF_TURNSTILE_SITEKEY = "0x4AAAAAAAQdW3qKzHmPVILa"
CF_TURNSTILE_SECRET = "0x4AAAAAAAQdW8yMYRHgEQZXUG_YJvM368I"
MONGODB_CONNECTION="mongodb+srv://RvrntQnYyEkM8WBnWfmbbFEwFQrydZfP:<EMAIL>/"
MONGODB_DB="test"
MONGODB_COLLECTION="GetDaysAvailability"
KOUNT_MID="102133"
KOUNT_KHASH_KEY="1b^jIFD)e1@<ZKuH”A+?Ea\\`p+ATAo6@:Wee+EM+(FD5Z2/N<"
KOUNT_ENV="TEST"
KOUNT_API_URL="https://api-sandbox.kount.com"
KOUNT_CLIENT_ID="102133"
KOUNT_API_KEY="MG9hMWtvNWltcDRIblgxWFAzNTg6azN0MTJ0dXMtZ29CeE5SQTljdTFKdkY2S0JZT2k3U1VaU0NtenVaeFphdEpVbXBscGRPaU1XVUVrZ2lWZktrNg=="
KOUNT_AUTH_URL="https://login.kount.com/oauth2/ausdppkujzCPQuIrY357/v1/token"
ENABLE_NEW_RELIC_LOGGING="false"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"
FIREBASE_API_KEY = "AIzaSyA9zpBj5XLoCFMGrosIb-spvRk96oG0EQs"
FIREBASE_AUTH_DOMAIN = "ab-testing-e0f27.firebaseapp.com"
FIREBASE_PROJECT_ID = "ab-testing-e0f27"
FIREBASE_STORAGE_BUCKET = "ab-testing-e0f27.firebasestorage.app"
FIREBASE_MESSAGING_SENDER_ID = "746796851952"
FIREBASE_APP_ID = "1:746796851952:web:a05512f5c005427bd41835"
FIREBASE_MEASUREMENT_ID = "G-NMJ2KTC6HY"
ENV_ALL_IN_PASS_CHILD_PEAK_AK="TOR.EVN1.MCC5986"
ENV_ALL_IN_PASS_SENIOR_PEAK_AK="TOR.EVN1.MCC5985"
ENV_ALL_IN_PASS_ADULT_PEAK_AK="TOR.EVN1.MCC5984"
ENV_BUNNY_PHOTO_PRODUCT_AK="TOR.EVN1.MCC5990"
ENV_FIVE_ACRES_PRODUCT_AK="TOR.EVN1.MCC5993"
ENV_ON_THE_ROCKS_EVENT_AK="TOR.EVN35"

[build.processing]
skip_processing = true

[context.branch-deploy.environment]
SHIFT4_I4GO_URL="https://access.shift4test.com"
SHIFT4_API_URL = "https://utgapi.shift4test.com/api/rest/v1"
SHIFT4_AUTH_TOKEN = "7DCA79F2-A29D-E2D9-D2157473A54221EE"
SHIFT4_CLIENT_GUID = "3C9A27F8-F498-A467-FBBBFCB933074E75"
SHIFT4_TOR_ACCESS_TOKEN = "AA58BCED-7D94-4B60-BD84-AA33D1CE8F62"
SHIFT4_RINK_ACCESS_TOKEN = "7171753A-867C-4E1F-BBFA-17EDE10EA294"
BOS_TOR_PASSWORD = 'eCw6e2VVoFN32URJzKjx'
# TODO: Remove before merge to main
SANITY_DATASET = "main"
BOS_TOR_SESSION_ID = "9BCC5726DF8D4C0A958F2E410"
BOS_API_URL = "https://preprod-tor-isapi-web.vivaticket.com/bosservices.dll"
BOS_TOR_USERNAME = "WEBB2C-TOR"
BOS_TOR_WORKSTATION_AK = "TOR.WKS98"
BOS_RINK_SESSION_ID="02D5FE4D5E234FA4A1A0D4B0D"
BOS_RINK_WORKSTATION_AK="RRC.WKS98"
BOS_RINK_USERNAME="WEBB2C-RRC"
BOS_RINK_PASSWORD="eCw6e2VVoFN32URJzKjx"
CF_TURNSTILE_SITEKEY = "1x00000000000000000000AA"
CF_TURNSTILE_SECRET = "1x0000000000000000000000000000000AA"
ENABLE_NEW_RELIC_LOGGING="false"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"

[context.deploy-preview.environment]
SANITY_DATASET = "${{ env.GITHUB_ACTOR_SLUG }}"
SHIFT4_I4GO_URL="https://access.shift4test.com"
SHIFT4_TOR_ACCESS_TOKEN = "AA58BCED-7D94-4B60-BD84-AA33D1CE8F62"
SHIFT4_RINK_ACCESS_TOKEN = "7171753A-867C-4E1F-BBFA-17EDE10EA294"
SHIFT4_API_URL = "https://utgapi.shift4test.com/api/rest/v1"
SHIFT4_AUTH_TOKEN = "7DCA79F2-A29D-E2D9-D2157473A54221EE"
SHIFT4_CLIENT_GUID = "3C9A27F8-F498-A467-FBBBFCB933074E75"
BOS_TOR_PASSWORD = 'eCw6e2VVoFN32URJzKjx'
BOS_TOR_SESSION_ID = "9BCC5726DF8D4C0A958F2E410"
BOS_API_URL = "https://preprod-tor-isapi-web.vivaticket.com/bosservices.dll"
BOS_TOR_USERNAME = "WEBB2C-TOR"
BOS_TOR_WORKSTATION_AK = "TOR.WKS98"
BOS_RINK_SESSION_ID="02D5FE4D5E234FA4A1A0D4B0D"
BOS_RINK_WORKSTATION_AK="RRC.WKS98"
BOS_RINK_USERNAME="WEBB2C-RRC"
BOS_RINK_PASSWORD="eCw6e2VVoFN32URJzKjx"
CF_TURNSTILE_SITEKEY = "1x00000000000000000000AA"
CF_TURNSTILE_SECRET = "1x0000000000000000000000000000000AA"
ENABLE_NEW_RELIC_LOGGING="false"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"


[context.viva-main-stable.environment]
SANITY_DATASET = "narcuri"
GTM_AUTH = "4GiqkFCWxrytH9vaQXuAWg"
GTM_PREVIEW = "env-1166"

[context.shift4.environment]
SANITY_DATASET = "staging"
SHIFT4_RINK_ACCESS_TOKEN = "04BD8A12-56AB-414D-ACA8-7B1D71931890"
SHIFT4_TOR_ACCESS_TOKEN = "E420DB85-F48F-477B-853C-9EB23D5258FD"
SHIFT4_API_URL = "https://api.shift4api.net/api/rest/v1"
SHIFT4_I4GO_URL = "https://access.i4go.com"

[context.production]
SHIFT4_RINK_ACCESS_TOKEN = "04BD8A12-56AB-414D-ACA8-7B1D71931890"
SHIFT4_TOR_ACCESS_TOKEN = "E420DB85-F48F-477B-853C-9EB23D5258FD"
SHIFT4_API_URL = "https://utg.shift4api.net/api/rest/v1"
SHIFT4_I4GO_URL = "https://access.i4go.com"
BOS_API_URL = "https://tor-isapi-b2c.azurewebsites.net/BosServices.dll"

BOS_TOR_WORKSTATION_AK = "TOR.WKS138"
BOS_TOR_SESSION_ID = "1B81F83F7A0342ABB548D6FD5"

BOS_TOR_USERNAME = "WEB2C1"
BOS_TOR_PASSWORD = "WbVtXQWEzw6DjBWDTQNY"

BOS_RINK_WORKSTATION_AK="RRC.WKS98"
BOS_RINK_SESSION_ID="EF63C62A7B8B4E2EB7A347696"

BOS_RINK_USERNAME="WEBB2C-RRC"
BOS_RINK_PASSWORD="Ks4vnB2MAdHHM4rRQi33"

MONGODB_DB="viva-cache"
GTM_ENV="production"
ENABLE_NEW_RELIC_LOGGING="true"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"
ENV_ALL_IN_PASS_ADULT_PEAK_AK="TOR.EVN1.MCC5989"
ENV_ALL_IN_PASS_CHILD_PEAK_AK="TOR.EVN1.MCC5991"
ENV_ALL_IN_PASS_SENIOR_PEAK_AK="TOR.EVN1.MCC5990"
ENV_BUNNY_PHOTO_PRODUCT_AK="TOR.EVN1.MCC5997"
ENV_FIVE_ACRES_PRODUCT_AK="TOR.EVN1.MCC6056"
ENV_ON_THE_ROCKS_EVENT_AK="TOR.EVN34"

[context.production.environment]
SHIFT4_RINK_ACCESS_TOKEN = "04BD8A12-56AB-414D-ACA8-7B1D71931890"
SHIFT4_TOR_ACCESS_TOKEN = "E420DB85-F48F-477B-853C-9EB23D5258FD"
SHIFT4_API_URL = "https://utg.shift4api.net/api/rest/v1"
SHIFT4_I4GO_URL = "https://access.i4go.com"
BOS_API_URL = "https://tor-isapi-b2c.azurewebsites.net/BosServices.dll"
BOS_TOR_SESSION_ID = "5405C0F979C84A7F96D9A75B4"
BOS_TOR_USERNAME = "WEBB2C-TOR"
BOS_TOR_PASSWORD = "WbVtXQWEzw6DjBWDTQNY"
BOS_TOR_WORKSTATION_AK = "TOR.WKS98"
BOS_RINK_SESSION_ID="EF63C62A7B8B4E2EB7A347696"
BOS_RINK_WORKSTATION_AK="RRC.WKS98"
BOS_RINK_USERNAME="WEBB2C-RRC"
BOS_RINK_PASSWORD="Ks4vnB2MAdHHM4rRQi33"
MONGODB_DB="viva-cache"
KOUNT_MID="102133"
KOUNT_KHASH_KEY="1b^jIFD)e1@<ZKuH”A+?Ea\\`p+ATAo6@:Wee+EM+(FD5Z2/N<"
KOUNT_ENV="PROD"
KOUNT_API_URL="https://api.kount.com"
KOUNT_CLIENT_ID="102133"
KOUNT_API_KEY="MG9hMWw0dHB5M3NJd2Y1azgzNTg6aDl6cy1McElpQUdZTHNSbUNfcllZanZKSjhJRmpvMThWSkRya3lsUjh6dnVIM2w3eTVkSHpNa09jZVVWX3hoWA=="
KOUNT_AUTH_URL="https://login.kount.com/oauth2/ausdppksgrbyM0abp357/v1/token"
GTM_ENV="production"
ENABLE_NEW_RELIC_LOGGING="true"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"
ENV_ALL_IN_PASS_ADULT_PEAK_AK="TOR.EVN1.MCC5989"
ENV_ALL_IN_PASS_CHILD_PEAK_AK="TOR.EVN1.MCC5991"
ENV_ALL_IN_PASS_SENIOR_PEAK_AK="TOR.EVN1.MCC5990"
ENV_BUNNY_PHOTO_PRODUCT_AK="TOR.EVN1.MCC5997"
ENV_FIVE_ACRES_PRODUCT_AK="TOR.EVN1.MCC6056"
ENV_ON_THE_ROCKS_EVENT_AK="TOR.EVN34"

[context.oljs.environment]
SHIFT4_RINK_ACCESS_TOKEN = "04BD8A12-56AB-414D-ACA8-7B1D71931890"
SHIFT4_TOR_ACCESS_TOKEN = "E420DB85-F48F-477B-853C-9EB23D5258FD"
SHIFT4_API_URL = "https://utg.shift4api.net/api/rest/v1"
SHIFT4_I4GO_URL = "https://access.i4go.com"
BOS_API_URL = "https://tor-isapi-b2c.azurewebsites.net/BosServices.dll"
BOS_TOR_SESSION_ID = "1B81F83F7A0342ABB548D6FD5"
BOS_TOR_USERNAME = "WEB2C1"
BOS_TOR_PASSWORD = "WbVtXQWEzw6DjBWDTQNY"
BOS_TOR_WORKSTATION_AK = "TOR.WKS138"
BOS_RINK_SESSION_ID="EF63C62A7B8B4E2EB7A347696"
BOS_RINK_WORKSTATION_AK="RRC.WKS98"
BOS_RINK_USERNAME="WEBB2C-RRC"
BOS_RINK_PASSWORD="Ks4vnB2MAdHHM4rRQi33"

[context.staging.environment]
SANITY_DATASET = "staging"
GTM_AUTH = "4GiqkFCWxrytH9vaQXuAWg"
GTM_PREVIEW = "env-1166"

[context.phase4.environment]
SANITY_DATASET = "main"

[context.release.environment]
ROBOTS_POLICY = "production"
ENABLE_NEW_RELIC_LOGGING="true"
NEW_RELIC_LICENSE_KEY="NRJS-06407ff2cd5a590aafc"

[functions]
included_files = ["../../.pnp.cjs"]

[[plugins]]
package = "./netlify/plugins/sdks/@rockefeller-center/netlify-plugin"

[plugins.inputs]
commands = ["lint", "typecheck"]
package_manager = "yarn"
working_directories = [
  "../components",
  "../hzdg",
  "../icons",
  ".",
]

[[plugins]]
package = "./netlify/plugins/sdks/@netlify/plugin-gatsby"

[[plugins]]
package = "./netlify/plugins/sdks/@rockefeller-center/netlify-plugin-pnp"

[[plugins]]
package = "./netlify/plugins/sdks/@rockefeller-center/netlify-plugin-cloudflare-caching"

[plugins.inputs]
auth_token = "VJ58KT3Rwv3aUGBYStfTQ6zsZvsPHYtCBeuUM1__"
zone_id = "a35f7b3dfb02e748c42ab4c83456d94f"

[[redirects]]
  from = "/contact/rink-private-lessons"
  to = "/contact"
  force = true
  status = 307

  [[redirects]]
  from = "/buy-tickets/christmas-tree-photo"
  to = "/buy-tickets"
  force = true
  status = 307

# Redirect /buy-tickets/citypass
[[redirects]]
  from = "/buy-tickets/citypass"
  to = "https://www.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

# Redirect /buy-tickets/refunds
[[redirects]]
  from = "/buy-tickets/refunds/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/de-de/tickets-kaufen/erstattungen/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/es-us/comprar-boletos/reembolsos/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/fr-fr/acheter-des-billets/remboursements/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/it-it/comprare-biglietti/rimborsi/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/ja-jp/チケットを買う/返金/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/ko-kr/티켓-구매하기/환불/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/pt-br/comprar-ingressos/reembolsos/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/zh-cn/买票/refunds/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307
[[redirects]]
  from = "/zh-tw/買票/退款/"
  to = "/contact/top-of-the-rock"
  force = true
  status = 307

[[redirects]]
  from = "/de-de/tickets-kaufen/citypass/"
  to = "https://de.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/es-us/comprar-boletos/citypass/"
  to = "https://es.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/fr-fr/acheter-des-billets/citypass/"
  to = "https://fr.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/it-it/comprare-biglietti/citypass/"
  to = "https://it.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/ja-jp/%E3%83%81%E3%82%B1%E3%83%83%E3%83%88%E3%82%92%E8%B2%B7%E3%81%86/citypass/"
  to = "https://ja.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/ko-kr/%ED%8B%B0%EC%BC%93-%EA%B5%AC%EB%A7%A4%ED%95%98%EA%B8%B0/citypass/"
  to = "https://www.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/pt-br/comprar-ingressos/citypass/"
  to = "https://pt.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/zh-cn/%E4%B9%B0%E7%A5%A8/citypass/"
  to = "https://zh.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

[[redirects]]
  from = "/zh-tw/%E8%B2%B7%E7%A5%A8/citypass/"
  to = "https://zh.citypass.com/new-york?mv_source=topoftherock&campaign=buytickets-cp"
  force = true
  status = 307

# Redirect /buy-tickets/citypass-redemption
[[redirects]]
  from = "/buy-tickets/citypass-redemption/"
  to = "/buy-tickets/"
  force = true
  status = 307

[[redirects]]
  from = "/de-de/tickets-kaufen/citypass-einloesen/"
  to = "/de-de/tickets-kaufen/"
  force = true
  status = 307

[[redirects]]
  from = "/es-us/comprar-boletos/canje-citypass/"
  to = "/es-us/comprar-boletos/"
  force = true
  status = 307

[[redirects]]
  from = "/fr-fr/acheter-des-billets/%C3%A9change-citypass/"
  to = "/fr-fr/acheter-des-billets/"
  force = true
  status = 307

[[redirects]]
  from = "/it-it/comprare-biglietti/citypass-riscatto/"
  to = "/it-it/comprare-biglietti/"
  force = true
  status = 307

[[redirects]]
  from = "/ja-jp/%E3%83%81%E3%82%B1%E3%83%83%E3%83%88%E3%82%92%E8%B2%B7%E3%81%86/citypass-%E5%BC%95%E6%8F%9B/"
  to = "/ja-jp/%E3%83%81%E3%82%B1%E3%83%83%E3%83%88%E3%82%92%E8%B2%B7%E3%81%86/"
  force = true
  status = 307

[[redirects]]
  from = "/ko-kr/%ED%8B%B0%EC%BC%93-%EA%B5%AC%EB%A7%A4%ED%95%98%EA%B8%B0/citypass-%EC%83%81%ED%99%98/"
  to = "/ko-kr/%ED%8B%B0%EC%BC%93-%EA%B5%AC%EB%A7%A4%ED%95%98%EA%B8%B0/"
  force = true
  status = 307

[[redirects]]
  from = "/pt-br/comprar-ingressos/resgate-citypass/"
  to = "/pt-br/comprar-ingressos/"
  force = true
  status = 307

[[redirects]]
  from = "/zh-cn/%E4%B9%B0%E7%A5%A8/citypass-redemption/"
  to = "/zh-cn/%E4%B9%B0%E7%A5%A8/"
  force = true
  status = 307

[[redirects]]
  from = "/zh-tw/%E8%B2%B7%E7%A5%A8/citypass-%E5%85%8C%E6%8F%9B/"
  to = "/zh-tw/%E8%B2%B7%E7%A5%A8/"
  force = true
  status = 307

[[redirects]]
  from = "/nyc"
  to = "/buy-tickets/?utm_source=paid-linknyc&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/NYC"
  to = "/buy-tickets/?utm_source=paid-linknyc&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/newyork"
  to = "/buy-tickets/?utm_source=paid-busshelters&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/NewYork"
  to = "/buy-tickets/?utm_source=paid-busshelters&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/NEWYORK"
  to = "/buy-tickets/?utm_source=paid-busshelters&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/bus"
  to = "/buy-tickets/?utm_source=paid-touristbus&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/Bus"
  to = "/buy-tickets/?utm_source=paid-touristbus&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/BUS"
  to = "/buy-tickets/?utm_source=paid-touristbus&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/jfk"
  to = "/buy-tickets/?utm_source=paid-jfkairport&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/JFK"
  to = "/buy-tickets/?utm_source=paid-jfkairport&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/lga"
  to = "/buy-tickets/?utm_source=paid-lgaairport&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/LGA"
  to = "/buy-tickets/?utm_source=paid-lgaairport&utm_medium=vanityurl&utm_campaign=THEBEAM&utm_content=tor"
  force = true
  status = 307

[[redirects]]
  from = "/magazine/food-drinks/rainbow-room-luxury-nyc-brunch/"
  to = "/private-events/rainbow-room/"
  force = true
  status = 307

[[redirects]]
  from = "/citibike"
  to = "https://www.rockefellercenter.com/buy-tickets/?utm_source=paid-citibike&utm_medium=vanityurl&utm_campaign=SKYLIFT&utm_content=tor"
  force = true
  status = 307

  [[redirects]]
  from = "/events/rockefeller-center-christmas-tree-lighting/"
  to = "/holidays/rockefeller-center-christmas-tree-lighting/"
  force = true
  status = 307
