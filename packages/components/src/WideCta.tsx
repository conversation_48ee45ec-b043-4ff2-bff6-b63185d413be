/** @jsxImportSource theme-ui @jsxRuntime classic */
import { H } from '@hzdg/sectioning';
import React from 'react';

import { useLocalizedLink } from '../../rockefellercenter/src/utils/use-localized-link';

import { Container, Flex, Box, Text, Link } from '.';

export interface WideCtaProps {
  border?: boolean;
  caption: string;
  title: string;
  link?: {
    url?: string;
    label?: string;
  };
  customCTA?: React.ReactNode;
}

export const WideCta = ({
  border,
  caption,
  title,
  link,
  customCTA,
}: WideCtaProps): JSX.Element => {
  const getLocalizedLink = useLocalizedLink();

  return (
    <Container
      sx={{ px: border ? [3, 4] : null, py: border ? [3, '74px'] : null }}
    >
      <Flex
        sx={{
          alignItems: ['flex-start', 'center'],
          bg: 'background',
          border: border ? '1px solid' : 'none',
          borderColor: 'accent',
          flexDirection: ['column', null, null, 'row'],
          justifyContent: ['space-around', 'space-between'],
          px: border ? [3, null, null, '36px'] : null,
          py: '36px',
        }}
      >
        <Flex
          sx={{
            flexDirection: ['column', null, null, 'row'],
            alignItems: ['flex-start', 'center'],
          }}
        >
          <H
            sx={{
              variant: 'text.heading',
              fontFamily: 'headingSecondary',
              flex: '0 0 auto',
              fontSize: 7,
              letterSpacing: 0,
              my: 2,
              maxWidth: '325px',
            }}
          >
            {title}
          </H>
          <Text
            as="p"
            sx={{
              flex: '1 1 auto',
              ml: [0, null, null, 5],
              py: [4, 2],
              textAlign: ['left', 'center', 'center', 'left'],
              opacity: 0.8,
              maxWidth: 450,
            }}
            variant="mediumP"
          >
            {caption}
          </Text>
        </Flex>
        <Box
          data-wide-cta
          sx={{
            flex: '0 0 auto',
            mt: [2, null, null, 0],
            mb: [3, null, null, 0],
            ml: [0, null, null, 5],
          }}
        >
          {customCTA ? (
            customCTA
          ) : link?.url && link?.label ? (
            <Link href={getLocalizedLink(link.url)} variant="underline">
              {link.label}
            </Link>
          ) : null}
        </Box>
      </Flex>
    </Container>
  );
};
