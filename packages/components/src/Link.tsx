/** @jsxImportSource theme-ui @jsxRuntime classic */
import React, { useCallback, useLayoutEffect, useRef, useState } from 'react';
import { ThemeUIStyleObject, Link as ThemeUiLink } from 'theme-ui';
import { withPrefix, Link as GatsbyLink } from 'gatsby';
import { useLocation } from '@reach/router';
import { isDOMInstance, getNearestScrollNode } from '@hzdg/dom-utils';

import { useHeaderHeight } from './useHeaderHeight';
import { useMergedRef } from './useMergedRef';
import { actions, useAppDispatch } from './store';
import { useScrollTo } from './useScrollTo';

import type { MouseEvent, ComponentProps } from 'react';
import type { GatsbyLinkProps } from 'gatsby-link';
import type { TishmanVariant } from '.';

export interface InternalLinkProps<TState>
  extends Omit<GatsbyLinkProps<TState>, 'ref' | 'innerRef'> {
  sx?: ThemeUIStyleObject<'links'>;
  /** The link style variant to use. */
  variant?: TishmanVariant<'links'>;
  useGatsbyLink?: boolean;
}

/**
 * A `GatsbyLink`-compatible component that works like `GatsbyLink`
 * in a Gatsby app, but like a @reach/router `Link` everywhere else.
 */
export function InternalLink<TState>({
  sx,
  variant,
  useGatsbyLink = true,
  ...props
}: InternalLinkProps<TState>): React.JSX.Element {
  if (useGatsbyLink) {
    return (
      <ThemeUiLink
        as={GatsbyLink}
        sx={sx as ThemeUIStyleObject}
        variant={variant ? `links.${variant}` : undefined}
        {...props}
      />
    );
  } else {
    return (
      <ThemeUiLink
        href={props.to}
        sx={sx as ThemeUIStyleObject}
        variant={variant ? `links.${variant}` : undefined}
        {...props}
      />
    );
  }
}

export interface AnchorLinkProps<TState>
  extends Omit<GatsbyLinkProps<TState>, 'ref' | 'ref'> {
  sx?: ThemeUIStyleObject<'links'>;
  /** The link style variant to use. */
  variant?: TishmanVariant<'links'>;
  ref?: React.RefObject<HTMLAnchorElement>;
}

/**
 * A `GatsbyLink`-compatible component that updates the location hash
 * (i.e., `<AnchorLink to="#id">`) rather than navigating to a new location.
 */
export function AnchorLink<TState>({
  to,
  replace,
  state,
  onClick,
  sx,
  variant,
  className,
  style,
  activeStyle,
  activeClassName = 'active',
  ...props
}: AnchorLinkProps<TState>): React.JSX.Element {
  const dispatch = useAppDispatch();
  const { pathname, hash } = useLocation();

  /** A ref to be passed to the anchor element. */
  const ref = useRef<HTMLAnchorElement>(null);
  /** The scrollable node to target for scroll effects. */
  const domTarget = useRef<HTMLElement | null>(null);

  const href = withPrefix(`${pathname}${to}`);
  const [isActive, setIsActive] = useState(false);

  useLayoutEffect(() => {
    if (typeof window !== 'undefined') {
      setIsActive(window.location.hash === to);
    }
  }, [to, hash]);

  // We'll determine the offset dynamically based on the target section
  const [scrollOffset, setScrollOffset] = useState({ x: 0, y: 0 });

  // Get the sticky header height
  const headerHeight = useHeaderHeight();

  const scrollTo = useScrollTo({
    domTarget: domTarget.current,
    behavior: 'smooth',
    forceAnimation: true,
    onStart: () => void dispatch(actions.anchorSections.setIsAnimating(true)),
    onRest: () => void dispatch(actions.anchorSections.setIsAnimating(false)),
  });
  /**
   * We have to manually navigate on click to prevent Gatsby from
   * automatically jumping the scroll position to the element.
   */
  const handleClick = useCallback(
    (e: MouseEvent<HTMLAnchorElement>) => {
      onClick?.(e);

      if (e.defaultPrevented) {
        return;
      }

      e.preventDefault();

      // Get the target element and check for custom scroll offset
      const targetId = decodeURIComponent(to.slice(1));
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        // Check for custom scroll offset in data attributes
        const offsetX = parseFloat(
          targetElement.getAttribute('data-scroll-offset-x') || '0',
        );
        const offsetY = parseFloat(
          targetElement.getAttribute('data-scroll-offset-y') || '0',
        );

        // Use the sticky header height from our hook
        const stickyHeaderHeight = headerHeight;

        // Apply the scroll offset immediately
        const newOffset = {
          x: offsetX,
          y: offsetY - stickyHeaderHeight, // Subtract sticky header height from the offset
        };
        setScrollOffset(newOffset);

        // Use setTimeout to ensure the offset is applied before scrolling
        setTimeout(() => {
          scrollTo(targetId);
        }, 0);
      } else {
        // If target element doesn't exist, just scroll to the ID
        scrollTo(targetId);
      }
    },
    [onClick, scrollTo, to, headerHeight],
  );

  const refCallback = useCallback(
    (node: HTMLAnchorElement | null) => {
      if (!node) return;

      const target = document.getElementById(decodeURIComponent(to.slice(1)));
      if (!target) return;

      // Check for custom scroll offset in data attributes
      const offsetX = parseFloat(
        target.getAttribute('data-scroll-offset-x') || '0',
      );
      const offsetY = parseFloat(
        target.getAttribute('data-scroll-offset-y') || '0',
      );

      // Use the sticky header height from our hook
      const stickyHeaderHeight = headerHeight;

      // Apply the scroll offset immediately
      const newOffset = {
        x: offsetX,
        y: offsetY - stickyHeaderHeight, // Subtract sticky header height from the offset
      };
      setScrollOffset(newOffset);

      const scrollNode = getNearestScrollNode(target);
      domTarget.current = isDOMInstance<HTMLHtmlElement>(
        scrollNode,
        HTMLHtmlElement,
      )
        ? null
        : scrollNode;
    },
    [to, headerHeight],
  );

  const mergedRef = useMergedRef(refCallback, ref);

  return (
    <InternalLink
      className={
        isActive && activeClassName
          ? (className?.split(' ') || [])
              .concat(activeClassName)
              .filter(Boolean)
              .join(` `)
          : className
      }
      onClick={handleClick}
      ref={mergedRef}
      style={isActive && activeStyle ? { ...style, ...activeStyle } : style}
      sx={
        variant
          ? {
              ...(sx as unknown as ThemeUIStyleObject<'links'> | undefined),
              variant: `links.${variant}`,
            }
          : (sx as unknown as ThemeUIStyleObject | undefined)
      }
      to={href}
      {...props}
    />
  );
}

export interface ExternalLinkProps
  extends Omit<
    ComponentProps<typeof ThemeUiLink>,
    'ref' | 'target' | 'rel' | 'variant'
  > {
  /** The link style variant to use. */
  variant?: TishmanVariant<'links'>;
}

/**
 * A component that renders an external link.
 *
 * External links will open in a new window and block
 * cross-origin destinations from accessing the opener window.
 */
export function ExternalLink(props: ExternalLinkProps): React.JSX.Element {
  return <ThemeUiLink rel="noopener" target="_blank" {...props} />;
}

/**
 * A component that renders an external link.
 *
 * External links will open in a new window and block
 * cross-origin destinations from accessing the opener window.
 */
export function LocalizedLink(props: ExternalLinkProps): React.JSX.Element {
  return <ThemeUiLink rel="noopener" {...props} />;
}

interface LinkPropsBase<TState>
  extends Omit<GatsbyLinkProps<TState>, 'ref' | 'innerRef' | 'to'> {
  to?: string;
  href?: string;
  /** The link style variant to use. */
  variant?: TishmanVariant<'links'>;
}

export interface LinkPropsWithTo<TState>
  extends Omit<LinkPropsBase<TState>, 'href'> {
  /**
   * The url string to navigate to.
   * Can be internal or external.
   */
  to: string;
}

export interface LinkPropsWithHref<TState>
  extends Omit<LinkPropsBase<TState>, 'to'> {
  /**
   * The url string to navigate to.
   * Can be internal or external.
   */
  href: string;
  useGatsbyLink?: boolean;
}

export type LinkProps<TState> =
  | LinkPropsWithTo<TState>
  | LinkPropsWithHref<TState>;

/**
 * A `GatsbyLink`-compatible component that supports internal,
 * external, and anchor urls.
 *
 * If the url is deemed internal (like `"/slug"`), then the standard
 * `GatsbyLink` behavior is used.
 *
 * If the url is an anchor (like `"#id"`), then the location is updated
 * with the new hash, but the standard gatsby scroll updating is aborted
 * (to allow for custom UI updates based on hash, like scroll animations
 * or tabbed view changes, etc.).
 *
 * If the url is external (like `"https://google.com"`), then
 * a new window will be opened (with protection from cross-origin
 * access to the opener window).
 */
export function Link<TState>(props: LinkPropsWithTo<TState>): React.JSX.Element;
export function Link<TState>(
  props: LinkPropsWithHref<TState>,
): React.JSX.Element;
export function Link<TState>({
  to,
  href = to,
  ...props
}: LinkPropsBase<TState>): React.JSX.Element {
  const location = useLocation();
  const { target } = props;

  // Check if href is one of the pages to be handled by OneLink proxy i.e. 'de-de'
  // If it is, treat it as external link so that the proxy will be used
  if (
    href &&
    /(de|es|fr|it|ja|ko|pt|zh)-(de|us|fr|it|jp|ko|br|cn|tw)/.test(href)
  ) {
    return (
      <LocalizedLink
        data-element="Link"
        data-link="LocalizedLink"
        href={href}
        {...props}
      />
    );
  }

  const url = new URL(
    href ?? '',
    location.href ?? 'https://rockefellercenter.com',
  );

  if (href && location && target !== '_blank') {
    if (
      url.origin.includes('localhost') ||
      url.origin.includes('rockefellercenter.com') ||
      url.origin.includes('--rockefellercenter.netlify.app') ||
      url.origin.includes('viva-main-stable') ||
      url.origin.includes('deploy-preview')
    ) {
      if (url.pathname === location.pathname && url.hash) {
        // be used as an anchor link and will scroll to anchor location

        return (
          <AnchorLink
            data-element="Link"
            data-link="AnchorLink"
            to={url.hash}
            {...props}
          />
        );
      }

      // Use Gatsby/Reach Link for internal links.(?!static)
      return (
        <InternalLink
          data-element="Link"
          data-link="InternalLink"
          to={href}
          {...props}
        />
      );
    }
  }

  return (
    <ExternalLink
      data-element="Link"
      data-link="ExternalLink"
      href={href}
      {...props}
    />
  );
}
