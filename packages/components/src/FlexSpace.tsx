/** @jsxImportSource theme-ui @jsxRuntime classic */
import { a, useSpring } from '@react-spring/web';
import React, { useRef, useCallback, useLayoutEffect } from 'react';
import useIntersection from '@hzdg/use-intersection';
import useSize from '@hzdg/use-size';

import Picture from './Picture';

import { Grid, Box, Text, Link } from '.';

import type { IGatsbyImageData } from 'gatsby-plugin-image';

interface FlexSpaceProps {
  spaceOne: SpaceProps;
  spaceTwo?: SpaceProps;
  bgImage: IGatsbyImageData;
  bgImageAlt?: string;
}

interface SpaceProps {
  title?: string;
  description?: string;
  link?: {
    url?: string;
    label?: string;
  };
}

export const FlexSpace = ({
  spaceOne,
  spaceTwo,
  bgImage,
  bgImageAlt = '',
}: FlexSpaceProps): JSX.Element => {
  const spaceOneRef = useRef<HTMLDivElement>(null);
  const spaceTwoRef = useRef<HTMLDivElement>(null);
  const [growStyle1, animateGrow1] = useSpring(() => ({ height: 0 }));
  const [growStyle2, animateGrow2] = useSpring(() => ({ height: 0 }));
  const [fadeStyle, animateFade] = useSpring(() => ({ opacity: 0 }));

  const measureAndUpdate = useCallback(() => {
    if (spaceOneRef.current) {
      void animateGrow1.start({
        height: spaceOneRef.current.offsetHeight,
      });
    }
    if (spaceTwoRef.current) {
      void animateGrow2.start({
        height: spaceTwoRef.current.offsetHeight,
      });
    }
  }, [animateGrow1, animateGrow2]);

  useLayoutEffect(() => {
    measureAndUpdate();
  }, [measureAndUpdate]);

  useSize(spaceOneRef, measureAndUpdate);
  useSize(spaceTwoRef, measureAndUpdate);

  const containerRef = useIntersection(
    ({ isIntersecting, rootBounds, boundingClientRect: rect }) => {
      const intersects = isIntersecting || (rootBounds?.top ?? 0) >= rect.top;

      if (spaceOneRef.current) {
        void animateGrow1.start({
          height: intersects ? spaceOneRef.current.offsetHeight : 0,
        });
      }
      if (spaceTwoRef.current) {
        void animateGrow2.start({
          height: intersects ? spaceTwoRef.current.offsetHeight : 0,
        });
      }
      void animateFade.start({
        opacity: intersects ? 1 : 0,
        delay: intersects ? 100 : 0,
      });
    },
    {
      threshold: 0.4,
    },
  );

  return (
    <Grid
      data-module="FlexSpace"
      ref={containerRef}
      sx={{
        px: [0, 6],
        pt: [0, 6],
        pb: [0, spaceTwo?.title ? 6 : 164],
        gridTemplateColumns: ['1fr', '1fr 1fr'],
        gridAutoRows: ['1fr', 'auto'],
        rowGap: [0, 6],
        minHeight: ['100%', 'auto'],
        gridTemplateAreas: [null, `'topL .' '. btmR'`],
        backgroundImage: ['none', `url(${bgImage?.images?.fallback?.src})`],
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <Box sx={{ display: ['block', 'none'] }}>
        <Picture alt={bgImageAlt} src={bgImage?.images?.fallback?.src || ''} />
      </Box>
      {/* SPACE ONE */}
      <Box
        ref={spaceOneRef}
        sx={{
          position: 'relative',
          gridArea: [null, 'topL'],
          mr: [0, 0],
          px: [3, 5],
          pt: [4, 6],
          pb: [5, 7],
          maxWidth: [null, 526],
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            background: 'black',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        >
          <a.div style={growStyle1} />
        </Box>
        <Box sx={{ position: 'relative' }}>
          <a.div style={fadeStyle}>
            <Text
              sx={{
                mb: 3,
                variant: 'styles.h2',
                fontFamily: 'headingSecondary',
                color: 'white',
                letterSpacing: 3,
              }}
            >
              {spaceOne.title}
            </Text>
            {spaceOne.description && (
              <Text
                sx={{ mb: 4, fontSize: [2, 3], color: 'white' }}
                variant="mediumP"
              >
                {spaceOne.description}
              </Text>
            )}
            <Link
              href={spaceOne.link?.url || ''}
              sx={{
                'color': 'white',
                'position': 'relative',
                '::after': {
                  content: '""',
                  position: 'absolute',
                  backgroundColor: 'white',
                  height: 2,
                  left: 0,
                  top: 4,
                  right: 0,
                  display: 'block',
                  width: '100%',
                },
              }}
              variant="underline"
            >
              <Box sx={{ color: 'white', display: 'inline-block' }}>
                {spaceOne.link?.label}
                <span className="sr-only">{` about ${spaceOne?.title}`}</span>
              </Box>
            </Link>
          </a.div>
        </Box>
      </Box>
      {/* OPTIONAL SPACE TWO */}
      {spaceTwo?.title && (
        <Box
          ref={spaceTwoRef}
          sx={{
            position: 'relative',
            gridArea: [null, 'btmR'],
            justifySelf: ['stretch', 'end'],
            mr: [0, 0],
            px: 4,
            pt: 4,
            pb: 5,
            maxWidth: [null, 483],
            width: '100%',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              background: 'white',
              bottom: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          >
            <a.div style={growStyle2} />
          </Box>
          <Box
            sx={{
              position: 'relative',
            }}
          >
            <a.div style={fadeStyle}>
              <Text
                sx={{
                  mb: 3,
                  variant: 'styles.h4',
                  fontFamily: 'headingSecondary',
                  color: 'black',
                }}
              >
                {spaceTwo?.title}
              </Text>
              {spaceTwo.description && (
                <Text
                  sx={{ mb: 4, color: 'black', fontSize: [2, 3] }}
                  variant="mediumP"
                >
                  {spaceTwo.description}
                </Text>
              )}
              <Link
                href={spaceTwo.link?.url || ''}
                sx={{
                  'color': 'black',
                  '::after': {
                    content: '""',
                    position: 'absolute',
                    backgroundColor: 'black',
                    height: 2,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    display: 'block',
                  },
                }}
                variant="underline"
              >
                {spaceTwo.link?.label}
                <span className="sr-only">{` about ${spaceTwo?.title}`}</span>
              </Link>
            </a.div>
          </Box>
        </Box>
      )}
    </Grid>
  );
};
