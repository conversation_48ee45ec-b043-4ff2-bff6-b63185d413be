/** @jsxImportSource theme-ui @jsxRuntime classic */
import React, { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { Container, IntrinsicBox } from '..';

import type { FieldValues, Path, SubmitHandler } from 'react-hook-form';

type SubmitError = {
  message: { message: string; errorTicket: string; error: string }[];
};

const apiResponseSchema = z
  .array(
    z.object({
      message: z.string(),
    }),
  )
  .min(1, { message: 'api response array cannot be empty.' });

const getSafeMessageFromResponse = async (
  response: Response,
): Promise<string> => {
  const responseClone = response.clone();

  try {
    const jsonPayload: unknown = await response.json();
    const validatedData = apiResponseSchema.parse(jsonPayload);
    return validatedData[0].message;
  } catch (error) {
    console.warn(
      'could not parse response as expected json. falling back to text.',
      {
        error,
      },
    );
    return responseClone.text();
  }
};

export interface FormProps<TFieldValues extends FieldValues>
  extends Omit<React.FormHTMLAttributes<HTMLFormElement>, 'onSubmit'> {
  id: string;
  // Image Dimesnions for file Upload in Flag Project Form
  dimensions?: {
    width: number | null;
    height: number | null;
  };
  onSubmit?: SubmitHandler<TFieldValues>;
  showErrorMessage?: boolean;
}

export const encodePhoto = async (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => {
      if (!reader.result) reject();
      resolve(String(reader.result));
    });
    if (file) {
      reader.readAsDataURL(file);
    }
  });

export function Form<TFieldValues extends FieldValues>({
  dimensions,
  showErrorMessage,
  ...props
}: FormProps<TFieldValues>): JSX.Element {
  const [submitAttempt, setSubmitAttempt] = useState(false);

  const methods = useFormContext<TFieldValues>();
  const { clearErrors, handleSubmit, formState, setError } = methods;

  const submitForm: SubmitHandler<TFieldValues> = async (values, event) => {
    event?.preventDefault();
    setSubmitAttempt(true);
    clearErrors('server' as Path<TFieldValues>);

    const formData = new FormData();
    formData.append('id', props.id);

    for (const i in values) {
      if (!Object.prototype.hasOwnProperty.call(values, i)) continue;

      const value = values[i] as unknown;

      if (value instanceof FileList) {
        const file = value[0];
        if (!file) continue;

        // eslint-disable-next-line no-await-in-loop
        const encodedPhoto = await encodePhoto(file);
        formData.append(i, encodedPhoto);
        // filename and filetype used for uploading image to Sanity
        formData.append('filename', file.name);
        formData.append('filetype', file.type);
      } else {
        formData.append(i, value as string);
      }
    }

    try {
      const response = await fetch('/api/submit', {
        method: 'POST',
        body: formData,
      });

      if (response.status !== 200) {
        const responseText = await getSafeMessageFromResponse(response);

        setError('server' as Path<TFieldValues>, {
          type: 'manual',
          message: responseText,
        });
      } else {
        props.onSubmit?.(values, event);
      }
    } catch (e) {
      setError('fetch' as Path<TFieldValues>, {
        type: 'manual',
        message: (e as Error).message,
      });
    }
  };

  const submitFormWithLargeImage: SubmitHandler<TFieldValues> = async (
    values,
    event,
  ) => {
    event?.preventDefault();

    setSubmitAttempt(true);
    clearErrors('server' as Path<TFieldValues>);

    const formData = new FormData();
    formData.append('imageHeight', dimensions?.height?.toString() ?? '');
    formData.append('imageWidth', dimensions?.width?.toString() ?? '');

    formData.append('id', props.id);
    formData.append('form-name', event?.target.getAttribute('name'));

    for (const i in values) {
      if (!Object.prototype.hasOwnProperty.call(values, i)) continue;

      const value = values[i] as unknown;

      if (value instanceof FileList) {
        const file = value[0];
        if (!file) continue;

        const dateString = new Date()
          .toLocaleDateString('en-US', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit',
          })
          .replaceAll('/', '-');

        const {
          firstName = 'first',
          lastName = 'last',
          artworkTitle = 'artwork',
        } = values;

        const extension = file.type.replace(/(.*)\//g, '');
        const name = `${firstName}_${lastName}_${artworkTitle}`.replace(
          /[^a-z0-9_]/gi,
          '-',
        );
        const filename = `${name}_${dateString}.${extension}`;

        formData.append('file', file, filename);
      } else {
        formData.append(i, value as string);
      }
    }

    fetch('/', {
      method: 'POST',
      body: formData,
    })
      .then((response) => {
        response.status === 200 && props.onSubmit?.(values, event);
        // Scroll to top of the page if submission went through
        window?.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth',
        });
      })
      .catch((error: Error) =>
        setError('fetch' as Path<TFieldValues>, {
          type: 'manual',
          message: (error as Error).message,
        }),
      );
  };

  const errorMessages = useMemo(
    () =>
      Object.entries(formState.errors).map(([errorKey, error]) => {
        if (error && 'message' in error && Array.isArray(error.message)) {
          const ticket = error.message[0].errorTicket;
          const { message } = error.message[0];
          return (
            <div key={errorKey}>
              {message}
              <br />
              {ticket ? ` Ticket: ${ticket}` : null}
              {errorKey === 'fetch' ? 'Fetch Error: 001' : null}
            </div>
          );
        }
        return null;
      }),
    [formState.errors],
  );

  const isFlagProjectForm = props.id === 'flagProjectForm';
  const isTreeSubmitForm = props.id === 'submitYourTreeInquiry';

  return (
    <IntrinsicBox ratio={9 / 1}>
      <Container data-module="Form" sx={{ maxWidth: 900 }}>
        <form
          {...props}
          noValidate
          onSubmit={handleSubmit(
            isFlagProjectForm || isTreeSubmitForm
              ? submitFormWithLargeImage
              : submitForm,
          )}
        />
      </Container>
      {showErrorMessage && (
        <Container data-module="Form">
          {submitAttempt && errorMessages.length > 0 && errorMessages}
        </Container>
      )}
    </IntrinsicBox>
  );
}
