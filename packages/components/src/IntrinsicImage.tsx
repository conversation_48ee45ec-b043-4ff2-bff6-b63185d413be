/** @jsxImportSource theme-ui @jsxRuntime classic */
import React, { forwardRef } from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';

import { IntrinsicBox, IntrinsicBoxProps } from './IntrinsicBox';

import type { IGatsbyImageData } from 'gatsby-plugin-image';
import type {
  ComponentProps,
  ForwardRefExoticComponent,
  RefAttributes,
} from 'react';
import type { CSSProperties } from 'theme-ui';

export type IntrinsicImageProps = IntrinsicBoxProps & {
  image: IGatsbyImageData;
  alt?: string;
  imgStyle?: CSSProperties;
  onLoad?: () => void;
  loading?: 'lazy' | 'eager';
};

export const IntrinsicImage = forwardRef(
  (
    { image, alt, imgStyle, onLoad, loading, ...boxProps }: IntrinsicImageProps,
    ref: ComponentProps<typeof IntrinsicBox>['ref'],
  ) => {
    return (
      <IntrinsicBox
        {...boxProps}
        ref={ref}
        sx={{ position: 'relative', ...boxProps.sx }}
      >
        <GatsbyImage
          alt={alt || ''}
          image={image}
          imgStyle={imgStyle}
          loading={loading}
          onLoad={onLoad}
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
          }}
        />
      </IntrinsicBox>
    );
  },
) as ForwardRefExoticComponent<IntrinsicImageProps & RefAttributes<unknown>>;

IntrinsicImage.displayName = 'IntrinsicImage';
