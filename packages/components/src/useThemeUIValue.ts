/** @jsxImportSource theme-ui @jsxRuntime classic */
import { useThemeUI } from 'theme-ui';

import { useBreakpointIndex } from './useBreakpointIndex'; // or your own hook
import baseTheme from './themes/base';

type ThemeScales = {
  // Dynamically extends from base theme while maintaining interface preference
  [K in keyof typeof baseTheme]: typeof baseTheme[K];
};

export const useThemeUIValue = <T extends string | number>(
  responsiveValue: T[] | T,
  scaleKey?: keyof ThemeScales,
): T => {
  const { theme } = useThemeUI();
  const breakpoint = useBreakpointIndex({ defaultIndex: 0 });

  const value = Array.isArray(responsiveValue)
    ? responsiveValue[breakpoint] ?? responsiveValue[responsiveValue.length - 1]
    : responsiveValue;

  if (scaleKey) {
    const scale = theme?.[scaleKey];
    if (scale && value in scale) {
      return scale[value as keyof typeof scale] ?? value;
    }
  }

  return value;
};
