{"name": "@tishman/components", "version": "0.0.1", "license": "UNLICENSED", "main": "./src/index.js", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}, "./store": {"import": "./src/store/index.ts", "require": "./src/store/index.ts"}}, "repository": {"type": "git", "url": "https://github.com/rockefeller-center/tishman", "directory": "packages/components"}, "scripts": {"lint": "yarn run lint:eslint && yarn run lint:prettier", "lint:eslint": "eslint --fix --ext js,jsx,ts,tsx .", "lint:prettier": "prettier --check --loglevel warn '**/*.{json,svg}'", "typecheck": "tsc --noEmit --pretty"}, "dependencies": {"@emotion/react": "^11.11.3", "@gatsbyjs/reach-router": "1.3.9", "@hzdg/dom-utils": "workspace:*", "@hzdg/focus-scope": "workspace:*", "@hzdg/sectioning": "workspace:*", "@hzdg/use-intersection": "workspace:*", "@hzdg/use-size": "workspace:*", "@mdx-js/react": "2.1.3", "@panzoom/panzoom": "4.5.0", "@reach/router": "npm:@gatsbyjs/reach-router@1.3.9", "@reach/visually-hidden": "0.17.0", "@react-spring/web": "9.5.5", "@react-stately/overlays": "3.4.1", "@reduxjs/toolkit": "1.8.5", "@sanity/block-content-to-react": "3.0.0", "@sanity/image-url": "1.0.1", "@theme-ui/color": "0.14.7", "@tishman/icons": "workspace:*", "date-fns": "2.29.3", "fontfaceobserver": "2.3.0", "gatsby": "patch:gatsby@npm%3A4.24.0#~/.yarn/patches/gatsby-npm-4.24.0-bf5332a9a2.patch", "gatsby-image": "3.11.0", "gatsby-link": "4.25.0", "gatsby-plugin-image": "3.13.1", "immer": "9.0.15", "invariant": "2.2.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-hook-form": "7.52.0", "react-query": "3.39.2", "react-redux": "8.0.4", "react-textarea-autosize": "8.3.4", "redux": "4.2.0", "styled-system": "5.1.5", "theme-ui": "0.16.2", "usehooks-ts": "3.1.1", "zustand": "4.1.1"}, "devDependencies": {"@types/fontfaceobserver": "^2", "@types/invariant": "^2", "@types/node": "^20.12.7", "@types/reach__router": "1.3.10", "@types/react": "18.2", "@types/react-dom": "18.2", "@types/react-helmet-async": "^1.0.0", "@types/react-redux": "7.1.24", "@types/styled-system": "^5", "eslint": "^8.0.0", "prettier": "2.7.1", "typescript": "^5.3.3"}, "peerDependencies": {"@emotion/react": "^11.11.3", "@mdx-js/react": "2.1.3", "gatsby": "4.24.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "3.39.2", "redux": "4.2.0", "theme-ui": "0.14.7"}}