---
allowed-tools: Bash(git:*), getJiraIssue
description: process jira ticket - read description via mcp, plan solution, create branch
---

## jira ticket workflow: $ARGUMENTS

### current context

- ticket: $ARGUMENTS
- current branch: !`git branch --show-current`
- git status: !`git status --porcelain`

### ticket details

!getJiraIssue($ARGUMENTS)

### git branch setup

!`git checkout -B $ARGUMENTS`

## your task

you're now in **plan mode** - analyze the jira ticket above and:

1. **understand the problem**: break down what needs to be done based on the ticket description
2. **plan the solution**: outline your approach step by step
3. **identify files/components**: determine what code needs to be modified
4. **propose implementation**: suggest the specific changes needed

if this requires code changes, proceed with implementation after planning. if it's just analysis/research, focus on providing comprehensive findings.

start with your analysis and planning phase.
