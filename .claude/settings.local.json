{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "<PERSON><PERSON>(diff:*)", "mcp__serena__initial_instructions", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "Bash(yarn typecheck)", "<PERSON><PERSON>(jq:*)", "mcp__serena__replace_regex", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__serena__replace_symbol_body", "mcp__serena__insert_before_symbol", "mcp__serena__find_file", "mcp__serena__find_referencing_symbols", "mcp__atlassian__atlassianUserInfo", "mcp__atlassian__getAccessibleAtlassianResources", "mcp__atlassian__searchJiraIssuesUsingJql", "mcp__atlassian__getJiraIssue", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_resize", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_snapshot", "Bash(playwright test tests/e2e/families-kids-carousel.spec.ts)", "<PERSON><PERSON>(npx playwright test:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:www.gatsbyjs.com)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__playwright__browser_evaluate", "<PERSON><PERSON>(cat:*)", "Bash(gh pr:*)"], "deny": []}}