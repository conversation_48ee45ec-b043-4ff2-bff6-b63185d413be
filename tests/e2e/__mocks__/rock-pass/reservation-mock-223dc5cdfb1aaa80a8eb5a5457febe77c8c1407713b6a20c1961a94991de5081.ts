
export const reservationMockPayload = {
  "flow": "RC_ROCK_PASS",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2026-04-01",
      "to": "2026-04-30"
    },
    "events": [
      "TOR.EVN27"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
