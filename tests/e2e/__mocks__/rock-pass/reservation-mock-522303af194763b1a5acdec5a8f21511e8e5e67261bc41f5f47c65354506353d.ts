
export const reservationMockPayload = {
  "flow": "RC_ROCK_PASS",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-12-01",
      "to": "2025-12-31"
    },
    "events": [
      "TOR.EVN33"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
