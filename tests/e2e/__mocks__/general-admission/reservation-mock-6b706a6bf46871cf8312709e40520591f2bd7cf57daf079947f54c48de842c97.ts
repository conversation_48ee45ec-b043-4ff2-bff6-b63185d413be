
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDayPerformances",
  "parameters": {
    "date": "2025-07-03",
    "time": {
      "from": "08:00:00",
      "to": "08:59:00"
    },
    "events": [
      "TOR.EVN27"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DATE": "2025-07-03",
  "PERFORMANCELIST": {
    "PERFORMANCE": [
      {
        "AK": "TOR.EVN27.PRF8498",
        "AVAILABILITY": {
          "TOTAL": "65",
          "AVAILABLE": "65",
          "GENERALADMISSION": "false"
        },
        "TIME": "08:00:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "08:58:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN27",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC5645",
              "PRICE": "35"
            },
            {
              "AK": "TOR.EVN1.MCC5646",
              "PRICE": "0"
            }
          ]
        }
      },
      {
        "AK": "TOR.EVN27.PRF8499",
        "AVAILABILITY": {
          "TOTAL": "65",
          "AVAILABLE": "65",
          "GENERALADMISSION": "false"
        },
        "TIME": "08:59:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "09:57:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN27",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC5645",
              "PRICE": "35"
            },
            {
              "AK": "TOR.EVN1.MCC5646",
              "PRICE": "0"
            }
          ]
        }
      }
    ]
  }
};
