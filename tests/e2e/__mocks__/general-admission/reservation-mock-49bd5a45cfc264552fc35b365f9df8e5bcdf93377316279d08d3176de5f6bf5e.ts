
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "FindAllPerformanceByCategories",
  "parameters": {
    "date": "2025-07-03",
    "categories": [
      "AIPPPEAK"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 7001,
    "text": "No performance available for the selected date range"
  },
  "ERROR": {
    "CODE": "7001",
    "TEXT": "No performance available for the selected date range",
    "TYPE": "Managed"
  },
  "PERFORMANCELIST": null
};
