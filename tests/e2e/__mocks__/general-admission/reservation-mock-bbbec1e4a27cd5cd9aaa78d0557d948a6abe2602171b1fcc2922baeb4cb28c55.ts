
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-07-01",
      "to": "2025-07-31"
    },
    "events": [
      "TOR.EVN27"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": {
    "DAY": [
      {
        "AVAILABILITY": {
          "TOTAL": "455",
          "AVAILABLE": "425",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-01"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "971",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-02"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-03"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-04"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-05"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-06"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-07"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-08"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-09"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-10"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-11"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-12"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-13"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-14"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-15"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-16"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-17"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-18"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-19"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-20"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-21"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-22"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-23"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-24"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-25"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-26"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-27"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-28"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-29"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-30"
      },
      {
        "AVAILABILITY": {
          "TOTAL": "975",
          "AVAILABLE": "975",
          "GENERALADMISSION": "false"
        },
        "DATE": "2025-07-31"
      }
    ]
  }
};
