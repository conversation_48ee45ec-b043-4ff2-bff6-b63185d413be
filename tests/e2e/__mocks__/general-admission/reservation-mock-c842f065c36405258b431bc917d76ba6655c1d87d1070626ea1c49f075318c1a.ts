
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDayPerformances",
  "parameters": {
    "date": "2025-07-03",
    "time": {
      "from": "08:00:00",
      "to": "08:59:00"
    },
    "events": [
      "TOR.EVN14"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DATE": "2025-07-03",
  "PERFORMANCELIST": {
    "PERFORMANCE": {
      "AK": "TOR.EVN14.PRF10502",
      "AVAILABILITY": {
        "TOTAL": "150",
        "AVAILABLE": "150",
        "GENERALADMISSION": "false"
      },
      "TIME": "08:00:00.000Z",
      "STATUS": "2",
      "SELLABLE": "true",
      "ENDTIME": "08:58:00.000Z",
      "REACHABLE": "true",
      "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
      "EVENTAK": "TOR.EVN14",
      "WAITLISTENABLED": "false",
      "TRANSFERRABLE": "true",
      "PRODUCTLIST": {
        "PRODUCT": [
          {
            "AK": "TOR.EVN1.MCC1622",
            "PRICE": "25"
          },
          {
            "AK": "TOR.EVN1.MCC2147",
            "PRICE": "0"
          }
        ]
      }
    }
  }
};
