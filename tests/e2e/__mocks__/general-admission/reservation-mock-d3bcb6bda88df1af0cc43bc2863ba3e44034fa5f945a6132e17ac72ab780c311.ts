
export const reservationMockPayload = {
  "flow": "NONE",
  "operation": "FindAllPerformanceByCategories",
  "parameters": {
    "date": "2025-06-12",
    "categories": [
      "AIPPPEAK"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "PERFORMANCELIST": {
    "PERFORMANCE": [
      {
        "ID": "301454",
        "AK": "TOR.EVN3.PRF162507",
        "REACHABLE": "true",
        "TRANSFERRABLE": "false"
      },
      {
        "ID": "301455",
        "AK": "TOR.EVN3.PRF162508",
        "REACHABLE": "true",
        "TRANSFERRABLE": "false"
      }
    ]
  }
};
