
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDayPerformances",
  "parameters": {
    "date": "2025-07-02",
    "time": {
      "from": "00:00:00.000Z",
      "to": "23:59:59.000Z"
    },
    "events": [
      "TOR.EVN29"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DATE": "2025-07-02",
  "PERFORMANCELIST": {
    "PERFORMANCE": {
      "AK": "TOR.EVN29.PRF153",
      "AVAILABILITY": {
        "TOTAL": "200",
        "AVAILABLE": "200",
        "GENERALADMISSION": "false"
      },
      "TIME": "06:00:00.000Z",
      "STATUS": "2",
      "SELLABLE": "true",
      "ENDTIME": "05:59:00.000Z",
      "REACHABLE": "true",
      "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
      "EVENTAK": "TOR.EVN29",
      "WAITLISTENABLED": "false",
      "TRANSFERRABLE": "true",
      "PRODUCTLIST": {
        "PRODUCT": {
          "AK": "TOR.EVN1.MCC5655",
          "PRICE": "19"
        }
      }
    }
  }
};
