
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "CheckBasket",
  "parameters": {
    "items": [
      {
        "AK": "TOR.EVN1.MCC410",
        "QTY": 1,
        "performances": [
          "TOR.EVN3.PRF165316"
        ],
        "capacityGuid": "{2C319FF6-A6B4-4ECF-92D9-0F821C5A010F}"
      },
      {
        "AK": "TOR.EVN1.MCC647",
        "QTY": 1,
        "performances": [
          "TOR.EVN3.PRF165316"
        ],
        "capacityGuid": "{CDBADE99-9A76-4B8D-BE1F-7BDF60DE7259}"
      },
      {
        "AK": "TOR.EVN1.MCC644",
        "QTY": 1,
        "performances": [
          "TOR.EVN3.PRF165316"
        ],
        "capacityGuid": "{D7D5C4F0-F1E4-4AD4-A8EE-662D656943EA}"
      }
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "ITEMLIST": {
    "ITEM": [
      {
        "AK": "TOR.EVN1.MCC410",
        "QTY": "1",
        "PEOPLECOUNT": "1",
        "ACTIVEFROM": "2025-07-01",
        "ACTIVETO": "2025-07-30",
        "PERFORMANCELIST": {
          "PERFORMANCE": {
            "AK": "TOR.EVN3.PRF165316",
            "EVENTAK": "TOR.EVN3",
            "EVENTCODE": "TORMAIN",
            "DATETIME": "2025-07-02",
            "ENDDATETIME": "2025-07-02",
            "NAME": "Main Deck",
            "SPACESTRUCTUREAK": "TOR.EVN3.PRF165316.SPS2"
          }
        },
        "PRICE": {
          "CURRENCY": "USD",
          "TAXLIST": {
            "TAX": {
              "NAME": "NYSTT",
              "RATE": "8.875",
              "VALUE": "3.55",
              "APPLIEDON": "40"
            }
          },
          "NET": "40",
          "TAX": "3.55",
          "GROSS": "43.55",
          "PRINTED": "40",
          "FOREIGNCURRENCYLIST": {
            "FOREIGNCURRENCY": {
              "CURRENCY": "USD",
              "GROSS": "43.55"
            }
          }
        },
        "CAPACITYGUID": "{2C319FF6-A6B4-4ECF-92D9-0F821C5A010F}",
        "ORIGINAL": "40",
        "DISCOUNT": "0",
        "SEATCATEGORYPRODUCTLIST": null
      },
      {
        "AK": "TOR.EVN1.MCC647",
        "QTY": "1",
        "PEOPLECOUNT": "1",
        "ACTIVEFROM": "2025-07-01",
        "ACTIVETO": "2025-07-30",
        "PERFORMANCELIST": {
          "PERFORMANCE": {
            "AK": "TOR.EVN3.PRF165316",
            "EVENTAK": "TOR.EVN3",
            "EVENTCODE": "TORMAIN",
            "DATETIME": "2025-07-02",
            "ENDDATETIME": "2025-07-02",
            "NAME": "Main Deck",
            "SPACESTRUCTUREAK": "TOR.EVN3.PRF165316.SPS2"
          }
        },
        "PRICE": {
          "CURRENCY": "USD",
          "TAXLIST": {
            "TAX": {
              "NAME": "NYSTT",
              "RATE": "8.875",
              "VALUE": "3.02",
              "APPLIEDON": "34"
            }
          },
          "NET": "34",
          "TAX": "3.02",
          "GROSS": "37.02",
          "PRINTED": "34",
          "FOREIGNCURRENCYLIST": {
            "FOREIGNCURRENCY": {
              "CURRENCY": "USD",
              "GROSS": "37.02"
            }
          }
        },
        "CAPACITYGUID": "{CDBADE99-9A76-4B8D-BE1F-7BDF60DE7259}",
        "ORIGINAL": "34",
        "DISCOUNT": "0",
        "SEATCATEGORYPRODUCTLIST": null
      },
      {
        "AK": "TOR.EVN1.MCC644",
        "QTY": "1",
        "PEOPLECOUNT": "1",
        "ACTIVEFROM": "2025-07-01",
        "ACTIVETO": "2025-07-30",
        "PERFORMANCELIST": {
          "PERFORMANCE": {
            "AK": "TOR.EVN3.PRF165316",
            "EVENTAK": "TOR.EVN3",
            "EVENTCODE": "TORMAIN",
            "DATETIME": "2025-07-02",
            "ENDDATETIME": "2025-07-02",
            "NAME": "Main Deck",
            "SPACESTRUCTUREAK": "TOR.EVN3.PRF165316.SPS2"
          }
        },
        "PRICE": {
          "CURRENCY": "USD",
          "TAXLIST": {
            "TAX": {
              "NAME": "NYSTT",
              "RATE": "8.875",
              "VALUE": "3.37",
              "APPLIEDON": "38"
            }
          },
          "NET": "38",
          "TAX": "3.37",
          "GROSS": "41.37",
          "PRINTED": "38",
          "FOREIGNCURRENCYLIST": {
            "FOREIGNCURRENCY": {
              "CURRENCY": "USD",
              "GROSS": "41.37"
            }
          }
        },
        "CAPACITYGUID": "{D7D5C4F0-F1E4-4AD4-A8EE-662D656943EA}",
        "ORIGINAL": "38",
        "DISCOUNT": "0",
        "SEATCATEGORYPRODUCTLIST": null
      }
    ]
  },
  "PROMOTIONAPPLIEDLIST": null,
  "RESERVATION": {
    "EXTERNALCODE": null,
    "RESERVATIONOWNER": {
      "AK": null
    },
    "INVOICE": "false"
  },
  "BASKETTOTAL": {
    "CURRENCY": "USD",
    "TAXLIST": {
      "TAX": {
        "NAME": "NYSTT",
        "RATE": "8.875",
        "VALUE": "9.94"
      }
    },
    "NET": "112",
    "TAX": "9.94",
    "GROSS": "121.94",
    "FOREIGNCURRENCYLIST": {
      "FOREIGNCURRENCY": {
        "CURRENCY": "USD",
        "GROSS": "121.94"
      }
    }
  },
  "COUPONLIST": null,
  "FLEXCONTRACTLIST": null,
  "DELIVERYITEMLIST": null,
  "DONATIONITEMLIST": null,
  "AVAILABLEDELIVERYLIST": {
    "AVAILABLEDELIVERY": [
      {
        "AK": "TOR.DLV1"
      },
      {
        "AK": "TOR.DLV2"
      }
    ]
  },
  "PRODUCTERRORLIST": null,
  "PAYMENTMETHODLIST": null
};
