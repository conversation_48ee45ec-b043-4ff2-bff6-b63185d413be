
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-06-01",
      "to": "2025-06-30"
    },
    "events": [
      "TOR.EVN32"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
