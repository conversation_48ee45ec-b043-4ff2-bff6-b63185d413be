
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-08-01",
      "to": "2025-08-31"
    },
    "events": [
      "TOR.EVN16"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
