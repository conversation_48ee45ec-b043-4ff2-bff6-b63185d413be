
export const reservationMockPayload = {
  "flow": "TOR_EXPRESS",
  "operation": "FindAllProductByStatGroup",
  "parameters": {
    "slug": "checkout"
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "PRODUCTLIST": {
    "PRODUCT": {
      "AK": "TOR.EVN1.MCC757",
      "SHEETNAME": "TOR Express Pass",
      "PRICELIST": {
        "CODE": "WEBB2C",
        "ID": "2"
      },
      "PRICETABLE": {
        "AK": "C435CDC3AD044A988F2B020B3D1D2024",
        "ID": "1"
      },
      "PRICE": {
        "CURRENCY": "USD",
        "TAXLIST": {
          "TAX": {
            "NAME": "New York State Tax TOR",
            "RATE": "8.875",
            "VALUE": "0"
          }
        },
        "NET": "0",
        "TAX": "0",
        "GROSS": "95",
        "PRINTED": "0"
      },
      "SORTORDER": "0",
      "PRODUCTTYPE": "1",
      "WARNING": {
        "ACCOUNT": {
          "APPLYTOALL": "false",
          "REQUIRED": "false",
          "REQUIREDTYPE": "0",
          "MEMBERSHIP": "false",
          "FORCEALREADYEXISTING": "false"
        },
        "EXTINFO": {
          "APPLYTOALL": "false",
          "REQUIRED": "false"
        },
        "DATEFROM": {
          "REQUIRED": "false"
        },
        "DATETO": {
          "REQUIRED": "false"
        },
        "PERFORMANCE": {
          "EVENTLIST": {
            "EVENT": {
              "AK": "TOR.EVN9",
              "CODE": "TOREXPP",
              "QTY": "1",
              "TYPE": "1"
            }
          },
          "REQUIRED": "true"
        },
        "PEOPLECOUNT": {
          "REQUIRED": "false"
        },
        "VARIABLEPRICE": {
          "REQUIRED": "false"
        },
        "DYNAMICPRICE": {
          "REQUIRED": "true"
        },
        "SALERESTRICTION": {
          "REQUIRED": "false"
        },
        "MEDIALINK": {
          "REQUIRED": "false"
        },
        "SALEVALIDITY": {
          "REQUIRED": "false"
        },
        "MONEYCARD": {
          "REQUIRED": "false"
        },
        "GIFTAID": {
          "REQUIRED": "false"
        }
      },
      "CODE": "EXPRESSTORWEB",
      "NAME": "Express Pass",
      "DESCRIPTION": "Express Pass",
      "ADDITIONALDESC": null,
      "TEMPLATEAK": "B74ED3DE387F4DBA95CD55635FCB4C63",
      "EVENTLIST": {
        "EVENTBASE": {
          "CODE": "TOREXPP",
          "AK": "TOR.EVN9"
        }
      },
      "I18NLIST": null,
      "STATGROUPLIST": {
        "STATGROUP": {
          "DESCRIPTION": "Express Pass All Ages",
          "CODE": "&XPASSALL"
        }
      },
      "FLEXCONTRACTBASELIST": null,
      "STATUS": "1",
      "PRICETYPE": "7"
    }
  }
};
