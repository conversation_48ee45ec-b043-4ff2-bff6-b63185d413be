
export const reservationMockPayload = {
  "flow": "TOR_GA",
  "operation": "CheckBasket",
  "parameters": {
    "items": [
      {
        "AK": "TOR.EVN1.MCC757",
        "QTY": 4,
        "performances": []
      }
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "ITEMLIST": {
    "ITEM": {
      "AK": "TOR.EVN1.MCC757",
      "QTY": "4",
      "PEOPLECOUNT": "1",
      "ACTIVEFROM": "2025-07-01",
      "ACTIVETO": "2025-07-30",
      "PRICE": {
        "CURRENCY": "USD",
        "TAXLIST": {
          "TAX": {
            "NAME": "NYSTT",
            "RATE": "8.875",
            "VALUE": "8.43",
            "APPLIEDON": "95"
          }
        },
        "NET": "380",
        "TAX": "33.72",
        "GROSS": "413.72",
        "FOREIGNCURRENCYLIST": {
          "FOREIGNCURRENCY": {
            "CURRENCY": "USD",
            "GROSS": "1654.88"
          }
        }
      },
      "ORIGINAL": "380",
      "DISCOUNT": "0"
    }
  },
  "PROMOTIONAPPLIEDLIST": null,
  "RESERVATION": {
    "EXTERNALCODE": null,
    "RESERVATIONOWNER": {
      "AK": null
    },
    "INVOICE": "false"
  },
  "BASKETTOTAL": {
    "CURRENCY": "USD",
    "TAXLIST": {
      "TAX": {
        "NAME": "NYSTT",
        "RATE": "8.875",
        "VALUE": "33.72"
      }
    },
    "NET": "380",
    "TAX": "33.72",
    "GROSS": "413.72",
    "FOREIGNCURRENCYLIST": {
      "FOREIGNCURRENCY": {
        "CURRENCY": "USD",
        "GROSS": "413.72"
      }
    }
  },
  "COUPONLIST": null,
  "FLEXCONTRACTLIST": null,
  "DELIVERYITEMLIST": null,
  "DONATIONITEMLIST": null,
  "AVAILABLEDELIVERYLIST": {
    "AVAILABLEDELIVERY": [
      {
        "AK": "TOR.DLV1"
      },
      {
        "AK": "TOR.DLV2"
      }
    ]
  },
  "PRODUCTERRORLIST": null,
  "PAYMENTMETHODLIST": null
};
