
export const reservationMockPayload = {
  "flow": "TOR_EXPRESS",
  "operation": "GetDayPerformances",
  "parameters": {
    "date": "2026-01-02",
    "time": {
      "from": "00:00:00.000Z",
      "to": "23:59:59.000Z"
    },
    "events": [
      "TOR.EVN14"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DATE": "2025-07-02",
  "PERFORMANCELIST": null
};
