
export const reservationMockPayload = {
  "flow": "TOR_EXPRESS",
  "operation": "GetDayPerformances",
  "parameters": {
    "date": "2025-07-01",
    "time": {
      "from": "00:00:00.000Z",
      "to": "23:59:59.000Z"
    },
    "events": [
      "TOR.EVN14"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DATE": "2025-07-01",
  "PERFORMANCELIST": {
    "PERFORMANCE": [
      {
        "AK": "TOR.EVN14.PRF10483",
        "AVAILABILITY": {
          "TOTAL": "150",
          "AVAILABLE": "150",
          "GENERALADMISSION": "false"
        },
        "TIME": "19:00:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "19:58:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN14",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC1622",
              "PRICE": "25"
            },
            {
              "AK": "TOR.EVN1.MCC2147",
              "PRICE": "0"
            }
          ]
        }
      },
      {
        "AK": "TOR.EVN14.PRF10484",
        "AVAILABILITY": {
          "TOTAL": "150",
          "AVAILABLE": "150",
          "GENERALADMISSION": "false"
        },
        "TIME": "20:00:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "20:58:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN14",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC1622",
              "PRICE": "25"
            },
            {
              "AK": "TOR.EVN1.MCC2147",
              "PRICE": "0"
            }
          ]
        }
      },
      {
        "AK": "TOR.EVN14.PRF10485",
        "AVAILABILITY": {
          "TOTAL": "150",
          "AVAILABLE": "150",
          "GENERALADMISSION": "false"
        },
        "TIME": "21:00:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "21:58:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN14",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC1622",
              "PRICE": "25"
            },
            {
              "AK": "TOR.EVN1.MCC2147",
              "PRICE": "0"
            }
          ]
        }
      },
      {
        "AK": "TOR.EVN14.PRF10486",
        "AVAILABILITY": {
          "TOTAL": "150",
          "AVAILABLE": "150",
          "GENERALADMISSION": "false"
        },
        "TIME": "22:00:00.000Z",
        "STATUS": "2",
        "SELLABLE": "true",
        "ENDTIME": "22:58:00.000Z",
        "REACHABLE": "true",
        "PRICETABLEAK": "C435CDC3AD044A988F2B020B3D1D2024",
        "EVENTAK": "TOR.EVN14",
        "WAITLISTENABLED": "false",
        "TRANSFERRABLE": "true",
        "PRODUCTLIST": {
          "PRODUCT": [
            {
              "AK": "TOR.EVN1.MCC1622",
              "PRICE": "25"
            },
            {
              "AK": "TOR.EVN1.MCC2147",
              "PRICE": "0"
            }
          ]
        }
      }
    ]
  }
};
