
export const reservationMockPayload = {
  "flow": "TOR_EXPRESS",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2026-01-01",
      "to": "2026-01-31"
    },
    "events": [
      "TOR.EVN17"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
