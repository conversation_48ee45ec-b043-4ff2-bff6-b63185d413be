
export const reservationMockPayload = {
  "flow": "TOR_EXPRESS",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-10-01",
      "to": "2025-10-31"
    },
    "events": [
      "TOR.EVN14"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
