
export const reservationMockPayload = {
  "flow": "TOR_VIP_ROCKSTAR",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2025-11-01",
      "to": "2025-11-30"
    },
    "events": [
      "TOR.EVN15"
    ]
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
