
export const reservationMockPayload = {
  "flow": "TOR_VIP_ROCKSTAR",
  "operation": "GetDaysAvailability",
  "parameters": {
    "date": {
      "from": "2026-08-01",
      "to": "2026-08-31"
    },
    "events": [
      "TOR.EVN33"
    ],
    "sellable": false
  }
};

export const reservationMockResponse = {
  "status": {
    "code": 200,
    "text": "Success"
  },
  "ERROR": {
    "CODE": "200",
    "TEXT": "Success",
    "TYPE": "Managed"
  },
  "DAYLIST": null
};
