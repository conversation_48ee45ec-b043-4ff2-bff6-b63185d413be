# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Yarn workspace monorepo** containing the **Rockefeller Center digital platform** - a Gatsby-based website with e-commerce capabilities built with React, TypeScript, and modern web technologies.

## Development Commands

### Initial Setup
```bash
# Install dependencies from root directory
yarn install

# Navigate to main workspace for development
cd packages/rockefellercenter

# Copy environment template
cp .env.sample .env.development
```

### Development Server
```bash
# Start backend server (run in separate terminal)
yarn workspace @tishman/rockefellercenter dev:be

# Start frontend development server
yarn workspace @tishman/rockefellercenter start:dev
```

### Build and Quality Assurance
```bash
# Build from root directory
yarn build

# Run linting
yarn lint

# Run type checking
yarn typecheck

# Run unit tests
yarn test:unit

# Run E2E tests
yarn test:e2e
```

### Single Test Execution
```bash
# Run specific unit test
jest tests/unit/formatToLongDate.test.ts

# Run specific E2E test
playwright test tests/e2e/general-admission.spec.ts
```

## Architecture Overview

### Workspace Structure
- **`packages/rockefellercenter/`** - Main Gatsby website application
- **`packages/components/`** - Shared React component library with Theme-UI
- **`packages/icons/`** - SVG icon library with auto-generated React components
- **`packages/hzdg/`** - Utility libraries for DOM manipulation and React hooks
- **`packages/sanity/`** - Content management system integration
- **`packages/shift4fetch/`** - Payment processing utilities

### Main Application Architecture (`packages/rockefellercenter/src/`)
- **`blocks/`** - Page-specific UI components (100+ specialized blocks)
- **`components/`** - Shared application components and utilities
- **`pages/`** - Gatsby pages and routing configuration
- **`api/`** - API endpoints for form submissions and external integrations
- **`store/`** - Redux Toolkit state management for cart and checkout flows
- **`data/`** - Static data files and GraphQL queries

### Key Technologies
- **Gatsby 5** with React 18 and TypeScript
- **Theme-UI** for design system and responsive styling
- **Sanity CMS** for content management with GraphQL integration
- **Redux Toolkit** + **React Query** for state management
- **Playwright** for E2E testing, **Jest** for unit testing
- **Netlify** for deployment with preview environments

## Data Flow Patterns

### Content Management
- **Sanity CMS** → GraphQL → Gatsby build-time queries → Static pages
- Content changes trigger automatic rebuilds via webhooks
- Multi-language support with translation management

### E-commerce State Management
- **Shopping cart** managed via Redux with localStorage persistence
- **Multi-step checkout** flows using wizard pattern
- **Payment processing** through Shift4 integration
- **Order management** with MongoDB backend

### Component Architecture
- **Block-based design** - each major page section is a self-contained block
- **Responsive-first** development with Theme-UI breakpoints
- **Accessibility-first** approach with proper ARIA labels and focus management
- **Performance optimized** with lazy loading and image optimization

## Testing Strategy

### E2E Testing (Primary)
- **Playwright** tests cover all critical user flows
- **Ticket purchasing** workflows extensively tested
- **Mock data** system for consistent test environments
- Tests located in `tests/e2e/` with page-specific organization

### Unit Testing
- **Jest** with TypeScript for utility functions
- **React Testing Library** for component testing
- Focus on business logic and data transformations
- Tests located in `tests/unit/`

## Development Workflow

### Feature Development
1. **Content modeling** in Sanity CMS if needed
2. **Block/component** development with responsive design
3. **GraphQL queries** for data fetching
4. **E2E tests** for new user flows
5. **Accessibility** audit and performance check

### Code Quality
- **ESLint + Prettier** for consistent code formatting
- **TypeScript strict mode** for type safety
- **Husky pre-commit hooks** for quality gates
- **Comprehensive type generation** from GraphQL schemas

### Deployment
- **Netlify** hosting with automatic preview deployments
- **Sentry** for error monitoring and performance tracking
- **Cloudflare CDN** for global content delivery
- **Firebase** for analytics and user engagement tracking

## Important Notes

### Environment Variables
- Backend server requires `VIVA_CLIENT_BASE_URL="http://localhost:3005"` and `DISABLE_MONGODB_CACHING="true"` in `.env.development`
- Extensive environment variable configuration in `gatsby-config.ts`
- Sensitive keys should never be committed to repository

### Development Dependencies
- **Node.js 20** (use nvm for version management)
- **Yarn 4** with PnP (Plug'n'Play) for fast dependency resolution
- **Bun** for fast backend development server

### Performance Considerations
- **Image optimization** through Gatsby Image Processing
- **Code splitting** with loadable components
- **Static generation** for optimal Core Web Vitals
- **Bundle analysis** available through build tools

## Common Development Tasks

### Adding New Pages
1. Create page component in `src/pages/`
2. Add corresponding block components if needed
3. Update GraphQL queries for data requirements
4. Add routing configuration if dynamic

### Adding New Components
1. Follow existing component patterns in `packages/components/`
2. Use Theme-UI for consistent styling
3. Implement responsive design with breakpoint utilities
4. Add proper TypeScript interfaces

### Updating Content Models
1. Modify schemas in Sanity CMS
2. Update GraphQL queries in Gatsby
3. Regenerate TypeScript types
4. Update component props accordingly

### Testing New Features
1. Write E2E tests for user-facing functionality
2. Add unit tests for business logic
3. Test accessibility with screen readers
4. Verify performance impact

This architecture supports a complex, multi-language e-commerce website while maintaining developer productivity and exceptional user experience.