diff --git a/README.md b/README.md
index d6dff24f7..f780a7a0c 100644
--- a/README.md
+++ b/README.md
@@ -89,10 +89,19 @@ The development environment runs on two server instances. One serves the front e
 
 #### Setup the backend server (fast-server):
 
-1. Install bun:
+1. Install bun and correct node version with nvm:
 
 ```bash
-$ brew install bun
+$ curl -fsSL https://bun.sh/install | bash
+
+# Install NVM
+$ curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash
+
+# Install the node version from `.nvmrc` if not already installed
+nvm install
+
+# Use the node version declared in `.nvmrc`
+nvm use
 ```
 
 2. Ensure your environment variables are set in the `.env.development` file:
diff --git a/packages/rockefellercenter/src/api/date/ny.ts b/packages/rockefellercenter/src/api/date/ny.ts
index d79fee93c..d74424682 100644
--- a/packages/rockefellercenter/src/api/date/ny.ts
+++ b/packages/rockefellercenter/src/api/date/ny.ts
@@ -13,7 +13,7 @@ export interface MonthInfo {
 }
 
 export interface DateTimeResponse extends MonthInfo {
-  future: Record<number, MonthInfo>; // Array of future months' date information
+  future: Record<string, MonthInfo>; // Array of future months' date information
 }
 
 export const formatShortDate = (date: Date) => {
@@ -25,13 +25,14 @@ export const formatShortDate = (date: Date) => {
 
 export const getMonthInfo = (date: Date): MonthInfo => {
   const day = date.getDate();
-  const month = date.getMonth(); // Months are 0-indexed
+  const monthZeroIndexed = date.getMonth(); // Months are 0-indexed
+  const month = monthZeroIndexed;
   const year = date.getFullYear();
   const hours = date.getHours();
   const minutes = date.getMinutes();
 
-  const firstOfMonth = new Date(year, month - 1, 1);
-  const lastOfMonth = new Date(year, month, 0);
+  const firstOfMonth = new Date(year, monthZeroIndexed, 1);
+  const lastOfMonth = new Date(year, monthZeroIndexed + 1, 0);
   const totalDays = lastOfMonth.getDate(); // Total days in the month
 
   return {
@@ -72,7 +73,10 @@ export default async (
       nyDate.getMonth() + i,
       1,
     );
-    const monthKey = nextMonthDate.getMonth() + 1; // Month number (1-12)
+    // Create a unique key that includes year to handle year boundaries correctly
+    const monthKey = `${nextMonthDate.getFullYear()}-${String(
+      nextMonthDate.getMonth() + 1,
+    ).padStart(2, '0')}`;
     response.future[monthKey] = getMonthInfo(nextMonthDate); // Assign month info to the corresponding month key
   }
 
diff --git a/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsAddOnStepBlock/BuyTicketsAddOnStepBlock.tsx b/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsAddOnStepBlock/BuyTicketsAddOnStepBlock.tsx
index d07c65dc9..3f71972e7 100644
--- a/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsAddOnStepBlock/BuyTicketsAddOnStepBlock.tsx
+++ b/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsAddOnStepBlock/BuyTicketsAddOnStepBlock.tsx
@@ -1,7 +1,9 @@
 /** @jsxImportSource theme-ui @jsxRuntime classic */
 
 import { Flex } from '@tishman/components';
-import React, { useContext, useEffect, useRef, useState } from 'react';
+import React, { useContext, useEffect, useRef, useState, useMemo } from 'react';
+import { useStaticQuery, graphql } from 'gatsby';
+import { SanityRichText } from '@tishman/components';
 
 import {
   AddOnItem,
@@ -22,6 +24,8 @@ import {
   selectTotalQuantity,
   selectWizardCurrentStep,
   useAppSelector,
+  useAppDispatch,
+  actions,
 } from '../../../store';
 import {
   AddonLimit,
@@ -35,9 +39,7 @@ import {
   StatGroup,
   ADDON_EVENTS_WITH_CAPACITY,
   SantaDays,
-  SANTA_PHOTO_PRODUCT_AK,
-  SKYLIFT_AK,
-  TAX_INCLUSIVE_PRODUCTS,
+  TICKET_SPECIALS,
 } from '../../../services/viva/constants';
 import {
   useCheckBasketMutation,
@@ -49,7 +51,6 @@ import { AddToCartButton } from '../../../components/AddToCartButton';
 import {
   isWithinSantaTimeConstraints,
   isWithinTimeConstraints,
-  getChampagneToastTimeConstraint,
   putInArray,
   isWithinChampagneToastTimeConstraint,
   isWithinDateConstraints,
@@ -57,6 +58,7 @@ import {
 import { parseTimeslotTime } from '../../../utils/parse-timeslot-hours';
 import { BuyTicketsLayoutContext } from '../../../layouts/BuyTicketsLayout';
 import useIsMobile from '../../../breakpoints/useIsMobile';
+import addOnMarketingText from '../../../data/translations/addOns';
 
 import { styles } from './BuyTicketsAddOnStepBlock.styles';
 
@@ -284,7 +286,95 @@ export const useAddOnItems = ({ stepNumber }: { stepNumber: number }) => {
   return { addOns, isLoading };
 };
 
+// Move Sanity query and utilities here
+const getDescriptionText = (
+  description:
+    | { children?: { text: string }[] }[]
+    | { children?: { text: string }[] },
+): string => {
+  if (!description) return '';
+
+  // If description is an array of blocks
+  if (Array.isArray(description)) {
+    return description
+      .map((block) => {
+        // Get all text from children
+        if (block.children) {
+          return block.children
+            .map((child) => child.text)
+            .filter(Boolean)
+            .join(' ');
+        }
+        return '';
+      })
+      .filter(Boolean)
+      .join('\n');
+  }
+
+  // If description is a single block
+  if (description.children) {
+    return description.children
+      .map((child) => child.text)
+      .filter(Boolean)
+      .join(' ');
+  }
+
+  return '';
+};
+
+const useSanityAddons = () => {
+  const data = useStaticQuery(graphql`
+    query GetAddons {
+      allSanityAddon {
+        edges {
+          node {
+            _id
+            name
+            image {
+              asset {
+                url
+                metadata {
+                  dimensions {
+                    width
+                    height
+                  }
+                }
+              }
+              alt
+              caption
+            }
+            _rawDescription
+            productAK
+            additionalLabel
+            _rawAdditionalText
+          }
+        }
+      }
+    }
+  `);
+
+  return useMemo(() => {
+    return data.allSanityAddon.edges.reduce(
+      // eslint-disable-next-line @typescript-eslint/no-explicit-any
+      (acc: { [key: string]: any }, edge: any) => {
+        const node = edge.node;
+        acc[node.productAK] = {
+          name: node.name,
+          description: getDescriptionText(node._rawDescription),
+          image: node.image?.asset?.url || null,
+          imageAlt: node.image?.alt || '',
+          additionalLabel: node.additionalLabel,
+          additionalText: node._rawAdditionalText,
+        };
+        return acc;
+      },
+      {},
+    );
+  }, [data]);
+};
+
 export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
+  const dispatch = useAppDispatch();
   const availablePerformancesHash =
     useAppSelector((state) => state.order.shopCart.addOnsPerformancesHash) ??
     {};
@@ -300,6 +390,7 @@ export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
   const shopCartQty = useAppSelector(selectTotalQuantity);
   const { addOns, isLoading } = useAddOnItems({ stepNumber });
   const hasPerformances = useAppSelector(selectHasPerformances);
+  const sanityAddons = useSanityAddons();
 
   const evaluateSpecial = (statgroup: StatGroup, hour: number) => {
     return false;
@@ -448,6 +539,43 @@ export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
     return doNotShow;
   });
 
+  // Handle add-on selections at the parent level
+  useEffect(() => {
+    if (!addOns?.length) return;
+
+    const addOnSelections = addOns
+      .filter((addOn) => addOn && addOn.ak) // Ensure addOn exists and has ak
+      .map((addOn) => {
+        const sanityData = addOn.ak ? sanityAddons[addOn.ak] : null;
+        const marketingCopy =
+          sanityData?.description ||
+          (addOnMarketingText.byFlow[flow]?.[addOn.statGroup]?.copy ??
+            addOnMarketingText.byStatGroup[addOn.statGroup]?.copy ??
+            '');
+
+        return {
+          description: marketingCopy,
+          ak: addOn.ak,
+          price: addOn.price * 100,
+          marketingText: addOn.marketingText || '',
+          name: sanityData?.name || addOn.name,
+          image: sanityData?.image || null,
+          quantity: 0,
+          isAddingOn: false,
+          statGroup: addOn.statGroup,
+          flow,
+        };
+      });
+
+    // Only dispatch if we have valid selections
+    if (addOnSelections.length > 0) {
+      dispatch(actions.order.setShopCartAddOnSelections(addOnSelections));
+    } else {
+      // If no valid selections, clear the add-ons
+      // dispatch(actions.order.setShopCartAddOnSelections([]));
+    }
+  }, [addOns, flow, dispatch, sanityAddons]);
+
   // Hide step until we get here
   if (currentStep !== stepNumber) return null;
   // We're now at this step in the flow, but we don't have any addons to show
@@ -482,6 +610,23 @@ export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
 
           if (!shouldShow) return null;
 
+          const sanityData = addOn.ak ? sanityAddons[addOn.ak] : null;
+          const marketingCopy =
+            sanityData?.description ||
+            (addOnMarketingText.byFlow[flow]?.[addOn.statGroup]?.copy ??
+              addOnMarketingText.byStatGroup[addOn.statGroup]?.copy ??
+              '');
+
+          const currentHour = selectedTimeSlot?.startRaw.hours || 0;
+          const ticketSpecial = TICKET_SPECIALS[addOn.statGroup];
+          const ticketOnSpecial =
+            ticketSpecial &&
+            ticketSpecial.onSpecial &&
+            (!ticketSpecial.hoursActive?.length ||
+              (ticketSpecial.hoursActive[0] <= currentHour &&
+                (!ticketSpecial.hoursActive[1] ||
+                  ticketSpecial.hoursActive[1] >= currentHour)));
+
           // Function to determine the maximum quantity of a specific add-on
           const maxQuantity = () => {
             if (SINGLE_QTY_ADDONS.includes(addOn.statGroup)) {
@@ -500,10 +645,24 @@ export const BuyTicketsAddOnStepBlock = ({ stepNumber, width }: TProps) => {
 
           return (
             <AddOn
+              additionalInfo={
+                sanityData?.additionalLabel
+                  ? {
+                      additionalLabel: sanityData.additionalLabel,
+                      additionalText: sanityData.additionalText ? (
+                        <SanityRichText blocks={sanityData.additionalText} />
+                      ) : (
+                        ''
+                      ),
+                    }
+                  : undefined
+              }
               addOn={addOn}
               available={availablePerformancesHash[addOn.ak]?.available ?? 0}
               key={addOn.ak}
+              marketingText={marketingCopy}
               maxQuantity={maxQuantity()}
+              ticketSpecial={ticketOnSpecial ? ticketSpecial : undefined}
             />
           );
         })}
diff --git a/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsCalendar.tsx b/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsCalendar.tsx
index bdf074507..54ce10f19 100644
--- a/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsCalendar.tsx
+++ b/packages/rockefellercenter/src/blocks/VivaBuyTickets/BuyTicketsCalendar.tsx
@@ -350,9 +350,15 @@ export const BuyTicketsCalendar = ({
         }}
       >
         {!twoColumn && page?.dateStep?.description && (
-          <Flex sx={{ alignItems: 'center', gap: 3 }}>
+          <Flex
+            sx={{
+              alignItems: 'center',
+              flexDirection: 'column',
+              gap: 3,
+            }}
+          >
+            {flow === Flow.TOR_GA && <July4thPopup />}
             <Text as="p" sx={{ fontSize: 1 }}>
-              {flow === Flow.TOR_GA && <July4thPopup />}
               {page.dateStep.description} {buyPremiumText}
             </Text>
           </Flex>
diff --git a/packages/rockefellercenter/src/components/AddToCartButton.tsx b/packages/rockefellercenter/src/components/AddToCartButton.tsx
index 88c795340..065443c96 100644
--- a/packages/rockefellercenter/src/components/AddToCartButton.tsx
+++ b/packages/rockefellercenter/src/components/AddToCartButton.tsx
@@ -317,7 +317,6 @@ export const AddToCartButton = ({
         // Overwrite with new add-on selections
         cartAddOnSelections = shopCartAddonSelections;
       }
-
       // Update performances for add-ons if needed
       if (performanceHash) {
         cartAddOnSelections = cartAddOnSelections.map((sel) => ({
@@ -329,7 +328,6 @@ export const AddToCartButton = ({
             : sel.performances,
         }));
       }
-
       dispatch(
         actions.checkout.setAddOns({
           addOns: cartAddOnSelections,
@@ -505,9 +503,21 @@ export const AddToCartButton = ({
         );
 
         const cartAddOnSelectionsWithCompTickets = [...cartAddOnSelections];
+
         compAddOns.forEach((compAddOn) => {
-          cartAddOnSelectionsWithCompTickets.push(compAddOn);
+          const existingIndex = cartAddOnSelectionsWithCompTickets.findIndex(
+            (addOn) => addOn.ak === compAddOn.ak,
+          );
+
+          if (existingIndex !== -1) {
+            // Replace existing add-on with the comp add-on
+            cartAddOnSelectionsWithCompTickets[existingIndex] = compAddOn;
+          } else {
+            // Add new comp add-on if no match found
+            cartAddOnSelectionsWithCompTickets.push(compAddOn);
+          }
         });
+
         dispatch(
           actions.checkout.setAddOns({
             addOns: cartAddOnSelectionsWithCompTickets,
diff --git a/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/index.tsx b/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/index.tsx
index b7007d0b1..65543d38f 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/index.tsx
+++ b/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/index.tsx
@@ -101,6 +101,7 @@ export const AddOnLineItem = (props: IAddOnLineItem) => {
       >
         {/* TICKET DETAILS */}
         <Flex
+          data-stat-group={statGroup}
           data-testid="add-on-line-item-ticket-details"
           sx={{
             borderTop: ['1px solid #D8D8D8', 'none'],
diff --git a/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/useAddOnLineItem.ts b/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/useAddOnLineItem.ts
index e8626e0f8..41e71964f 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/useAddOnLineItem.ts
+++ b/packages/rockefellercenter/src/components/BuyTickets/AddOnLineItem/useAddOnLineItem.ts
@@ -1,4 +1,4 @@
-import React, { useCallback, useEffect, useMemo, useState } from 'react';
+import React, { useCallback, useMemo, useState } from 'react';
 
 import { PHOTO_PKG_AK, StatGroup } from '../../../services/viva/constants';
 import {
@@ -68,44 +68,6 @@ export const useAddOnLineItem = (props: IAddOnLineItem) => {
   // depending on whether it's allowed or the maximum limit is reached
   const quantityDelta = canIncrement ? 1 : maxQuantity;
 
-  // Create an add-on item selection object
-  const addOnItemSelection = useMemo(
-    () => ({
-      description,
-      ak,
-      price: price * 100,
-      marketingText,
-      name: ga4ItemName,
-      image,
-      quantity: initialQuantity,
-      isAddingOn: false,
-      statGroup,
-      flow,
-    }),
-    [
-      ak,
-      description,
-      marketingText,
-      price,
-      initialQuantity,
-      statGroup,
-      name,
-      image,
-    ],
-  );
-
-  // Update the shop cart with the add-on item selection
-  // or increment upsell count based on conditions
-  useEffect(() => {
-    if (canIncrement) {
-      const addOnItemDTO = { ...addOnItemSelection };
-
-      dispatch(actions.order.setShopCartAddOnSelections([addOnItemDTO]));
-    } else {
-      dispatch(actions.order.incrementUpsellCount(Number(initialQuantity)));
-    }
-  }, [initialQuantity, addOnItemSelection, canIncrement, dispatch]);
-
   // Decrement the quantity of an add-on item
   const handleDecrement = useCallback(() => {
     if (
diff --git a/packages/rockefellercenter/src/components/BuyTickets/AddOns/AddOn.tsx b/packages/rockefellercenter/src/components/BuyTickets/AddOns/AddOn.tsx
index 7950ee6c8..36a48f437 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/AddOns/AddOn.tsx
+++ b/packages/rockefellercenter/src/components/BuyTickets/AddOns/AddOn.tsx
@@ -1,13 +1,6 @@
 /** @jsxImportSource theme-ui @jsxRuntime classic */
-import {
-  Flex,
-  Text,
-  Box,
-  SxStyleProp,
-  SanityRichText,
-} from '@tishman/components';
-import React, { useMemo } from 'react';
-import { useStaticQuery, graphql } from 'gatsby';
+import { Flex, Text, Box, SxStyleProp } from '@tishman/components';
+import React from 'react';
 
 import defaultImage from '../../../images/top-rock-at-night.jpg';
 import photoPassImage from '../../../images/photo-pass.jpg';
@@ -24,16 +17,13 @@ import champagneImage from '../../../images/champagne.jpg';
 import ornamentImage from '../../../images/ornament.jpg';
 import {
   useAppSelector,
-  selectFlow,
   selectTotalQuantity,
   selectNumberOfTicketsWithoutChildrenAndToddlers,
   selectHasMembership,
-  selectFirstTimeSlot,
 } from '../../../store';
 import { AddOnLineItem } from '../AddOnLineItem';
-import { StatGroup, TICKET_SPECIALS } from '../../../services/viva/constants';
+import { StatGroup, TicketSpecial } from '../../../services/viva/constants';
 import { AddOnMembershipLineItem } from '../AddOnMembershipLineItem';
-import addOnMarketingText from '../../../data/translations/addOns';
 
 import { styles } from './AddOns.styles';
 
@@ -46,6 +36,11 @@ type TProps = {
   marketingText?: string;
   style?: SxStyleProp;
   maxQuantity?: number;
+  additionalInfo?: {
+    additionalLabel: string;
+    additionalText: string | React.JSX.Element;
+  };
+  ticketSpecial?: TicketSpecial;
 };
 
 const IMAGES: { [key in StatGroup]?: string } = {
@@ -63,127 +58,26 @@ const IMAGES: { [key in StatGroup]?: string } = {
   [StatGroup.CHAMPAGNE_TOAST]: champagneImage,
 };
 
-const getDescriptionText = (description: any): string => {
-  if (!description) return '';
-
-  // If description is an array of blocks
-  if (Array.isArray(description)) {
-    return description
-      .map((block) => {
-        // Get all text from children
-        if (block.children) {
-          return block.children
-            .map((child: any) => child.text)
-            .filter(Boolean)
-            .join(' ');
-        }
-        return '';
-      })
-      .filter(Boolean)
-      .join('\n');
-  }
-
-  // If description is a single block
-  if (description.children) {
-    return description.children
-      .map((child: any) => child.text)
-      .filter(Boolean)
-      .join(' ');
-  }
-
-  return '';
-};
-
-const useSanityAddons = () => {
-  const data = useStaticQuery(graphql`
-    query GetAddons {
-      allSanityAddon {
-        edges {
-          node {
-            _id
-            name
-            image {
-              asset {
-                url
-                metadata {
-                  dimensions {
-                    width
-                    height
-                  }
-                }
-              }
-              alt
-              caption
-            }
-            _rawDescription
-            productAK
-            additionalLabel
-            _rawAdditionalText
-          }
-        }
-      }
-    }
-  `);
-
-  return useMemo(() => {
-    return data.allSanityAddon.edges.reduce(
-      (acc: { [key: string]: any }, edge: any) => {
-        const node = edge.node;
-        acc[node.productAK] = {
-          name: node.name,
-          description: getDescriptionText(node._rawDescription),
-          image: node.image?.asset?.url || null,
-          imageAlt: node.image?.alt || '',
-          additionalLabel: node.additionalLabel,
-          additionalText: node._rawAdditionalText,
-        };
-        return acc;
-      },
-      {},
-    );
-  }, [data]);
-};
-
 export const AddOn = ({
   addOn,
   available,
   maxQuantity,
   marketingText = '',
   style,
+  additionalInfo,
+  ticketSpecial,
 }: TProps) => {
   const totalQuantity = useAppSelector(selectTotalQuantity);
   const isMembership = useAppSelector(selectHasMembership);
   const numberOfTicketsWithoutChildrenAndToddlers = useAppSelector(
     selectNumberOfTicketsWithoutChildrenAndToddlers,
   );
-  const selectedTimeSlot = useAppSelector(selectFirstTimeSlot);
-  const flow = useAppSelector(selectFlow);
-
-  const sanityAddons = useSanityAddons();
 
   const initialQuantity =
     addOn.statGroup === StatGroup.CHAMPAGNE_TOAST
       ? numberOfTicketsWithoutChildrenAndToddlers
       : totalQuantity;
 
-  const ticketSpecial = TICKET_SPECIALS[addOn.statGroup];
-  const currentHour = selectedTimeSlot?.startRaw.hours || 0;
-  const ticketOnSpecial =
-    ticketSpecial &&
-    ticketSpecial.onSpecial &&
-    (!ticketSpecial.hoursActive?.length || // if no hours specified, special is active all day
-      (ticketSpecial.hoursActive[0] <= currentHour && // check start hour
-        (!ticketSpecial.hoursActive[1] || // if no end hour, special is active after start hour
-          ticketSpecial.hoursActive[1] >= currentHour))); // check end hour if it exists
-
-  const sanityData = addOn.ak ? sanityAddons[addOn.ak] : null;
-
-  const marketingCopy =
-    sanityData?.description ||
-    (addOnMarketingText.byFlow[flow]?.[addOn.statGroup]?.copy ??
-      addOnMarketingText.byStatGroup[addOn.statGroup]?.copy ??
-      '');
-
   return (
     <Flex sx={{ flexDirection: 'column', ...style }}>
       {isMembership ? (
@@ -218,29 +112,20 @@ export const AddOn = ({
         addOn &&
         addOn.statGroup && (
           <AddOnLineItem
-            additionalInfo={
-              sanityData?.additionalLabel
-                ? {
-                    additionalLabel: sanityData?.additionalLabel,
-                    additionalText: sanityData?.additionalText ? (
-                      <SanityRichText blocks={sanityData.additionalText} />
-                    ) : undefined,
-                  }
-                : {}
-            }
+            additionalInfo={additionalInfo}
             ak={addOn.ak}
-            description={marketingCopy}
-            ga4ItemName={addOn.name || sanityData?.name}
-            image={sanityData?.image || IMAGES[addOn.statGroup] || defaultImage}
+            description={marketingText}
+            ga4ItemName={addOn.name}
+            image={IMAGES[addOn.statGroup] || defaultImage}
             initialQuantity={initialQuantity}
             key={addOn.ak}
             marketingText={marketingText}
             maxQuantity={maxQuantity ?? totalQuantity}
-            name={sanityData?.name || addOn.name}
+            name={addOn.name}
             price={addOn.price}
             soldOut={available === 0}
             statGroup={addOn.statGroup}
-            ticketSpecial={ticketOnSpecial ? ticketSpecial : undefined}
+            ticketSpecial={ticketSpecial}
           />
         )
       )}
diff --git a/packages/rockefellercenter/src/components/BuyTickets/Modals/Cart/Cart.tsx b/packages/rockefellercenter/src/components/BuyTickets/Modals/Cart/Cart.tsx
index 689014819..ad590c041 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/Modals/Cart/Cart.tsx
+++ b/packages/rockefellercenter/src/components/BuyTickets/Modals/Cart/Cart.tsx
@@ -1,7 +1,6 @@
 /** @jsxImportSource theme-ui @jsxRuntime classic */
 import { Text, Flex } from '@tishman/components';
 import React, { useEffect, useMemo, useState } from 'react';
-import { getWindow } from '@hzdg/dom-utils';
 
 import {
   actions,
@@ -31,7 +30,6 @@ interface BuyTicketsModal {
 }
 
 export const CartModal = ({ closeModal }: BuyTicketsModal) => {
-  const window = getWindow();
   const [isInitialLoad, setIsInitialLoad] = useState(true);
   const translations = useTranslations<CartContentTranslationsForLocale>(
     Translatable.CartContent,
diff --git a/packages/rockefellercenter/src/components/BuyTickets/PrintTicket/PrintSummary.tsx b/packages/rockefellercenter/src/components/BuyTickets/PrintTicket/PrintSummary.tsx
index e9b46f5b3..6919bb2f7 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/PrintTicket/PrintSummary.tsx
+++ b/packages/rockefellercenter/src/components/BuyTickets/PrintTicket/PrintSummary.tsx
@@ -127,8 +127,6 @@ export const PrintSummary = ({
     return performanceTimes;
   }, [dateTime, order, ticket]);
 
-  const isJupiter = ticket.item.PRODUCT.NAME.includes('Jupiter');
-
   const getTicketName = (name: string) => {
     if (name === 'Season Member') return 'Season Pass';
     if (name === 'Bar Cafe Item') return 'Champagne Toast';
@@ -201,7 +199,7 @@ export const PrintSummary = ({
               <Text>{ticketDateTime?.[0]?.date}</Text>
             </Box>
           )}
-          {!ticket.dateOnly && ticketDateTime?.[0]?.time && !isJupiter && (
+          {!ticket.dateOnly && ticketDateTime?.[0]?.time && (
             <Box>
               <Box sx={{ height: 40 }}>
                 <ThemeProvider theme={getThemeByName('Top of the Rock Yellow')}>
diff --git a/packages/rockefellercenter/src/components/BuyTickets/SegmentedTimePicker/Availabilities/utils.ts b/packages/rockefellercenter/src/components/BuyTickets/SegmentedTimePicker/Availabilities/utils.ts
index 6981ca666..62ff057ea 100644
--- a/packages/rockefellercenter/src/components/BuyTickets/SegmentedTimePicker/Availabilities/utils.ts
+++ b/packages/rockefellercenter/src/components/BuyTickets/SegmentedTimePicker/Availabilities/utils.ts
@@ -104,7 +104,7 @@ type DiscountedData = {
 };
 
 type SpecialEventPerformances = {
-  ID: string;
+  ID?: string;
   AK: string;
   REACHABLE: string;
   TRANSFERRABLE: string;
diff --git a/packages/rockefellercenter/src/components/Cart/Summary.tsx b/packages/rockefellercenter/src/components/Cart/Summary.tsx
index 332f66895..8609c75f7 100644
--- a/packages/rockefellercenter/src/components/Cart/Summary.tsx
+++ b/packages/rockefellercenter/src/components/Cart/Summary.tsx
@@ -204,7 +204,7 @@ export const CartSummary = ({ totals, sx }: Props) => {
                 {pluralize('Item', selectionsCount)})
               </Text>
               {totals?.total && !isLoading ? (
-                <Text>
+                <Text data-testid="subtotal-amount">
                   {totals?.inclusiveTax &&
                   parseFloat(totals.inclusiveTax) > 0 &&
                   totals.subtotal
@@ -226,7 +226,7 @@ export const CartSummary = ({ totals, sx }: Props) => {
                 {pluralize('Tax', 5)}
               </Text>
               {totals?.totalTax && !isLoading ? (
-                <Text>
+                <Text data-testid="total-tax-amount">
                   {totals?.inclusiveTax && parseFloat(totals.inclusiveTax) > 0
                     ? formatMoney(
                         parseFloat(totals.totalTax) -
@@ -257,7 +257,9 @@ export const CartSummary = ({ totals, sx }: Props) => {
                   {translations.summary.total}
                 </Text>
                 {totals.totalWithTax ? (
-                  <Text>{formatMoney(totals.totalWithTax)}</Text>
+                  <Text data-testid="total-with-tax-amount">
+                    {formatMoney(totals.totalWithTax)}
+                  </Text>
                 ) : null}
               </Flex>
               {(parseInt(totals.totalDiscount ?? '0') ?? 0) > 0 && (
@@ -270,7 +272,7 @@ export const CartSummary = ({ totals, sx }: Props) => {
                     {translations.summary.promoCode}
                   </Text>
                   {totals.totalDiscount ? (
-                    <Text>
+                    <Text data-testid="total-discount-amount">
                       {formatMoney(totals.totalDiscount, undefined, true)}
                     </Text>
                   ) : null}
@@ -294,6 +296,7 @@ export const CartSummary = ({ totals, sx }: Props) => {
               </Text>
               {totals.totalWithTax ? (
                 <Text
+                  data-testid="total-with-tax-amount"
                   sx={{
                     fontWeight: 500,
                   }}
diff --git a/packages/rockefellercenter/src/components/Cart/TotalLine.tsx b/packages/rockefellercenter/src/components/Cart/TotalLine.tsx
index 9f1c4f815..788482bed 100644
--- a/packages/rockefellercenter/src/components/Cart/TotalLine.tsx
+++ b/packages/rockefellercenter/src/components/Cart/TotalLine.tsx
@@ -37,6 +37,7 @@ export const CartTotalLine = ({ totals, sx }: Props) => {
         </Text>
         {totals.total ? (
           <Text
+            data-testid="total-line-amount"
             sx={{
               fontWeight: 500,
             }}
diff --git a/packages/rockefellercenter/src/components/Popups/July4thPopup.tsx b/packages/rockefellercenter/src/components/Popups/July4thPopup.tsx
index 3555208ec..6ef8cbadc 100644
--- a/packages/rockefellercenter/src/components/Popups/July4thPopup.tsx
+++ b/packages/rockefellercenter/src/components/Popups/July4thPopup.tsx
@@ -26,7 +26,6 @@ export const July4thPopup = () => {
         gap: '18px',
         padding: '16px 12px',
         my: 2,
-        maxWidth: '422px',
         width: '100%',
         minHeight: '156px',
       }}
diff --git a/packages/rockefellercenter/src/components/price-to-labels-map.tsx b/packages/rockefellercenter/src/components/price-to-labels-map.tsx
index 612159cd1..e7774966e 100644
--- a/packages/rockefellercenter/src/components/price-to-labels-map.tsx
+++ b/packages/rockefellercenter/src/components/price-to-labels-map.tsx
@@ -99,11 +99,11 @@ export const PriceToLabelsMap = ({
       </Flex>
 
       {prices.map((price, index) => (
-        <Box key={index}>
-          <Text as="span" sx={{ ...boldTextStyles }}>
+        <Box data-testid="price-container" key={index}>
+          <Text as="span" data-testid="price-value" sx={{ ...boldTextStyles }}>
             {formatMoney(price.value).replace('US$', '$')}
           </Text>
-          <Text as="span" sx={{ ...textStyles }}>
+          <Text as="span" data-testid="price-label" sx={{ ...textStyles }}>
             {price.label}
           </Text>
         </Box>
diff --git a/packages/rockefellercenter/src/data/hotel-list.json b/packages/rockefellercenter/src/data/hotel-list.json
new file mode 100644
index 000000000..bbec64900
--- /dev/null
+++ b/packages/rockefellercenter/src/data/hotel-list.json
@@ -0,0 +1,75 @@
+{
+  "baccarat_hotel": "Baccarat Hotel",
+  "the_peninsula": "The Peninsula",
+  "the_st_regis_ny": "The St Regis NY",
+  "the_plaza_new_york": "The Plaza New York",
+  "the_mark": "The Mark",
+  "the_waldorf_astoria": "The Waldorf Astoria",
+  "jw_marriott_essex_house": "JW Marriott Essex House",
+  "lotte_new_york_palace": "Lotte New York Palace",
+  "omni_berkshire": "Omni Berkshire",
+  "four_seasons_ny": "Four Seasons NY",
+  "park_central_hotel": "Park Central Hotel",
+  "new_york_hilton_midtown": "New York Hilton Midtown",
+  "riu_plaza_manhattan": "Riu Plaza Manhattan",
+  "hard_rock_hotel_ny": "Hard Rock Hotel NY",
+  "ny_marriot_marquis": "NY Marriot Marquis",
+  "hilton_new_york": "Hilton New York",
+  "intercontinental_new_york_times_square": "Intercontinental (New York Times Square)",
+  "le_meridien_central_park_new_york": "Le Meridien Central Park New York",
+  "loews_regency_new_york_hotel": "Loews Regency New York Hotel",
+  "mandarin_oriental": "Mandarin Oriental",
+  "park_hyatt_new_york": "Park Hyatt New York",
+  "the_jewel_hotel": "The Jewel Hotel",
+  "the_ritz_carlton_new_york": "The Ritz Carlton New York",
+  "the_ritz_carlton_central_park": "The Ritz-Carlton Central Park",
+  "the_whitby_hotel": "The Whitby Hotel",
+  "1_hotel_central_park": "1 Hotel Central Park",
+  "aliz_hotel": "Aliz Hotel",
+  "andaz_5th_avenue": "Andaz 5th Avenue",
+  "arlo_midtown": "Arlo Midtown",
+  "conrad_hotel": "Conrad Hotel",
+  "equinox_hotels": "Equinox Hotels",
+  "hilton_tempo": "Hilton Tempo",
+  "hotel_mela": "Hotel Mela",
+  "margaritaville": "Margaritaville",
+  "park_lane_new_york": "Park Lane New York",
+  "sofitel_ny": "Sofitel NY",
+  "the_carlyle_hotel": "The Carlyle Hotel",
+  "the_edition": "The Edition",
+  "the_fifth_avenue_hotel_lhw": "The Fifth Avenue Hotel (LHW)",
+  "the_langham_new_york_fifth_avenue": "The Langham, New York, Fifth Avenue",
+  "the_muse_new_york": "The Muse New York",
+  "the_surrey": "The Surrey",
+  "the_wallace": "The Wallace",
+  "the_westin_grand_central": "The Westin Grand Central",
+  "thompson_central_park": "Thompson Central Park",
+  "trump_hotels": "Trump Hotels",
+  "the_pierre": "The Pierre",
+  "aman": "Aman",
+  "bryant_park_hotel": "Bryant Park Hotel",
+  "club_quarters_hotel_world_trade_center": "Club Quarters Hotel World Trade Center",
+  "crosby_street_ny": "Crosby Street NY",
+  "embassy_suites": "Embassy Suites",
+  "fairfield_inn_and_suites_ny_manhattantimes_square": "Fairfield Inn & Suites NY Manhattan/Times Square",
+  "fouquets": "Fouquets",
+  "hotel_edison": "Hotel Edison",
+  "oyo_times_square": "OYO Times Square",
+  "park_terrace_hotel": "Park Terrace Hotel",
+  "renaissance_new_york_times_square": "Renaissance New York Times Square",
+  "royalton_park_avenue": "Royalton Park Avenue",
+  "sonesta_international_hotels": "Sonesta International Hotels",
+  "the_beekman_a_thompson_hotel": "The Beekman (A Thompson Hotel)",
+  "the_chatwal": "The Chatwal",
+  "the_dylan_hotel": "The Dylan Hotel",
+  "the_ritz_carlton_nomad": "The Ritz-Carlton Nomad",
+  "hotel_eventi": "Hotel Eventi",
+  "lexington_hotel_the_autograph_collection": "Lexington Hotel, The - Autograph Collection",
+  "park_west_hotel": "Park West Hotel",
+  "radio_city_apartments": "Radio City Apartments",
+  "soho_grand_hotel": "Soho Grand Hotel",
+  "the_dominick": "The Dominick",
+  "the_ned_nomad": "The Ned Nomad",
+  "the_roxy": "The Roxy",
+  "moxy_williamsburg": "Moxy Williamsburg"
+}
diff --git a/packages/rockefellercenter/src/layouts/BuyTicketsLayout.tsx b/packages/rockefellercenter/src/layouts/BuyTicketsLayout.tsx
index 4bcf8cbdd..1f9fbe149 100644
--- a/packages/rockefellercenter/src/layouts/BuyTicketsLayout.tsx
+++ b/packages/rockefellercenter/src/layouts/BuyTicketsLayout.tsx
@@ -65,6 +65,9 @@ import {
   useAppSelector,
   selectWizardDatePicker,
   selectEvent,
+  selectPerformancesForFlow,
+  selectShopCartAddOnSelections,
+  selectShopCartDateTime,
   selectWizardCurrentStep,
 } from '../store';
 import { useGetEditQueryParams } from '../utils/use-ticket-edit-mode';
@@ -82,7 +85,10 @@ import type {
 } from '../services/viva/types';
 import type { ComponentPropsWithoutRef } from 'react';
 import type { TishmanThemeName } from '@tishman/components';
-import type { BuyTicketsVariantPageData } from '../components/BuyTickets';
+import type {
+  AddOnItem,
+  BuyTicketsVariantPageData,
+} from '../components/BuyTickets';
 import type { TicketTypesForFlow } from '../data/translations/ticketType';
 import type { ProductTicketType } from '../services/viva/types/response';
 import type { Availability } from '../components/BuyTickets/SegmentedTimePicker/Availabilities';
@@ -165,10 +171,15 @@ const BuyTicketsLayoutProvider = ({
   // useTieredPricing();
 
   const shopCartDate = useAppSelector(selectShopCartDate);
+  const shopCartDateTime = useAppSelector(selectShopCartDateTime);
   const orderBosEvent = useAppSelector(selectEvent);
   const wizardDatePicker = useAppSelector(selectWizardDatePicker);
   const stepNumber = useAppSelector(selectWizardCurrentStep);
   const selectedPerformances = useAppSelector(selectPerformances);
+  const selectedPerformancesForFlow = useAppSelector(selectPerformancesForFlow);
+  const currentShopCartAddOnSelections = useAppSelector(
+    selectShopCartAddOnSelections,
+  );
   const comboIndex = useAppSelector((state) => state.order.combo.comboIndex);
   const products = useFlowProductsQuery();
   const checkBasketMutation = useCheckBasketMutation(flow);
@@ -415,11 +426,38 @@ const BuyTicketsLayoutProvider = ({
     return () => window.removeEventListener('beforeunload', callback);
   }, []);
 
+  // useEffect(() => {
+  //   if (DYNAMIC_PRICING_FLOWS.includes(flow)) {
+  //     getUpdatedTickets();
+  //   }
+  // }, [flow, selectedPerformances]);
+
   useEffect(() => {
     if (DYNAMIC_PRICING_FLOWS.includes(flow)) {
       getUpdatedTickets();
     }
-  }, [flow, selectedPerformances]);
+
+    // If we change the selected performance then we need to set add-on quantities
+    // to 0 for add-ons in the shopCart AddOnSelections.
+    // This helps protect when we choose a performance that has different availability
+    // for the add-ons.
+    const updatedAddOns = currentShopCartAddOnSelections.map((addon) => {
+      const addOnItemDTO: AddOnItem = {
+        ...addon,
+        quantity: 0,
+      };
+      return addOnItemDTO;
+    });
+
+    dispatch(actions.order.setShopCartAddOnSelections(updatedAddOns));
+    dispatch(
+      actions.checkout.setAddOns({
+        addOns: updatedAddOns,
+        dateTime: shopCartDateTime,
+        flow,
+      }),
+    );
+  }, [flow, selectedPerformancesForFlow]);
 
   useEffect(() => {
     setIsAddOnLoading(isCompAddOnLoading);
@@ -556,7 +594,7 @@ const BuyTicketsLayoutProvider = ({
       );
     });
 
-    // If NO availability and we still have retries, continue cycling
+    // If NO availability (either no data returned or no available dates) and we still have retries, continue cycling
     if (!hasAvailability && shouldFetchRetries > 0) {
       // First check if we need to increment the year
       const nextMonth = (wizardDatePicker.month + 1) % 12;
@@ -575,6 +613,7 @@ const BuyTicketsLayoutProvider = ({
     }
   }, [
     wizardDatePicker.month,
+    wizardDatePicker.year,
     getDaysAvailabilityQuery.data,
     getDaysAvailabilityQuery.isSuccess,
     getDaysAvailabilityQuery.isLoading,
@@ -675,7 +714,6 @@ const BuyTicketsLayoutProvider = ({
     getDaysAvailabilityQuery.isSuccess,
     getDaysAvailabilityQuery.data,
     isInitialLoad,
-    shouldFetchRetries,
     isInitialFutureMonth,
     isFirstAvailableDateSet,
   ]);
diff --git a/packages/rockefellercenter/src/pages/buy-tickets/index.tsx b/packages/rockefellercenter/src/pages/buy-tickets/index.tsx
index d465ca495..b647d6311 100644
--- a/packages/rockefellercenter/src/pages/buy-tickets/index.tsx
+++ b/packages/rockefellercenter/src/pages/buy-tickets/index.tsx
@@ -1,13 +1,15 @@
 /** @jsxImportSource theme-ui @jsxRuntime classic */
 import { graphql } from 'gatsby';
-// import { useLocation } from '@reach/router';
+import { useLocation } from '@reach/router';
 import React from 'react';
+import { Box, Flex, Text } from '@tishman/components';
 
 import { BuyTicketsApp } from '../../components/BuyTickets/BuyTicketsApp';
 import { Layout } from '../../layouts';
 // import { useNetlifyBotCheck } from '../../utils';
 import { Oljs } from '../../components/Oljs';
 import { usePromoCode } from '../../utils/use-promo-code-param';
+import hotelList from '../../data/hotel-list.json';
 
 import type { PageConfig } from '../../PageConfig';
 
@@ -17,6 +19,20 @@ export const pageConfig: PageConfig = {
 };
 
 export default function BuyTickets(): React.JSX.Element {
+  const location = useLocation();
+  const searchParams = new URLSearchParams(location.search);
+  const utmCampaign = searchParams.get('utm_campaign');
+  const utmSource = searchParams.get('utm_source');
+  const HOTEL_UTM = 'hotels';
+
+  const showWelcomeMessage =
+    utmCampaign &&
+    utmSource === HOTEL_UTM &&
+    hotelList[utmCampaign as keyof typeof hotelList];
+  const hotelName = showWelcomeMessage
+    ? hotelList[utmCampaign as keyof typeof hotelList]
+    : '';
+
   // Serve bots a flattened version of the page
   // const location = useLocation();
   // useNetlifyBotCheck(location.href);
@@ -26,6 +42,29 @@ export default function BuyTickets(): React.JSX.Element {
 
   return (
     <Layout sx={{ marginTop: [0, 5] }}>
+      {showWelcomeMessage && (
+        <Flex sx={{ textAlign: 'center', mx: [2, 4], px: [2, 4] }}>
+          <Flex sx={{ flex: ['0', '0 0 15%'] }} />
+          <Box>
+            <Text sx={{ fontSize: [4, 6], textAlign: 'left', marginBottom: 1 }}>
+              Welcome,
+            </Text>
+            <Text
+              sx={{
+                fontSize: [5, 7],
+                textAlign: 'center',
+                flex: 1,
+                marginBottom: 6,
+                paddingBottom: 1,
+                borderBottom: '3px double',
+              }}
+              variant="h2"
+            >
+              {hotelName}
+            </Text>
+          </Box>
+        </Flex>
+      )}
       <Oljs />
       <BuyTicketsApp />
     </Layout>
diff --git a/packages/rockefellercenter/src/services/viva/constants/index.ts b/packages/rockefellercenter/src/services/viva/constants/index.ts
index d53a2702d..2124309c6 100644
--- a/packages/rockefellercenter/src/services/viva/constants/index.ts
+++ b/packages/rockefellercenter/src/services/viva/constants/index.ts
@@ -844,10 +844,10 @@ export const TimeConstraints: {
     startTime: { hours: 11, minutes: '45' },
     endTime: { hours: 20, minutes: '00' },
   },
-  [StatGroup.TOR_VIP]: {
-    startTime: { hours: 11, minutes: '00' },
-    endTime: { hours: 21, minutes: '44' },
-  },
+  // [StatGroup.TOR_VIP]: {
+  //   startTime: { hours: 11, minutes: '00' },
+  //   endTime: { hours: 21, minutes: '44' },
+  // },
   [StatGroup.TOR_VIP_ROCKSTAR]: {
     startTime: { hours: 10, minutes: '30' },
     endTime: { hours: 20, minutes: '44' },
@@ -1163,6 +1163,8 @@ export const ADDONS_THAT_DONT_SHOW_TIME = [
   ORNAMENT_AK,
   SKYLIFT_AK,
   BEAM_AK,
+  FIVE_ACRES_AK,
+  RESTAURANT_JUPITER_AK,
 ];
 
 export const ADDITIONAL_EXPRESS_PASS_ADDONS_THAT_DONT_SHOW_TIME = [
diff --git a/packages/rockefellercenter/src/store/selectors/order.ts b/packages/rockefellercenter/src/store/selectors/order.ts
index 5eb47d138..856f03409 100644
--- a/packages/rockefellercenter/src/store/selectors/order.ts
+++ b/packages/rockefellercenter/src/store/selectors/order.ts
@@ -68,6 +68,12 @@ export const selectPerformances = createSelector(
   (shopCart) => shopCart.performances,
 );
 
+export const selectPerformancesForFlow = createSelector(
+  selectShopCart,
+  selectFlow,
+  (shopcart, flow) => shopcart.performances[flow],
+);
+
 export const selectPerformancesFlat = createSelector(
   selectPerformances,
   (performances) => Object.values(performances),
diff --git a/packages/rockefellercenter/src/store/slices/checkout.ts b/packages/rockefellercenter/src/store/slices/checkout.ts
index 10e1f2f69..dd58082d6 100644
--- a/packages/rockefellercenter/src/store/slices/checkout.ts
+++ b/packages/rockefellercenter/src/store/slices/checkout.ts
@@ -10,7 +10,6 @@ import {
   removeObjectForLocalStorageKey,
 } from '../utils';
 import {
-  addOnUpdateDTO,
   removeByTypeAndDateTimeDTO,
   clearDateFromCheckoutDTO,
   setFlowSelectionDTO,
@@ -137,12 +136,12 @@ export const checkoutSlice = createSlice({
         flow: Flow;
       }>,
     ) => {
-      const update = addOnUpdateDTO({
-        ...action.payload,
-        data: state.cart.addOnSelections,
-      });
-
-      state.cart.addOnSelections = update;
+      const validAddOns = action.payload.addOns.filter(
+        (addOn) => addOn?.quantity && addOn.quantity > 0,
+      );
+      state.cart.addOnSelections[action.payload.flow] = {
+        [action.payload.dateTime]: validAddOns,
+      };
 
       return state;
     },
diff --git a/packages/rockefellercenter/src/store/slices/order.ts b/packages/rockefellercenter/src/store/slices/order.ts
index e45b0456c..c8fa87103 100644
--- a/packages/rockefellercenter/src/store/slices/order.ts
+++ b/packages/rockefellercenter/src/store/slices/order.ts
@@ -1,12 +1,5 @@
 import { createSlice } from '@reduxjs/toolkit';
-// import moment from 'moment-timezone';
-import {
-  tzDate,
-  format as tempoFormat,
-  addDay,
-  addMonth,
-  addYear,
-} from '@formkit/tempo';
+import { tzDate } from '@formkit/tempo';
 
 import {
   Flow,
@@ -245,18 +238,39 @@ export const orderSlice = createSlice({
       if (state.shopCart.addOnSelections.length === 0) {
         state.shopCart.addOnSelections = action.payload;
       } else {
-        const selectionIndex = state.shopCart.addOnSelections.findIndex(
-          (s) => s.ak === action.payload[0].ak,
+        if (action.payload.length === 0) {
+          return state;
+        }
+
+        // Create a map of existing add-ons by ak for quick lookup
+        const existingAddOnsMap = new Map(
+          state.shopCart.addOnSelections.map((addOn) => [addOn.ak, addOn]),
         );
 
-        if (selectionIndex !== -1) {
-          state.shopCart.addOnSelections[selectionIndex] = {
-            ...state.shopCart.addOnSelections[selectionIndex],
-            ...action.payload[0],
-          };
-        } else {
-          state.shopCart.addOnSelections.push(action.payload[0]);
-        }
+        // Process each add-on in the payload
+        action.payload.forEach((newAddOn) => {
+          const existingAddOn = existingAddOnsMap.get(newAddOn.ak);
+
+          if (existingAddOn) {
+            // If it exists, update it while preserving any existing properties
+            existingAddOnsMap.set(newAddOn.ak, {
+              ...existingAddOn,
+              ...newAddOn,
+              // Preserve the higher quantity if both have quantities
+              quantity: newAddOn.quantity ?? 0,
+              // Preserve existing performances if new one doesn't have any
+              performances: newAddOn.performances?.length
+                ? newAddOn.performances
+                : existingAddOn.performances,
+            });
+          } else {
+            // If it doesn't exist, add it
+            existingAddOnsMap.set(newAddOn.ak, newAddOn);
+          }
+        });
+
+        // Convert map back to array
+        state.shopCart.addOnSelections = Array.from(existingAddOnsMap.values());
       }
       return state;
     },
diff --git a/packages/rockefellercenter/src/store/slices/wizard.ts b/packages/rockefellercenter/src/store/slices/wizard.ts
index cd6c19133..e5732f45b 100644
--- a/packages/rockefellercenter/src/store/slices/wizard.ts
+++ b/packages/rockefellercenter/src/store/slices/wizard.ts
@@ -91,7 +91,7 @@ const initialState: WizardState = {
     firstOfMonth: formatShortDate(new Date(year, month - 1, 1)),
     lastOfMonth: formatShortDate(new Date(year, month, 0)),
     totalDays: new Date(year, month, 0).getDate(),
-    future: [],
+    future: {},
   },
   datePicker: {
     day: 1,
diff --git a/packages/rockefellercenter/src/utils/use-automatic-comp-addon.ts b/packages/rockefellercenter/src/utils/use-automatic-comp-addon.ts
index 296b176b8..b4a69296a 100644
--- a/packages/rockefellercenter/src/utils/use-automatic-comp-addon.ts
+++ b/packages/rockefellercenter/src/utils/use-automatic-comp-addon.ts
@@ -8,6 +8,7 @@ import {
   AddOnAKForStatGroup,
   AddOnStatGroupsByFlowFlat,
   BosEvent,
+  COMP_TICKETS,
   Flow,
   StatGroup,
 } from '../services/viva/constants';
@@ -63,7 +64,7 @@ const useAutomaticCompAddon = () => {
       .map((_addOn) => ({
         ak: _addOn?.AK,
         description: _addOn?.DESCRIPTION ?? '',
-        price: parseFloat(_addOn?.PRICE.GROSS) ?? 0,
+        price: parseFloat(_addOn?.PRICE.GROSS) * 100 ?? 0,
         marketingText: _addOn?.ADDITIONALDESC ?? '',
         quantity: shopCartSelections.reduce(
           (sum, selection) => sum + selection.count,
@@ -144,14 +145,13 @@ const useAutomaticCompAddon = () => {
             };
           });
 
-          // Update shop cart add-on selections
-          dispatch(actions.order.setShopCartAddOnSelections(updatedAddOns));
           const updatedAddOnsWithCount = updatedAddOns.map((addOn) => ({
             ...addOn,
-            quantity: totalQuantity as number,
+            quantity: COMP_TICKETS.includes(addOn.statGroup)
+              ? (totalQuantity as number)
+              : 0,
           }));
 
-          // Update checkout add-ons
           dispatch(
             actions.checkout.setAddOns({
               addOns: updatedAddOnsWithCount,
